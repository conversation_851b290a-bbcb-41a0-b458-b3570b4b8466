# === Python cache ===
__pycache__/
*.py[cod]
*.so
*.pyd
*.pyo

# === Bytecode ===
*.pyc
*.pyo
*.pyd
*.pdb

# === Virtual environments ===
.venv/
venv/
env/
ENV/
env.bak/
env_old/
*.egg-info/
.eggs/

# === Jupyter + notebooks ===
.ipynb_checkpoints/
*.ipynb

# === PyTorch + Checkpoints ===
*.pt
*.pth
*.ckpt
*.onnx

# === Model assets ===
assets/checkpoints/
visualizations/
results/
logs/
runs/
*.log

# === Hydra outputs ===
outputs/
.multirun/
outputs_multirun/
outputs_single_run/

# === Debug or crash dumps ===
*.log
*.tmp
*.bak
*.swp
*.DS_Store
Thumbs.db

# === VSCode ===
.vscode/
.history/

# === PyCharm ===
.idea/

# === Environment ===
environment.yml
.env
*.env

# === Testing ===
.pytest_cache/
.coverage
htmlcov/
.tox/

# === Build artifacts ===
build/
dist/
*.egg
*.whl

# === Packaging ===
MANIFEST
*.manifest
*.spec

# === Misc ===
dev/__pycache__/
