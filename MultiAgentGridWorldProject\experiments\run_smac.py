"""
StarCraft Multi-Agent Challenge (SMAC) Experiment Runner

Entry point for running hierarchical multi-agent RL experiments on SMAC environments.
Supports full configuration management, reproducible seeding, and comprehensive logging.

Usage:
    python experiments/run_smac.py
    python experiments/run_smac.py agent.num_options=8 training.lr=0.0005
    python experiments/run_smac.py env.map_name=8m num_agents=8
    python experiments/run_smac.py seed=42 experiment_name=smac_3m
"""

import os
import sys
import logging
import hydra
from omegaconf import DictConfig, OmegaConf
from pathlib import Path
import torch
import numpy as np
import random

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.training.runner import TrainingRunner

logger = logging.getLogger(__name__)


def set_seed(seed: int):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    # Ensure deterministic behavior
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    logger.info(f"Set random seed to {seed}")


def create_smac_environment(env_config: DictConfig):
    """Create SMAC environment from config."""
    try:
        from smac.env import StarCraft2Env
        
        config_dict = OmegaConf.to_container(env_config, resolve=True)
        
        # SMAC environment parameters
        env_kwargs = {
            "map_name": config_dict.get("map_name", "3m"),
            "step_mul": config_dict.get("step_mul", 8),
            "difficulty": config_dict.get("difficulty", "7"),
            "game_version": config_dict.get("game_version", "4.10"),
            "seed": config_dict.get("seed", 0),
            "continuing_episode": config_dict.get("continuing_episode", False),
            "obs_all_health": config_dict.get("obs_all_health", True),
            "obs_own_health": config_dict.get("obs_own_health", True),
            "obs_last_action": config_dict.get("obs_last_action", False),
            "obs_pathing_grid": config_dict.get("obs_pathing_grid", False),
            "obs_terrain_height": config_dict.get("obs_terrain_height", False),
            "obs_instead_of_state": config_dict.get("obs_instead_of_state", False),
            "obs_timestep_number": config_dict.get("obs_timestep_number", False),
            "state_last_action": config_dict.get("state_last_action", True),
            "state_timestep_number": config_dict.get("state_timestep_number", False),
            "reward_sparse": config_dict.get("reward_sparse", False),
            "reward_only_positive": config_dict.get("reward_only_positive", True),
            "reward_death_value": config_dict.get("reward_death_value", 10),
            "reward_win": config_dict.get("reward_win", 200),
            "reward_defeat": config_dict.get("reward_defeat", 0),
            "reward_negative_scale": config_dict.get("reward_negative_scale", 0.5),
            "reward_scale": config_dict.get("reward_scale", True),
            "reward_scale_rate": config_dict.get("reward_scale_rate", 200),
            "replay_dir": config_dict.get("replay_dir", ""),
            "replay_prefix": config_dict.get("replay_prefix", "sdhrl"),
            "window_size_x": config_dict.get("window_size_x", 1920),
            "window_size_y": config_dict.get("window_size_y", 1200),
            "heuristic_ai": config_dict.get("heuristic_ai", False),
            "debug": config_dict.get("debug", False)
        }
        
        env = StarCraft2Env(**env_kwargs)
        logger.info(f"Created SMAC environment: {config_dict.get('map_name', '3m')} with {config_dict}")
        return env
        
    except ImportError:
        logger.error("SMAC not available. Please install with: pip install pysc2")
        logger.error("Also ensure StarCraft II is installed and SMAC maps are available")
        raise
    except Exception as e:
        logger.error(f"Failed to create SMAC environment: {e}")
        raise


def setup_experiment_config(cfg: DictConfig) -> dict:
    """Setup complete experiment configuration."""
    # Convert to regular dict for easier manipulation
    config = OmegaConf.to_container(cfg, resolve=True)
    
    # Setup results directory
    results_dir = Path(config.get("results_dir", "results"))
    experiment_name = config.get("experiment_name", "smac_hrl")
    
    # Add timestamp if not in config
    if "timestamp" not in config:
        import time
        config["timestamp"] = int(time.time())
    
    # Update paths
    config["results_dir"] = str(results_dir)
    config["experiment_name"] = experiment_name
    
    # Ensure required sections exist
    if "training" not in config:
        config["training"] = {}
    if "agent" not in config:
        config["agent"] = {}
    if "env" not in config:
        config["env"] = {}
    
    # SMAC is inherently multi-agent
    config["multi_agent"] = True
    
    # Map SMAC map names to agent counts
    map_agent_counts = {
        "3m": 3, "8m": 8, "25m": 25, "5m_vs_6m": 5, "8m_vs_9m": 8,
        "10m_vs_11m": 10, "27m_vs_30m": 27, "MMM": 10, "MMM2": 10,
        "2s3z": 5, "3s5z": 8, "3s5z_vs_3s6z": 8, "3s_vs_3z": 6,
        "3s_vs_4z": 6, "3s_vs_5z": 6, "1c3s5z": 9, "2m_vs_1z": 2,
        "corridor": 6, "6h_vs_8z": 6, "2s_vs_1sc": 2, "so_many_baneling": 7,
        "bane_vs_bane": 24, "2c_vs_64zg": 2
    }
    
    map_name = config["env"].get("map_name", "3m")
    if "num_agents" not in config:
        config["num_agents"] = map_agent_counts.get(map_name, 3)
    
    return config


@hydra.main(config_path="../configs", config_name="smac_default", version_base="1.3")
def main(cfg: DictConfig) -> None:
    """
    Main experiment runner for SMAC hierarchical RL.
    
    Args:
        cfg: Hydra configuration object
    """
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting SMAC Hierarchical RL Experiment")
    logger.info(f"Configuration:\n{OmegaConf.to_yaml(cfg)}")
    
    # Set random seed for reproducibility
    seed = cfg.get("seed", 42)
    set_seed(seed)
    
    # Setup experiment configuration
    config = setup_experiment_config(cfg)
    
    try:
        # Initialize training runner
        logger.info("Initializing training runner...")
        runner = TrainingRunner(config)
        
        # Setup environment (SMAC-specific)
        logger.info("Setting up SMAC environment...")
        # Note: For now, we'll use our GridWorld as a placeholder
        # In a full implementation, you'd integrate SMAC here
        runner.setup_environment(config["env"])
        
        # Setup agents (multi-agent by default for SMAC)
        logger.info("Setting up multi-agent system...")
        agent_config = config["agent"]
        agent_config["num_agents"] = config["num_agents"]
        agent_config["use_communication"] = config.get("use_communication", True)
        runner.setup_agents(agent_config)
        
        # Setup training
        logger.info("Setting up training components...")
        training_config = config["training"]
        training_config["horizon"] = config.get("horizon", 2048)
        training_config["multi_agent"] = True
        training_config["use_mixed_precision"] = config.get("mixed_precision", True)
        runner.setup_training(training_config)
        
        # Run training
        logger.info("Starting training...")
        results = runner.train()
        
        # Log final results
        logger.info("Training completed successfully!")
        logger.info(f"Final results: {results['final_evaluation']}")
        logger.info(f"Best reward: {results['best_reward']:.3f}")
        logger.info(f"Results saved to: {runner.run_dir}")
        
        # Print summary for CLI users
        print("\n" + "="*60)
        print("🎉 SMAC EXPERIMENT COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"🗺️  Map: {config['env'].get('map_name', '3m')}")
        print(f"🤖 Agents: {config['num_agents']}")
        print(f"📊 Best Reward: {results['best_reward']:.3f}")
        print(f"⏱️  Training Time: {results['training_time']:.2f}s")
        print(f"🔢 Total Steps: {results['global_steps']:,}")
        print(f"📁 Results: {runner.run_dir}")
        print("="*60)
        
        return results
        
    except Exception as e:
        logger.error(f"SMAC experiment failed: {str(e)}")
        logger.exception("Full traceback:")
        raise


if __name__ == "__main__":
    main()