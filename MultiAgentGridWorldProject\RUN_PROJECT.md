# 🚀 Running SD-HRL: Complete Execution Guide

> **Publication-Ready Multi-Agent Framework**  
> *Step-by-Step Guide for Training, Benchmarking, and Real-World Applications*

[![Status](https://img.shields.io/badge/status-publication%20ready-brightgreen)](PROJECT_STATUS.md)
[![Tests](https://img.shields.io/badge/tests-134%2F155%20passing-green)](tests/)
[![Benchmarks](https://img.shields.io/badge/benchmarks-complete-blue)](BENCHMARK_RESULTS.md)

This comprehensive guide provides step-by-step instructions to **reproduce our research results**, **run benchmarks**, and **deploy real-world applications** using the SD-HRL framework.

---

## ⚡ **Quick Start (30 Seconds)**

### **🚀 Instant Demo**
```bash
# Clone and setup
git clone https://github.com/your-username/MultiAgentGridWorldProject.git
cd MultiAgentGridWorldProject
pip install -e .

# Run 30-second demo
python experiments/run_gridworld.py training.num_epochs=2
```

### **📊 Reproduce Paper Results**
```bash
# 1. Train SD-HRL (5 minutes)
python experiments/run_gridworld.py

# 2. Run baseline comparisons (10 minutes)
python benchmarks/baselines.py --algo qmix --env gridworld --num_runs 1
python benchmarks/baselines.py --algo maddpg --env gridworld --num_runs 1
python benchmarks/baselines.py --algo ippo --env gridworld --num_runs 1

# 3. Generate comparison plots (1 minute)
python benchmarks/compare_results.py --metric all

# 4. Run real-world case study (5 minutes)
python benchmarks/realworld_case.py --env cityflow --num_episodes 20
```

### **✅ Verify Installation**
```bash
# Check core functionality
python -c "from src.controllers.hierarchical_agent import HierarchicalAgent; print('✅ SD-HRL ready!')"

# Run test suite
pytest tests/ -x --tb=short
```

---

## 📋 **Complete Benchmark Reproduction**

### **🏆 Academic Benchmark Suite**
```bash
# Complete benchmark reproduction (30 minutes)
./scripts/run_full_benchmarks.sh

# Or run step by step:

# 1. SD-HRL Training
python experiments/run_gridworld.py

# 2. All Baselines
for algo in qmix maddpg ippo; do
    python benchmarks/baselines.py --algo $algo --env gridworld --num_runs 3
done

# 3. Statistical Analysis
python benchmarks/compare_results.py --metric all --save_pdf

# 4. Real-World Applications
for env in cityflow ieee33 warehouse; do
    python benchmarks/realworld_case.py --env $env --num_episodes 50
done
```

### **📊 Expected Results**
After running the complete benchmark suite, you should see:
```
results/
├── plots/
│   ├── reward_vs_communication.png     # SD-HRL: 85% efficiency
│   └── performance_comparison.png      # SD-HRL: 65% success rate
├── baselines/
│   ├── qmix_gridworld_*.yaml          # QMIX: 79.6% efficiency
│   ├── maddpg_gridworld_*.yaml        # MADDPG: 55% efficiency
│   └── ippo_gridworld_*.yaml          # IPPO: 100% efficiency, 42% success
└── realworld/
    ├── cityflow_case_study_*.yaml     # Traffic: 4.09s avg wait
    ├── ieee33_case_study_*.yaml       # Power: 0.0169 p.u. deviation
    └── warehouse_case_study_*.yaml    # Robotics: 2.0% collision rate
```

---

## 🧪 **Research Experiments**

### **🎯 Core SD-HRL Training**

#### **Standard Training (Paper Results)**
```bash
# Default configuration (reproduces paper results)
python experiments/run_gridworld.py

# With custom parameters
python experiments/run_gridworld.py \
    agent.deadlock_threshold=20 \
    env.map_size=[10,10] \
    training.num_epochs=50
```

#### **Ablation Studies**
```bash
# Without communication
python experiments/run_gridworld.py agent.use_communication=false

# Different option frequencies
python experiments/run_gridworld.py agent.option_freq=4
python experiments/run_gridworld.py agent.option_freq=12

# Various deadlock thresholds
python experiments/run_gridworld.py agent.deadlock_threshold=10
python experiments/run_gridworld.py agent.deadlock_threshold=30
```

#### **Scalability Analysis**
```bash
# Different agent counts
for agents in 2 4 6 8; do
    python experiments/run_gridworld.py env.num_agents=$agents
done

# Different environment sizes
for size in 8 12 16; do
    python experiments/run_gridworld.py env.map_size=[$size,$size]
done
```

### **🌍 Real-World Applications**

#### **Traffic Control (CityFlow)**
```bash
# Basic traffic simulation
python benchmarks/realworld_case.py --env cityflow --num_episodes 50

# Custom traffic scenario
python benchmarks/realworld_case.py \
    --env cityflow \
    --config benchmarks/configs/cityflow_default.yaml \
    --num_episodes 100
```

#### **Smart Grid (IEEE-33)**
```bash
# Power system control
python benchmarks/realworld_case.py --env ieee33 --num_episodes 30

# With custom load variation
python benchmarks/realworld_case.py \
    --env ieee33 \
    --config benchmarks/configs/ieee33_default.yaml \
    --num_episodes 50
```

#### **Warehouse Robotics**
```bash
# Multi-robot coordination
python benchmarks/realworld_case.py --env warehouse --num_episodes 40

# Larger warehouse
python benchmarks/realworld_case.py \
    --env warehouse \
    --config benchmarks/configs/warehouse_default.yaml \
    --num_episodes 60
```

---

## 📊 **Baseline Comparisons**

### **🏆 Academic Baselines**

#### **Individual Baseline Runs**
```bash
# QMIX (Value-based with centralized mixing)
python benchmarks/baselines.py --algo qmix --env gridworld --num_runs 3

# MADDPG (Multi-agent actor-critic)
python benchmarks/baselines.py --algo maddpg --env gridworld --num_runs 3

# IPPO (Independent PPO)
python benchmarks/baselines.py --algo ippo --env gridworld --num_runs 3
```

#### **Complete Comparison Suite**
```bash
# Run all baselines with statistical significance
./scripts/run_baselines.sh

# Or manually:
for algo in qmix maddpg ippo; do
    for run in {1..5}; do
        python benchmarks/baselines.py --algo $algo --env gridworld --seed $((42 + run))
    done
done
```

#### **Generate Comparison Analysis**
```bash
# Create all comparison plots and tables
python benchmarks/compare_results.py --metric all

# Specific comparisons
python benchmarks/compare_results.py --metric reward_vs_comm
python benchmarks/compare_results.py --metric performance
python benchmarks/compare_results.py --metric table
```

### **📈 Expected Benchmark Results**

After running the complete baseline comparison, you should see:

| Algorithm | Communication Efficiency | Success Rate | Training Time |
|-----------|-------------------------|--------------|---------------|
| **SD-HRL** | **85.0% ± 3%** | **65.0% ± 7%** | 1,400s |
| QMIX | 79.6% | 100.0% | 1,000s |
| MADDPG | 55.0% | 48.0% | 1,200s |
| IPPO | 100.0% | 42.0% | 800s |

### **🔍 Analysis Tools**
```bash
# Statistical significance testing
python benchmarks/compare_results.py --metric all --statistical_tests

# Generate publication-ready figures
python benchmarks/compare_results.py --metric all --save_pdf --high_dpi

# Export data for external analysis
python benchmarks/compare_results.py --metric table --format csv,excel
```

---

## 🏆 3. Baseline Comparisons

### 🔄 Run All Baselines

#### Complete Benchmark Suite
```bash
python benchmarks/compare_results.py \
    --methods SDHRL,QMIX,MADDPG,IPPO \
    --environments all \
    --trials 5 \
    --output-dir results/benchmarks/
```

#### Specific Baseline Comparison
```bash
python benchmarks/baselines.py \
    --baseline QMIX \
    --environment gridworld \
    --config configs/env_gridworld.yaml \
    --episodes 1000
```

### 🌍 Real-World Case Studies

#### Traffic Control (CityFlow)
```bash
python benchmarks/realworld_case.py \
    --domain traffic \
    --scenario cityflow_1x1 \
    --agents 4 \
    --duration 3600
```

#### Power Grid Management (IEEE-33)
```bash
python benchmarks/realworld_case.py \
    --domain power_grid \
    --scenario ieee_33_bus \
    --agents 10 \
    --load-variation 0.2
```

---

## 🎨 4. Visualization & Analysis

### 📹 Generate Visualizations

#### Create Training GIFs
```bash
python src/utils/visualization.py \
    --mode animate \
    --log-dir logs/gridworld_run_001/ \
    --output visualizations/gridworld_demo.gif \
    --fps 10 \
    --highlight-communication true
```

#### Communication Pattern Analysis
```bash
python src/utils/attention_viz.py \
    --mode attention_heatmap \
    --model-path assets/checkpoints/sdhrl_gridworld.pth \
    --episodes 10 \
    --output visualizations/communication_patterns.png
```

#### Dynamic Attention Over Time
```bash
python src/utils/attention_viz.py \
    --mode attention_timeline \
    --model-path assets/checkpoints/sdhrl_gridworld.pth \
    --episodes 5 \
    --output visualizations/agent_focus_over_time.gif
```

#### Performance Plots
```bash
python src/utils/visualization.py \
    --mode performance_plots \
    --results-dir results/ \
    --metrics reward,success_rate,deadlock_rate \
    --output visualizations/smac_performance.png
```

---

## ⚙️ 5. Hyperparameter Optimization

### 🔍 Automated Hyperparameter Sweep

#### Ray Tune Integration
```bash
python experiments/sweep_hyperparams.py \
    --config configs/training_defaults.yaml \
    --search-space learning_rate,batch_size,attention_threshold \
    --trials 50 \
    --gpus 2
```

#### Optuna Optimization
```bash
python experiments/sweep_hyperparams.py \
    --optimizer optuna \
    --objective task_completion_rate \
    --trials 100 \
    --study-name sdhrl_optimization
```

#### Grid Search
```bash
python experiments/sweep_hyperparams.py \
    --method grid_search \
    --params "learning_rate=[1e-4,3e-4,1e-3],batch_size=[32,64,128]" \
    --environment gridworld
```

---

## 📋 6. Configuration Management

### 🔧 Key Configuration Files

#### Agent Configuration (`configs/agent_hrl_icp.yaml`)
```yaml
agent:
  type: hierarchical
  option_policy:
    hidden_dims: [256, 256]
    activation: relu
    option_duration: 10
  worker_policy:
    hidden_dims: [128, 128]
    activation: tanh
  communication:
    attention_dim: 64
    threshold: 0.3
    max_range: 5
```

#### Environment Configuration (`configs/env_gridworld.yaml`)
```yaml
environment:
  name: dynamic_gridworld
  grid_size: [12, 12]
  num_agents: 6
  num_goals: 3
  obstacle_density: 0.2
  dynamic_obstacles: true
  partial_observability: true
  observation_radius: 3
```

#### Training Configuration (`configs/training_defaults.yaml`)
```yaml
training:
  max_episodes: 10000
  max_timesteps: 2000000
  batch_size: 64
  learning_rate: 3e-4
  gamma: 0.99
  tau: 0.005
  update_frequency: 100
  save_frequency: 1000
  eval_frequency: 500
```

### 🎛️ Command Line Overrides

#### Override Single Parameters
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --override "training.learning_rate=1e-3,environment.num_agents=8"
```

#### Environment-Specific Overrides
```bash
python experiments/run_mpe.py \
    --config configs/env_mpe.yaml \
    --override "environment.scenario=simple_spread,agent.communication.threshold=0.5"
```

---

## 🐛 7. Debugging & Troubleshooting

### 🔍 Debug Mode

#### Enable Detailed Logging
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --debug true \
    --log-level DEBUG \
    --save-trajectories true
```

#### Quick Sanity Check
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --max-episodes 10 \
    --render true \
    --no-save true
```

### 🧪 Unit Testing

#### Run All Tests
```bash
python -m pytest tests/ -v
```

#### Test Specific Components
```bash
python -m pytest tests/test_environment.py::test_gridworld_dynamics -v
python -m pytest tests/test_agents.py::test_hierarchical_agent_action -v
python -m pytest tests/test_comms.py::test_attention_mechanism -v
```

### 📊 Performance Profiling

#### Memory Usage Analysis
```bash
python -m memory_profiler experiments/run_gridworld.py --config configs/sdhrl_default.yaml
```

#### GPU Utilization Monitoring
```bash
nvidia-smi -l 1 & python experiments/run_gridworld.py --config configs/sdhrl_default.yaml --gpus 1
```

---

## 🧹 8. Project Management

### 📁 Directory Structure After Training

```
MultiAgentGridWorldProject/
├── assets/                   # Pretrained models and large files
│   └── checkpoints/
│       ├── sdhrl_gridworld.pth
│       ├── sdhrl_mpe.pth
│       └── sdhrl_smac.pth
├── logs/                     # Training logs and TensorBoard data
│   ├── tensorboard/
│   ├── wandb/
│   ├── training_logs.txt
│   └── experiment_configs/
├── results/                  # Evaluation results and metrics
│   ├── benchmarks/
│   ├── evaluation_metrics.json
│   ├── performance_comparison.csv
│   └── attention_analysis/
├── visualizations/           # Generated plots and animations
│   ├── gridworld_demo.gif
│   ├── communication_patterns.png
│   ├── agent_focus_over_time.gif
│   ├── smac_performance.png
│   └── training_curves.png
└── examples/                 # Quick-start examples
    ├── train_sdhrl_gridworld.py
    ├── eval_sdhrl_starcraft.py
    └── visualize_attention_weights.py
```

### 🗑️ Cleanup Commands

#### Remove All Generated Files
```bash
rm -rf logs/ results/
mkdir -p logs results
```

#### Clean Specific Experiment
```bash
rm -rf logs/gridworld_run_* results/gridworld_*
```

#### Reset to Fresh State (Keep Assets)
```bash
git clean -fdx --exclude=src/ --exclude=configs/ --exclude=assets/ --exclude=examples/ --exclude=*.md
```

#### Complete Reset (Remove Everything Including Pretrained Models)
```bash
rm -rf logs/ results/ assets/checkpoints/*
mkdir -p logs results assets/checkpoints
```

---

## 💡 9. Pro Tips & Best Practices

### 🚀 Performance Optimization

#### Multi-GPU Training
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --gpus 2 \
    --distributed-backend nccl \
    --batch-size 128
```

#### Efficient Hyperparameter Search
```bash
# Use smaller episodes for initial search
python experiments/sweep_hyperparams.py \
    --config configs/training_defaults.yaml \
    --override "training.max_episodes=1000" \
    --trials 20 \
    --early-stopping true
```

### 📊 Experiment Tracking

#### Weights & Biases Integration
```bash
export WANDB_PROJECT="sdhrl-experiments"
export WANDB_ENTITY="your-username"
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml --wandb true
```

#### TensorBoard Monitoring
```bash
tensorboard --logdir logs/tensorboard --port 6006 &
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml
```

### 🔄 Reproducibility

#### Set Random Seeds
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --seed 42 \
    --deterministic true
```

#### Save Complete Configuration
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --save-config results/experiment_config.yaml
```

---

## 📚 10. Additional Resources

### 📖 Documentation

- **Architecture Details**: `docs/architecture.md`
- **API Reference**: `docs/protocols.md`
- **Benchmark Specifications**: `docs/benchmarks.md`
- **Glossary**: `docs/glossary.md`

### 🔗 External Dependencies

- **PettingZoo**: Multi-agent environment suite
- **SMAC**: StarCraft Multi-Agent Challenge
- **PyTorch**: Deep learning framework
- **Hydra**: Configuration management
- **Ray Tune**: Hyperparameter optimization

### 🆘 Getting Help

#### Common Issues
1. **CUDA Out of Memory**: Reduce batch size or use gradient accumulation
2. **Environment Not Found**: Check environment installation and paths
3. **Config Errors**: Validate YAML syntax and parameter names

#### Support Channels
- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check `docs/` for detailed explanations
- **Email**: Contact the development team

---

## 📝 11. Citation

If you use this project in your research, please cite:

```bibtex
@misc{MultiAgentGridWorld2025,
  title={Scalable Decentralized Hierarchical RL: MultiAgent Coordination in Dynamic Environments},
  author={Md Shoaib Uddin Chanda},
  year={2025},
  note={https://github.com/your-repo-link},
  license={CC BY-NC 4.0}
}
```

---

## ✅ Quick Reference

### Most Common Commands

```bash
# Quick start examples
python examples/train_sdhrl_gridworld.py
python examples/eval_sdhrl_starcraft.py
python examples/visualize_attention_weights.py

# Full training
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml

# Evaluation
python src/training/runner.py --model-path assets/checkpoints/sdhrl_gridworld.pth --episodes 100

# Benchmarking
python benchmarks/compare_results.py --methods SDHRL,QMIX --environments gridworld

# Visualization
python src/utils/visualization.py --mode animate --output visualizations/demo.gif
python src/utils/attention_viz.py --mode attention_heatmap --output visualizations/attention.png

# Testing
python -m pytest tests/ -v
```

### Configuration Quick Edit

```bash
# Edit main config
nano configs/sdhrl_default.yaml

# Edit environment settings
nano configs/env_gridworld.yaml

# Edit agent parameters
nano configs/agent_hrl_icp.yaml
```

---

**🎯 Happy Experimenting with MultiAgentGridWorldProject!**

For questions or contributions, please refer to the main [README.md](README.md) or open an issue on GitHub.