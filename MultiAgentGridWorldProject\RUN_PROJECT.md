# 🏁 Running the MultiAgentGridWorldProject

> **Complete Guide for Training, Evaluation, and Benchmarking**
> *Scalable Decentralized Hierarchical Reinforcement Learning Framework*

This guide provides step-by-step instructions to **train**, **evaluate**, **benchmark**, and **visualize** experiments using the MultiAgentGridWorldProject framework with hierarchical agents and implicit communication.

---

## 📦 0. Initial Setup (One-Time)

### 1. Environment Setup

#### Option A: Conda (Recommended)
```bash
conda env create -f environment.yml
conda activate sdhrl-env
```

#### Option B: pip
```bash
pip install -r requirements.txt
```

#### Option C: Development Installation
```bash
pip install -e .
```

### 2. Verify Installation
```bash
python -c "import src; print('✅ Installation successful!')"
```

---

## 🚀 1. Training Experiments

### 🎯 GridWorld with Dynamic Events

#### Basic Training (Default Config)
```bash
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml
```

#### Custom Configuration
```bash
python experiments/run_gridworld.py \
    --config configs/env_gridworld.yaml \
    --agents 8 \
    --grid-size 15 \
    --dynamic-obstacles true \
    --max-episodes 5000
```

#### Advanced HRL Training
```bash
python experiments/run_gridworld.py \
    --config configs/agent_hrl_icp.yaml \
    --hierarchical true \
    --option-duration 10 \
    --attention-threshold 0.3 \
    --communication-range 5
```

### 🌐 Multi-Particle Environment (MPE)

#### Cooperative Navigation
```bash
python experiments/run_mpe.py \
    --config configs/env_mpe.yaml \
    --scenario simple_spread \
    --num-agents 6 \
    --landmarks 6
```

#### Predator-Prey Scenario
```bash
python experiments/run_mpe.py \
    --config configs/env_mpe.yaml \
    --scenario simple_tag \
    --num-good 4 \
    --num-adversaries 2
```

### ⚔️ SMAC (StarCraft II)

#### 3 Marines vs 3 Marines
```bash
python experiments/run_smac.py \
    --config configs/env_smac.yaml \
    --map-name 3m \
    --difficulty 7
```

#### Large Scale Battle
```bash
python experiments/run_smac.py \
    --config configs/env_smac.yaml \
    --map-name 27m_vs_30m \
    --difficulty 7 \
    --max-timesteps 2000000
```

---

## 🔍 2. Model Evaluation

### 📊 Evaluate Trained Models

#### Single Model Evaluation
```bash
python src/training/runner.py \
    --model-path checkpoints/sdhrl_gridworld_best.pt \
    --episodes 100 \
    --render true \
    --save-results results/evaluation.json
```

#### Multi-Environment Evaluation
```bash
python src/training/trainer.py \
    --mode evaluate \
    --config configs/sdhrl_default.yaml \
    --environments gridworld,mpe,smac \
    --seeds 42,1337,2025
```

### 🎯 Performance Metrics

#### Generate Metrics Report
```bash
python src/utils/metrics.py \
    --results-dir results/ \
    --metrics task_completion,deadlock_rate,communication_efficiency \
    --output-format csv,json
```

#### Real-time Monitoring
```bash
python src/utils/logger.py \
    --tensorboard-dir logs/tensorboard \
    --wandb-project sdhrl-experiments \
    --live-plotting true
```

---

## 🏆 3. Baseline Comparisons

### 🔄 Run All Baselines

#### Complete Benchmark Suite
```bash
python benchmarks/compare_results.py \
    --methods SDHRL,QMIX,MADDPG,IPPO \
    --environments all \
    --trials 5 \
    --output-dir results/benchmarks/
```

#### Specific Baseline Comparison
```bash
python benchmarks/baselines.py \
    --baseline QMIX \
    --environment gridworld \
    --config configs/env_gridworld.yaml \
    --episodes 1000
```

### 🌍 Real-World Case Studies

#### Traffic Control (CityFlow)
```bash
python benchmarks/realworld_case.py \
    --domain traffic \
    --scenario cityflow_1x1 \
    --agents 4 \
    --duration 3600
```

#### Power Grid Management (IEEE-33)
```bash
python benchmarks/realworld_case.py \
    --domain power_grid \
    --scenario ieee_33_bus \
    --agents 10 \
    --load-variation 0.2
```

---

## 🎨 4. Visualization & Analysis

### 📹 Generate Visualizations

#### Create Training GIFs
```bash
python src/utils/visualization.py \
    --mode animate \
    --log-dir logs/gridworld_run_001/ \
    --output visualizations/gridworld_demo.gif \
    --fps 10 \
    --highlight-communication true
```

#### Communication Pattern Analysis
```bash
python src/utils/visualization.py \
    --mode attention_heatmap \
    --model-path checkpoints/sdhrl_best.pt \
    --episodes 10 \
    --output visualizations/communication_patterns.png
```

#### Performance Plots
```bash
python src/utils/visualization.py \
    --mode performance_plots \
    --results-dir results/ \
    --metrics reward,success_rate,deadlock_rate \
    --output visualizations/smac_performance.png
```

---

## ⚙️ 5. Hyperparameter Optimization

### 🔍 Automated Hyperparameter Sweep

#### Ray Tune Integration
```bash
python experiments/sweep_hyperparams.py \
    --config configs/training_defaults.yaml \
    --search-space learning_rate,batch_size,attention_threshold \
    --trials 50 \
    --gpus 2
```

#### Optuna Optimization
```bash
python experiments/sweep_hyperparams.py \
    --optimizer optuna \
    --objective task_completion_rate \
    --trials 100 \
    --study-name sdhrl_optimization
```

#### Grid Search
```bash
python experiments/sweep_hyperparams.py \
    --method grid_search \
    --params "learning_rate=[1e-4,3e-4,1e-3],batch_size=[32,64,128]" \
    --environment gridworld
```

---

## 📋 6. Configuration Management

### 🔧 Key Configuration Files

#### Agent Configuration (`configs/agent_hrl_icp.yaml`)
```yaml
agent:
  type: hierarchical
  option_policy:
    hidden_dims: [256, 256]
    activation: relu
    option_duration: 10
  worker_policy:
    hidden_dims: [128, 128]
    activation: tanh
  communication:
    attention_dim: 64
    threshold: 0.3
    max_range: 5
```

#### Environment Configuration (`configs/env_gridworld.yaml`)
```yaml
environment:
  name: dynamic_gridworld
  grid_size: [12, 12]
  num_agents: 6
  num_goals: 3
  obstacle_density: 0.2
  dynamic_obstacles: true
  partial_observability: true
  observation_radius: 3
```

#### Training Configuration (`configs/training_defaults.yaml`)
```yaml
training:
  max_episodes: 10000
  max_timesteps: 2000000
  batch_size: 64
  learning_rate: 3e-4
  gamma: 0.99
  tau: 0.005
  update_frequency: 100
  save_frequency: 1000
  eval_frequency: 500
```

### 🎛️ Command Line Overrides

#### Override Single Parameters
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --override "training.learning_rate=1e-3,environment.num_agents=8"
```

#### Environment-Specific Overrides
```bash
python experiments/run_mpe.py \
    --config configs/env_mpe.yaml \
    --override "environment.scenario=simple_spread,agent.communication.threshold=0.5"
```

---

## 🐛 7. Debugging & Troubleshooting

### 🔍 Debug Mode

#### Enable Detailed Logging
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --debug true \
    --log-level DEBUG \
    --save-trajectories true
```

#### Quick Sanity Check
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --max-episodes 10 \
    --render true \
    --no-save true
```

### 🧪 Unit Testing

#### Run All Tests
```bash
python -m pytest tests/ -v
```

#### Test Specific Components
```bash
python -m pytest tests/test_environment.py::test_gridworld_dynamics -v
python -m pytest tests/test_agents.py::test_hierarchical_agent_action -v
python -m pytest tests/test_comms.py::test_attention_mechanism -v
```

### 📊 Performance Profiling

#### Memory Usage Analysis
```bash
python -m memory_profiler experiments/run_gridworld.py --config configs/sdhrl_default.yaml
```

#### GPU Utilization Monitoring
```bash
nvidia-smi -l 1 & python experiments/run_gridworld.py --config configs/sdhrl_default.yaml --gpus 1
```

---

## 🧹 8. Project Management

### 📁 Directory Structure After Training

```
MultiAgentGridWorldProject/
├── checkpoints/              # Saved model weights
│   ├── sdhrl_gridworld_best.pt
│   ├── sdhrl_mpe_latest.pt
│   └── baseline_qmix_final.pt
├── logs/                     # Training logs and TensorBoard data
│   ├── tensorboard/
│   ├── wandb/
│   └── training_logs.txt
├── results/                  # Evaluation results and metrics
│   ├── benchmarks/
│   ├── evaluation_metrics.json
│   └── performance_comparison.csv
└── visualizations/           # Generated plots and animations
    ├── gridworld_demo.gif
    ├── communication_patterns.png
    └── training_curves.png
```

### 🗑️ Cleanup Commands

#### Remove All Generated Files
```bash
rm -rf checkpoints/ logs/ results/
mkdir -p checkpoints logs results
```

#### Clean Specific Experiment
```bash
rm -rf logs/gridworld_run_* checkpoints/sdhrl_gridworld_*
```

#### Reset to Fresh State
```bash
git clean -fdx --exclude=src/ --exclude=configs/ --exclude=*.md
```

---

## 💡 9. Pro Tips & Best Practices

### 🚀 Performance Optimization

#### Multi-GPU Training
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --gpus 2 \
    --distributed-backend nccl \
    --batch-size 128
```

#### Efficient Hyperparameter Search
```bash
# Use smaller episodes for initial search
python experiments/sweep_hyperparams.py \
    --config configs/training_defaults.yaml \
    --override "training.max_episodes=1000" \
    --trials 20 \
    --early-stopping true
```

### 📊 Experiment Tracking

#### Weights & Biases Integration
```bash
export WANDB_PROJECT="sdhrl-experiments"
export WANDB_ENTITY="your-username"
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml --wandb true
```

#### TensorBoard Monitoring
```bash
tensorboard --logdir logs/tensorboard --port 6006 &
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml
```

### 🔄 Reproducibility

#### Set Random Seeds
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --seed 42 \
    --deterministic true
```

#### Save Complete Configuration
```bash
python experiments/run_gridworld.py \
    --config configs/sdhrl_default.yaml \
    --save-config results/experiment_config.yaml
```

---

## 📚 10. Additional Resources

### 📖 Documentation

- **Architecture Details**: `docs/architecture.md`
- **API Reference**: `docs/protocols.md`
- **Benchmark Specifications**: `docs/benchmarks.md`
- **Glossary**: `docs/glossary.md`

### 🔗 External Dependencies

- **PettingZoo**: Multi-agent environment suite
- **SMAC**: StarCraft Multi-Agent Challenge
- **PyTorch**: Deep learning framework
- **Hydra**: Configuration management
- **Ray Tune**: Hyperparameter optimization

### 🆘 Getting Help

#### Common Issues
1. **CUDA Out of Memory**: Reduce batch size or use gradient accumulation
2. **Environment Not Found**: Check environment installation and paths
3. **Config Errors**: Validate YAML syntax and parameter names

#### Support Channels
- **GitHub Issues**: Report bugs and feature requests
- **Documentation**: Check `docs/` for detailed explanations
- **Email**: Contact the development team

---

## 📝 11. Citation

If you use this project in your research, please cite:

```bibtex
@misc{MultiAgentGridWorld2025,
  title={Scalable Decentralized Hierarchical RL: MultiAgent Coordination in Dynamic Environments},
  author={Md Shoaib Uddin Chanda},
  year={2025},
  note={https://github.com/your-repo-link},
  license={CC BY-NC 4.0}
}
```

---

## ✅ Quick Reference

### Most Common Commands

```bash
# Basic training
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml

# Evaluation
python src/training/runner.py --model-path checkpoints/best.pt --episodes 100

# Benchmarking
python benchmarks/compare_results.py --methods SDHRL,QMIX --environments gridworld

# Visualization
python src/utils/visualization.py --mode animate --output visualizations/demo.gif

# Testing
python -m pytest tests/ -v
```

### Configuration Quick Edit

```bash
# Edit main config
nano configs/sdhrl_default.yaml

# Edit environment settings
nano configs/env_gridworld.yaml

# Edit agent parameters
nano configs/agent_hrl_icp.yaml
```

---

**🎯 Happy Experimenting with MultiAgentGridWorldProject!**

For questions or contributions, please refer to the main [README.md](README.md) or open an issue on GitHub.