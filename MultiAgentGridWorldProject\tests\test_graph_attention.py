"""
Graph Attention Communication Tests

Comprehensive validation of graph attention-based communication module
including output shapes, masking, KL regularization, and attention maps.
"""

import pytest
import torch
import numpy as np
from typing import Dict, Any
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.comms import GraphAttentionEncoder, MultiAgentCommunicationModule


class TestGraphAttentionEncoder:
    """Test graph attention encoder functionality."""
    
    @pytest.fixture
    def config(self):
        """Basic configuration for graph attention encoder."""
        return {
            "input_dim": 64,
            "hidden_dim": 128,
            "output_dim": 64,
            "num_heads": 4,
            "num_layers": 2,
            "dropout": 0.1,
            "use_residual": True,
            "use_layer_norm": True,
            "kl_weight": 0.01,
            "use_kl_regularization": True
        }
    
    @pytest.fixture
    def encoder(self, config):
        """Create graph attention encoder."""
        return GraphAttentionEncoder(config)
    
    @pytest.fixture
    def sample_data(self):
        """Create sample input data."""
        num_nodes = 4
        input_dim = 64
        
        x = torch.randn(num_nodes, input_dim)
        edge_index = torch.tensor([[0, 1, 2, 3, 1, 2], [1, 2, 3, 0, 0, 1]], dtype=torch.long)
        mask = torch.tensor([1.0, 1.0, 0.0, 1.0])  # Agent 2 is muted
        
        return x, edge_index, mask
    
    def test_encoder_creation(self, encoder, config):
        """Test encoder instantiation."""
        assert encoder is not None
        assert encoder.input_dim == config["input_dim"]
        assert encoder.output_dim == config["output_dim"]
        assert encoder.num_heads == config["num_heads"]
        print("✅ GraphAttentionEncoder creation test passed")
    
    def test_output_shape(self, encoder, sample_data):
        """Test that input N×D produces output N×D'."""
        x, edge_index, mask = sample_data
        
        # Forward pass
        output = encoder(x, edge_index, mask)
        
        # Check output shape
        expected_shape = (x.shape[0], encoder.output_dim)
        assert output.shape == expected_shape, f"Expected {expected_shape}, got {output.shape}"
        
        # Check output is finite
        assert torch.isfinite(output).all(), "Output contains non-finite values"
        
        print("✅ Output shape test passed")
    
    def test_masked_attention(self, encoder, sample_data):
        """Test that masked agents get zero attention."""
        x, edge_index, mask = sample_data
        
        # Forward pass with mask
        output_masked = encoder(x, edge_index, mask)
        
        # Forward pass without mask
        output_unmasked = encoder(x, edge_index, None)
        
        # Muted agent (index 2) should have different output
        muted_agent_idx = 2
        assert not torch.allclose(output_masked[muted_agent_idx], output_unmasked[muted_agent_idx]), \
            "Muted agent output should be different from unmasked"
        
        # Check that muted agent's output is properly masked (should be zeros or very small)
        muted_output_norm = torch.norm(output_masked[muted_agent_idx])
        unmuted_output_norm = torch.norm(output_unmasked[muted_agent_idx])
        assert muted_output_norm < unmuted_output_norm, "Muted agent should have reduced output magnitude"
        
        print("✅ Masked attention test passed")
    
    def test_kl_loss_validity(self, encoder, sample_data):
        """Test that KL loss returns non-negative scalar."""
        x, edge_index, mask = sample_data
        
        # Forward pass to generate attention weights
        output = encoder(x, edge_index, mask)
        
        # Compute KL divergence
        kl_loss = encoder.compute_kl_divergence()
        
        # Check KL loss properties
        assert isinstance(kl_loss, torch.Tensor), "KL loss should be a tensor"
        assert kl_loss.dim() == 0, "KL loss should be a scalar"
        assert kl_loss.item() >= -1e-6, f"KL loss should be non-negative, got {kl_loss.item()}"
        assert torch.isfinite(kl_loss), "KL loss should be finite"
        
        print("✅ KL loss validity test passed")
    
    def test_attention_map_structure(self, encoder, sample_data):
        """Test that attention map has correct structure."""
        x, edge_index, mask = sample_data
        
        # Forward pass to generate attention weights
        output = encoder(x, edge_index, mask)
        
        # Get attention map
        attention_map = encoder.get_attention_map()
        
        if attention_map is not None:
            num_nodes = x.shape[0]
            
            # Check attention map shape
            expected_shape = (num_nodes, num_nodes)
            assert attention_map.shape == expected_shape, f"Expected {expected_shape}, got {attention_map.shape}"
            
            # Check attention values are in valid range
            assert torch.all(attention_map >= 0), "Attention values should be non-negative"
            assert torch.isfinite(attention_map).all(), "Attention values should be finite"
            
            # Check that rows sum to reasonable values (not necessarily 1 due to masking)
            row_sums = attention_map.sum(dim=1)
            assert torch.all(row_sums >= 0), "Row sums should be non-negative"
        
        print("✅ Attention map structure test passed")
    
    def test_residual_connections(self, config):
        """Test residual connections functionality."""
        # Test with matching input/output dimensions
        config_match = config.copy()
        config_match["input_dim"] = config_match["output_dim"]
        encoder_match = GraphAttentionEncoder(config_match)
        
        # Test with different input/output dimensions
        config_diff = config.copy()
        config_diff["input_dim"] = 32
        config_diff["output_dim"] = 64
        encoder_diff = GraphAttentionEncoder(config_diff)
        
        # Sample data
        x_match = torch.randn(4, config_match["input_dim"])
        x_diff = torch.randn(4, config_diff["input_dim"])
        edge_index = torch.tensor([[0, 1, 2], [1, 2, 0]], dtype=torch.long)
        
        # Forward passes
        output_match = encoder_match(x_match, edge_index)
        output_diff = encoder_diff(x_diff, edge_index)
        
        # Check outputs are valid
        assert torch.isfinite(output_match).all()
        assert torch.isfinite(output_diff).all()
        
        # Check residual projection exists for different dimensions
        assert hasattr(encoder_diff, 'residual_proj')
        assert not hasattr(encoder_match, 'residual_proj') or encoder_match.residual_proj is None
        
        print("✅ Residual connections test passed")
    
    def test_different_graph_structures(self, encoder):
        """Test encoder with different graph structures."""
        num_nodes = 4
        input_dim = encoder.input_dim
        x = torch.randn(num_nodes, input_dim)
        
        # Test 1: Fully connected graph
        edges_full = []
        for i in range(num_nodes):
            for j in range(num_nodes):
                if i != j:
                    edges_full.append([i, j])
        edge_index_full = torch.tensor(edges_full, dtype=torch.long).t()
        
        output_full = encoder(x, edge_index_full)
        assert output_full.shape == (num_nodes, encoder.output_dim)
        
        # Test 2: Chain graph
        edge_index_chain = torch.tensor([[0, 1, 2], [1, 2, 3]], dtype=torch.long)
        output_chain = encoder(x, edge_index_chain)
        assert output_chain.shape == (num_nodes, encoder.output_dim)
        
        # Test 3: Empty graph (no edges)
        edge_index_empty = torch.empty((2, 0), dtype=torch.long)
        output_empty = encoder(x, edge_index_empty)
        assert output_empty.shape == (num_nodes, encoder.output_dim)
        
        # Outputs should be different for different graph structures
        assert not torch.allclose(output_full, output_chain, atol=1e-3)
        
        print("✅ Different graph structures test passed")


class TestMultiAgentCommunicationModule:
    """Test complete multi-agent communication module."""
    
    @pytest.fixture
    def config(self):
        """Configuration for communication module."""
        return {
            "num_agents": 4,
            "communication_dim": 64,
            "comm_hidden_dim": 128,
            "attention_heads": 4,
            "comm_layers": 2,
            "max_communication_range": 5.0
        }
    
    @pytest.fixture
    def comm_module(self, config):
        """Create communication module."""
        return MultiAgentCommunicationModule(config)
    
    @pytest.fixture
    def sample_agent_data(self, config):
        """Create sample agent data."""
        agent_features = {
            f"agent_{i}": torch.randn(config["communication_dim"])
            for i in range(config["num_agents"])
        }
        
        agent_positions = {
            f"agent_{i}": torch.tensor([float(i), float(i)])
            for i in range(config["num_agents"])
        }
        
        communication_mask = {
            f"agent_{i}": i != 2  # Agent 2 is muted
            for i in range(config["num_agents"])
        }
        
        return agent_features, agent_positions, communication_mask
    
    def test_communication_module_creation(self, comm_module, config):
        """Test communication module instantiation."""
        assert comm_module is not None
        assert comm_module.num_agents == config["num_agents"]
        assert comm_module.communication_dim == config["communication_dim"]
        print("✅ Communication module creation test passed")
    
    def test_multi_agent_communication(self, comm_module, sample_agent_data):
        """Test multi-agent communication forward pass."""
        agent_features, agent_positions, communication_mask = sample_agent_data
        
        # Forward pass
        updated_features = comm_module(agent_features, agent_positions, communication_mask)
        
        # Check output structure
        assert isinstance(updated_features, dict)
        assert len(updated_features) == len(agent_features)
        
        for agent_id in agent_features.keys():
            assert agent_id in updated_features
            assert updated_features[agent_id].shape == agent_features[agent_id].shape
            assert torch.isfinite(updated_features[agent_id]).all()
        
        # Check that features have changed (communication occurred)
        for agent_id in agent_features.keys():
            if communication_mask[agent_id]:  # Only active agents should have significant changes
                feature_change = torch.norm(updated_features[agent_id] - agent_features[agent_id])
                assert feature_change > 1e-6, f"Active agent {agent_id} should have feature changes"
        
        print("✅ Multi-agent communication test passed")
    
    def test_proximity_based_communication(self, comm_module, sample_agent_data):
        """Test proximity-based communication graph creation."""
        agent_features, agent_positions, communication_mask = sample_agent_data
        
        # Test with close positions (should communicate)
        close_positions = {
            "agent_0": torch.tensor([0.0, 0.0]),
            "agent_1": torch.tensor([1.0, 1.0]),  # Close to agent_0
            "agent_2": torch.tensor([10.0, 10.0]),  # Far from others
            "agent_3": torch.tensor([0.5, 0.5])   # Close to agent_0 and agent_1
        }
        
        updated_close = comm_module(agent_features, close_positions, communication_mask)
        
        # Test with far positions (limited communication)
        far_positions = {
            f"agent_{i}": torch.tensor([float(i * 10), float(i * 10)])
            for i in range(4)
        }
        
        updated_far = comm_module(agent_features, far_positions, communication_mask)
        
        # Communication should be different based on proximity
        for agent_id in agent_features.keys():
            if communication_mask[agent_id]:
                close_change = torch.norm(updated_close[agent_id] - agent_features[agent_id])
                far_change = torch.norm(updated_far[agent_id] - agent_features[agent_id])
                # Changes should be different (not necessarily one > other due to complexity)
                assert not torch.allclose(updated_close[agent_id], updated_far[agent_id], atol=1e-3)
        
        print("✅ Proximity-based communication test passed")
    
    def test_communication_metrics(self, comm_module, sample_agent_data):
        """Test communication metrics computation."""
        agent_features, agent_positions, communication_mask = sample_agent_data
        
        # Forward pass
        updated_features = comm_module(agent_features, agent_positions, communication_mask)
        
        # Get metrics
        metrics = comm_module.get_communication_metrics()
        
        # Check metrics structure
        assert isinstance(metrics, dict)
        assert "communication_kl_loss" in metrics
        
        # Check metric values
        kl_loss = metrics["communication_kl_loss"]
        assert isinstance(kl_loss, float)
        assert kl_loss >= -1e-6  # Allow small numerical errors
        assert np.isfinite(kl_loss)
        
        if "attention_entropy" in metrics:
            entropy = metrics["attention_entropy"]
            assert isinstance(entropy, float)
            assert entropy >= 0
            assert np.isfinite(entropy)
        
        print("✅ Communication metrics test passed")
    
    def test_attention_visualization(self, comm_module, sample_agent_data):
        """Test attention visualization functionality."""
        agent_features, agent_positions, communication_mask = sample_agent_data
        
        # Forward pass
        updated_features = comm_module(agent_features, agent_positions, communication_mask)
        
        # Get attention visualization
        attention_map = comm_module.visualize_attention()
        
        if attention_map is not None:
            # Check attention map properties
            assert isinstance(attention_map, torch.Tensor)
            assert attention_map.dim() == 2
            assert attention_map.shape[0] == attention_map.shape[1]  # Square matrix
            assert torch.isfinite(attention_map).all()
            assert torch.all(attention_map >= 0)  # Non-negative attention
        
        print("✅ Attention visualization test passed")


def test_communication_integration():
    """Integration test with all communication components."""
    config = {
        "num_agents": 3,
        "communication_dim": 32,
        "comm_hidden_dim": 64,
        "attention_heads": 2,
        "comm_layers": 2,
        "max_communication_range": 3.0,
        "input_dim": 32,
        "output_dim": 32,
        "hidden_dim": 64,
        "num_heads": 2,
        "use_kl_regularization": True,
        "kl_weight": 0.01
    }
    
    # Create communication module
    comm_module = MultiAgentCommunicationModule(config)
    
    # Create sample data
    agent_features = {
        f"agent_{i}": torch.randn(config["communication_dim"])
        for i in range(config["num_agents"])
    }
    
    agent_positions = {
        f"agent_{i}": torch.tensor([float(i), 0.0])
        for i in range(config["num_agents"])
    }
    
    # Run multiple communication steps
    for step in range(5):
        updated_features = comm_module(agent_features, agent_positions)
        
        # Check all outputs are valid
        for agent_id, features in updated_features.items():
            assert torch.isfinite(features).all()
            assert features.shape == agent_features[agent_id].shape
        
        # Update features for next step
        agent_features = updated_features
    
    # Get final metrics
    metrics = comm_module.get_communication_metrics()
    assert "communication_kl_loss" in metrics
    
    print("✅ Communication integration test passed")


if __name__ == "__main__":
    # Run tests manually if not using pytest
    print("Running Graph Attention Communication Tests...")
    
    # Basic encoder tests
    config = {
        "input_dim": 64,
        "hidden_dim": 128,
        "output_dim": 64,
        "num_heads": 4,
        "num_layers": 2,
        "dropout": 0.1,
        "use_residual": True,
        "use_layer_norm": True,
        "kl_weight": 0.01,
        "use_kl_regularization": True
    }
    
    encoder_test = TestGraphAttentionEncoder()
    encoder = GraphAttentionEncoder(config)
    
    # Sample data
    x = torch.randn(4, 64)
    edge_index = torch.tensor([[0, 1, 2, 3, 1, 2], [1, 2, 3, 0, 0, 1]], dtype=torch.long)
    mask = torch.tensor([1.0, 1.0, 0.0, 1.0])
    sample_data = (x, edge_index, mask)
    
    encoder_test.test_encoder_creation(encoder, config)
    encoder_test.test_output_shape(encoder, sample_data)
    encoder_test.test_masked_attention(encoder, sample_data)
    encoder_test.test_kl_loss_validity(encoder, sample_data)
    encoder_test.test_attention_map_structure(encoder, sample_data)
    
    # Communication module tests
    comm_config = {
        "num_agents": 4,
        "communication_dim": 64,
        "comm_hidden_dim": 128,
        "attention_heads": 4,
        "comm_layers": 2,
        "max_communication_range": 5.0
    }
    
    comm_test = TestMultiAgentCommunicationModule()
    comm_module = MultiAgentCommunicationModule(comm_config)
    
    agent_features = {f"agent_{i}": torch.randn(64) for i in range(4)}
    agent_positions = {f"agent_{i}": torch.tensor([float(i), float(i)]) for i in range(4)}
    communication_mask = {f"agent_{i}": i != 2 for i in range(4)}
    sample_agent_data = (agent_features, agent_positions, communication_mask)
    
    comm_test.test_communication_module_creation(comm_module, comm_config)
    comm_test.test_multi_agent_communication(comm_module, sample_agent_data)
    comm_test.test_communication_metrics(comm_module, sample_agent_data)
    
    # Integration test
    test_communication_integration()
    
    print("\n🎉 All Graph Attention Communication Tests Passed!")
    print("✅ Communication module ready for hierarchical policy integration")