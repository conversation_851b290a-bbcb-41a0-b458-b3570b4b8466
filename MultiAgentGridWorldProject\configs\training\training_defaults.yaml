# Training Configuration for SD-HRL
seed: 42
num_epochs: 1000                  # Extended training for complex tasks
max_timesteps: 2000000           # Alternative stopping criterion

# Bat<PERSON> and Buffer Configuration
batch_size: 128                   # Larger batches for stability
mini_batch_size: 32              # For gradient accumulation
replay_buffer_size: 500000       # Larger buffer for diverse experiences
prioritized_replay: true         # PER for better sample efficiency
per_alpha: 0.6                   # Prioritization exponent
per_beta: 0.4                    # Importance sampling correction
per_beta_increment: 0.001        # Beta annealing

# Learning Rates and Optimization
lr: 0.0003                       # Slightly lower for stability
lr_schedule: "cosine"            # "constant", "linear", "cosine", "exponential"
lr_decay: 0.99                   # For exponential decay
optimizer: "adam"                # "adam", "rmsprop", "sgd"
adam_eps: 1e-8
weight_decay: 1e-5
max_grad_norm: 10.0              # Gradient clipping

# RL Algorithm Parameters
gamma: 0.99                      # Discount factor
gae_lambda: 0.95                 # GAE parameter
tau: 0.005                       # Soft target update rate
target_update_freq: 200          # Hard target update frequency
double_q_learning: true          # Double DQN
dueling_networks: true           # Dueling architecture

# Multi-Agent Specific
centralized_training: true       # CTDE
decentralized_execution: true
value_loss_coef: 0.5            # Value function loss weight
entropy_coef: 0.01              # Entropy regularization
option_entropy_coef: 0.005      # Option entropy regularization

# Training Schedule
warmup_steps: 10000             # Random actions before training
train_freq: 4                   # Training frequency (steps)
gradient_steps: 1               # Gradient steps per training
num_workers: 8                  # Parallel environments

# Logging and Monitoring
log_interval: 50                # Log every N episodes
checkpoint_interval: 100        # Save model every N episodes
eval_interval: 25               # Evaluate every N episodes
save_best_model: true
save_last_model: true
log_gradients: false            # Log gradient norms
log_weights: false              # Log weight histograms

# Evaluation Configuration
num_eval_episodes: 10           # More episodes for robust evaluation
eval_deterministic: true        # Deterministic policy during eval
render_eval: false              # Render evaluation episodes
eval_freq: 50                   # Evaluation frequency
record_video: true              # Record evaluation videos
video_freq: 100                 # Video recording frequency

# Early Stopping
early_stopping: true
patience: 100                   # Episodes without improvement
min_delta: 0.01                 # Minimum improvement threshold

# Logging Backends
use_tensorboard: true
use_wandb: true
wandb_project: "SD-HRL"
wandb_entity: null              # Your W&B username/team
wandb_run_name: "${experiment_name}_${experiment_group}_${now:%H-%M}"
wandb_tags: ["${experiment_name}", "${experiment_group}"]
wandb_notes: "SD-HRL experiment with hierarchical agents and implicit communication"

# Advanced Training Features
curriculum_learning: false      # Progressive task difficulty
self_play: false               # Self-play training
population_training: false     # Population-based training
meta_learning: false           # Meta-learning capabilities