
# Usage:
#   conda env create -f environment.yml
#   conda activate sdhrl-env

name: sdhrl-env

channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults

dependencies:
  # ============================================================================
  # PYTHON & CORE SYSTEM
  # ============================================================================
  - python=3.10
  - pip

  # ============================================================================
  # DEEP LEARNING WITH GPU ACCELERATION
  # ============================================================================
  - pytorch>=2.2.0
  - torchvision>=0.17.0
  - torchaudio>=2.2.0
  - pytorch-cuda=12.1          # CUDA toolkit for PyTorch (adjust version as needed)
  - cudatoolkit=12.1           # NVIDIA CUDA toolkit
  - cudnn=8.9                  # NVIDIA cuDNN for deep neural networks

  # ============================================================================
  # SCIENTIFIC COMPUTING & GPU ACCELERATION
  # ============================================================================
  - numpy>=1.24.0
  - scipy>=1.11.0
  - pandas>=2.1.0
  - cupy>=12.3.0               # GPU-accelerated NumPy alternative
  - numba>=0.58.0              # JIT compilation with CUDA support

  # ============================================================================
  # VISUALIZATION & PLOTTING
  # ============================================================================
  - matplotlib>=3.8.0
  - seaborn>=0.13.0
  - plotly>=5.17.0
  - pillow>=10.1.0
  - imageio>=2.33.0

  # ============================================================================
  # ENVIRONMENT SIMULATION
  # ============================================================================
  - pygame>=2.5.0
  - networkx>=3.2.0

  # ============================================================================
  # CONFIGURATION & UTILITIES
  # ============================================================================
  - pyyaml>=6.0
  - tqdm>=4.66.0
  - click>=8.1.0
  - colorama>=0.4.6

  # ============================================================================
  # TESTING & DEVELOPMENT
  # ============================================================================
  - pytest>=7.4.0
  - black>=23.11.0
  - flake8>=6.1.0

  # ============================================================================
  # PIP-ONLY DEPENDENCIES (Not available in conda)
  # ============================================================================
  - pip:
    # --- Reinforcement Learning & Multi-Agent Environments ---
    - pytorch-lightning>=2.2.1
    - gymnasium>=0.29.1
    - stable-baselines3>=2.2.1
    - pettingzoo>=1.24.0
    - supersuit>=3.9.0

    # --- Hierarchical RL & Attention Mechanisms ---
    - einops>=0.7.0
    - transformers>=4.36.0
    - xformers>=0.0.23

    # --- Configuration Management ---
    - hydra-core>=1.3.2
    - omegaconf>=2.3.0

    # --- Experiment Tracking & Logging ---
    - tensorboard>=2.15.0
    - wandb>=0.16.0
    - rich>=13.7.0

    # --- Hyperparameter Optimization ---
    - "ray[tune]>=2.8.0"
    - optuna>=3.5.0

    # --- Additional Visualization ---
    - moviepy>=1.0.3
    - Box2D>=2.3.10

    # --- Testing Extensions ---
    - pytest-mock>=3.12.0
    - pytest-cov>=4.1.0

    # --- Optional: SMAC Environment (Uncomment if needed) ---
    # - pysc2>=3.0.0
    # - smac>=1.0.0

    # --- Optional: Advanced GPU Acceleration (Install manually if needed) ---
    # - torch-tensorrt
    # - onnx>=1.15.0
    # - onnxruntime-gpu>=1.16.0