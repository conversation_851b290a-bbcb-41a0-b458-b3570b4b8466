"""
SD-HRL Utilities Package

This package contains utility modules for logging, metrics, visualization,
and analysis tools for the SD-HRL framework.

Modules:
    logger: Logging utilities with TensorBoard and WandB integration
    metrics: Performance metrics and evaluation tools
    visualization: Environment and training visualization tools
    attention_viz: Attention mechanism visualization and analysis
"""

from .logger import setup_logger, get_logger
from .metrics import MetricsCollector, compute_success_rate, compute_communication_efficiency
from .visualization import GridWorldVisualizer, create_training_gif, plot_training_curves
from .attention_viz import AttentionVisualizer, plot_attention_heatmap, analyze_communication_patterns

__all__ = [
    # Logger utilities
    "setup_logger",
    "get_logger",
    
    # Metrics
    "MetricsCollector", 
    "compute_success_rate",
    "compute_communication_efficiency",
    
    # Visualization
    "GridWorldVisualizer",
    "create_training_gif",
    "plot_training_curves",
    
    # Attention analysis
    "AttentionVisualizer",
    "plot_attention_heatmap", 
    "analyze_communication_patterns",
]

__version__ = "1.0.0"
__author__ = "SD-HRL Research Team"