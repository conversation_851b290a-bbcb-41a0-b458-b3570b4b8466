"""
Critic Network

Value function (V) or Q-function networks for hierarchical RL.
Supports local and shared critics with hierarchical inputs.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, Any, List, Union
import logging

logger = logging.getLogger(__name__)


class CriticNetwork(nn.Module):
    """
    Value function network for reinforcement learning.
    
    Supports both state-value (V) and action-value (Q) functions
    with optional hierarchical inputs like option embeddings.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.obs_dim = config.get("obs_dim", 128)
        self.action_dim = config.get("action_dim", 4)
        self.hidden_dim = config.get("hidden_dim", 256)
        self.num_layers = config.get("num_layers", 3)
        self.activation = config.get("activation", "relu")
        
        # Critic type
        self.critic_type = config.get("critic_type", "state_value")  # "state_value" or "action_value"
        self.num_critics = config.get("num_critics", 1)  # For ensemble critics
        
        # Hierarchical support
        self.use_option_conditioning = config.get("use_option_conditioning", False)
        self.num_options = config.get("num_options", 6)
        self.option_embed_dim = config.get("option_embed_dim", 32)
        
        # Regularization
        self.dropout = config.get("dropout", 0.1)
        self.use_layer_norm = config.get("use_layer_norm", True)
        self.use_spectral_norm = config.get("use_spectral_norm", False)
        
        # Output options
        self.output_entropy = config.get("output_entropy", False)
        self.output_distributional = config.get("output_distributional", False)
        self.num_atoms = config.get("num_atoms", 51) if self.output_distributional else None
        
        # Build network
        self._build_network()
        
        # Initialize weights
        self._initialize_weights()
        
        logger.info(f"CriticNetwork initialized: {self.critic_type}, "
                   f"{self.num_critics} critics, hierarchical: {self.use_option_conditioning}")
    
    def _build_network(self):
        """Build the critic network."""
        
        # Determine input dimension
        input_dim = self.obs_dim
        if self.critic_type == "action_value":
            input_dim += self.action_dim
        if self.use_option_conditioning:
            input_dim += self.option_embed_dim
        
        # Activation function
        if self.activation == "relu":
            activation_fn = nn.ReLU
        elif self.activation == "tanh":
            activation_fn = nn.Tanh
        elif self.activation == "gelu":
            activation_fn = nn.GELU
        elif self.activation == "swish":
            activation_fn = nn.SiLU
        else:
            activation_fn = nn.ReLU
        
        # Option embeddings
        if self.use_option_conditioning:
            self.option_embeddings = nn.Embedding(self.num_options, self.option_embed_dim)
        
        # Build critics (ensemble if num_critics > 1)
        self.critics = nn.ModuleList()
        
        for critic_idx in range(self.num_critics):
            layers = []
            current_dim = input_dim
            
            for i in range(self.num_layers):
                # Linear layer
                linear = nn.Linear(current_dim, self.hidden_dim)
                if self.use_spectral_norm:
                    linear = nn.utils.spectral_norm(linear)
                layers.append(linear)
                
                # Activation
                layers.append(activation_fn())
                
                # Layer normalization
                if self.use_layer_norm:
                    layers.append(nn.LayerNorm(self.hidden_dim))
                
                # Dropout
                if self.dropout > 0:
                    layers.append(nn.Dropout(self.dropout))
                
                current_dim = self.hidden_dim
            
            # Output layer
            if self.output_distributional:
                output_dim = self.num_atoms
            else:
                output_dim = 1
            
            output_layer = nn.Linear(self.hidden_dim, output_dim)
            if self.use_spectral_norm:
                output_layer = nn.utils.spectral_norm(output_layer)
            layers.append(output_layer)
            
            self.critics.append(nn.Sequential(*layers))
        
        # Entropy estimation network (optional)
        if self.output_entropy:
            self.entropy_head = nn.Sequential(
                nn.Linear(self.hidden_dim, self.hidden_dim // 2),
                activation_fn(),
                nn.Linear(self.hidden_dim // 2, 1),
                nn.Softplus()  # Ensure positive entropy
            )
    
    def _initialize_weights(self):
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                if self.config.get("use_orthogonal_init", True):
                    nn.init.orthogonal_(module.weight)
                else:
                    nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0.0, 0.1)
    
    def forward(self, observations: torch.Tensor,
                actions: Optional[torch.Tensor] = None,
                options: Optional[torch.Tensor] = None,
                return_features: bool = False) -> Dict[str, torch.Tensor]:
        """Forward pass through critic network.
        
        Computes value estimates for given observations and optionally actions/options.
        Supports both state-value (V) and action-value (Q) functions with hierarchical
        conditioning through option embeddings.
        
        Args:
            observations (torch.Tensor): Batch of observations with shape 
                ``[batch_size, obs_dim]``.
            actions (torch.Tensor, optional): Batch of actions with shape 
                ``[batch_size, action_dim]``. Required when ``critic_type`` is 
                ``"action_value"``. Default: ``None``.
            options (torch.Tensor, optional): Batch of option indices with shape 
                ``[batch_size]``. Required when ``use_option_conditioning`` is ``True``.
                Default: ``None``.
            return_features (bool, optional): If ``True``, returns intermediate 
                feature representations. Default: ``False``.
                
        Returns:
            Dict[str, torch.Tensor]: Dictionary containing:
                - **values** (torch.Tensor): Critic value estimates with shape 
                  ``[batch_size, num_critics]`` for ensemble critics or 
                  ``[batch_size, num_atoms]`` for distributional critics.
                - **entropy** (torch.Tensor, optional): Entropy estimates with shape 
                  ``[batch_size]``. Only present if ``output_entropy`` is ``True``.
                - **features** (torch.Tensor, optional): Intermediate feature 
                  representations with shape ``[batch_size, hidden_dim]``. 
                  Only present if ``return_features`` is ``True``.
                  
        Raises:
            ValueError: If required inputs are missing (e.g., actions for Q-function,
                options for hierarchical conditioning).
                
        Note:
            For distributional critics, the values represent probability distributions
            over value atoms rather than point estimates.
        """
        batch_size = observations.shape[0]
        
        # Prepare input
        inputs = [observations]
        
        # Add actions for Q-function
        if self.critic_type == "action_value":
            if actions is None:
                raise ValueError("Actions required for action-value critic")
            inputs.append(actions)
        
        # Add option embeddings
        if self.use_option_conditioning:
            if options is None:
                raise ValueError("Options required for option-conditioned critic")
            option_embeds = self.option_embeddings(options)
            inputs.append(option_embeds)
        
        # Concatenate inputs
        network_input = torch.cat(inputs, dim=-1)
        
        # Forward through critics
        critic_values = []
        features = None
        
        for critic in self.critics:
            # Get features from the last hidden layer
            x = network_input
            for i, layer in enumerate(critic[:-1]):  # All layers except output
                x = layer(x)
            
            # Store features from first critic
            if features is None:
                features = x
            
            # Get final output
            value = critic[-1](x)
            
            if self.output_distributional:
                # Distributional critic (e.g., for C51)
                value = F.softmax(value, dim=-1)
            
            critic_values.append(value)
        
        # Stack critic outputs
        values = torch.stack(critic_values, dim=-1)  # [batch_size, 1, num_critics]
        if not self.output_distributional:
            values = values.squeeze(1)  # [batch_size, num_critics]
        
        result = {"values": values}
        
        # Add entropy estimation
        if self.output_entropy:
            entropy = self.entropy_head(features).squeeze(-1)
            result["entropy"] = entropy
        
        # Add features if requested
        if return_features:
            result["features"] = features
        
        return result
    
    def compute_target(self, rewards: torch.Tensor,
                      next_values: torch.Tensor,
                      dones: torch.Tensor,
                      gamma: float = 0.99) -> torch.Tensor:
        """Compute TD target values."""
        targets = rewards + gamma * next_values * (1 - dones.float())
        return targets
    
    def compute_loss(self, predictions: torch.Tensor,
                    targets: torch.Tensor,
                    loss_type: str = "mse") -> torch.Tensor:
        """Compute critic loss."""
        if loss_type == "mse":
            return F.mse_loss(predictions, targets)
        elif loss_type == "huber":
            return F.smooth_l1_loss(predictions, targets)
        elif loss_type == "mae":
            return F.l1_loss(predictions, targets)
        else:
            raise ValueError(f"Unknown loss type: {loss_type}")


class HierarchicalCritic(nn.Module):
    """
    Hierarchical critic with separate value functions for options and actions.
    
    Maintains both option-level values (for option selection) and
    action-level values (for worker policies).
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.num_options = config.get("num_options", 6)
        
        # Option-level critic (for high-level policy)
        option_config = config.copy()
        option_config.update({
            "critic_type": "state_value",
            "use_option_conditioning": False,
            "num_critics": config.get("num_option_critics", 1)
        })
        self.option_critic = CriticNetwork(option_config)
        
        # Action-level critic (for worker policies)
        action_config = config.copy()
        action_config.update({
            "critic_type": "action_value",
            "use_option_conditioning": True,
            "num_critics": config.get("num_action_critics", 2)  # Double Q-learning
        })
        self.action_critic = CriticNetwork(action_config)
        
        logger.info(f"HierarchicalCritic initialized: {self.num_options} options, "
                   f"option_critics={option_config['num_critics']}, "
                   f"action_critics={action_config['num_critics']}")
    
    def forward(self, observations: torch.Tensor,
                actions: Optional[torch.Tensor] = None,
                options: Optional[torch.Tensor] = None,
                level: str = "both") -> Dict[str, torch.Tensor]:
        """
        Forward pass through hierarchical critic.
        
        Args:
            observations: Batch of observations
            actions: Batch of actions (for action-level critic)
            options: Batch of options (for action-level critic)
            level: Which level to evaluate ("option", "action", or "both")
            
        Returns:
            Dictionary with option_values and/or action_values
        """
        result = {}
        
        if level in ["option", "both"]:
            option_output = self.option_critic(observations)
            result["option_values"] = option_output["values"]
        
        if level in ["action", "both"]:
            if actions is None or options is None:
                raise ValueError("Actions and options required for action-level critic")
            action_output = self.action_critic(observations, actions, options)
            result["action_values"] = action_output["values"]
        
        return result
    
    def compute_option_loss(self, observations: torch.Tensor,
                           option_returns: torch.Tensor) -> torch.Tensor:
        """Compute loss for option-level critic."""
        option_output = self.option_critic(observations)
        option_values = option_output["values"]
        
        if option_values.dim() > 1:
            option_values = option_values.mean(dim=-1)  # Average over ensemble
        
        return self.option_critic.compute_loss(option_values, option_returns)
    
    def compute_action_loss(self, observations: torch.Tensor,
                           actions: torch.Tensor,
                           options: torch.Tensor,
                           action_returns: torch.Tensor) -> torch.Tensor:
        """Compute loss for action-level critic."""
        action_output = self.action_critic(observations, actions, options)
        action_values = action_output["values"]
        
        if action_values.dim() > 1:
            # For ensemble critics, compute loss for each critic
            losses = []
            for i in range(action_values.shape[-1]):
                loss = self.action_critic.compute_loss(action_values[:, i], action_returns)
                losses.append(loss)
            return torch.stack(losses).mean()
        else:
            return self.action_critic.compute_loss(action_values, action_returns)


class SharedCritic(nn.Module):
    """
    Shared critic for multi-agent settings.
    
    Takes observations and actions from all agents to compute centralized values.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.num_agents = config.get("num_agents", 4)
        self.obs_dim = config.get("obs_dim", 128)
        self.action_dim = config.get("action_dim", 4)
        
        # Shared critic configuration
        shared_config = config.copy()
        shared_config.update({
            "obs_dim": self.num_agents * self.obs_dim,  # Concatenated observations
            "action_dim": self.num_agents * self.action_dim,  # Concatenated actions
            "critic_type": "action_value"
        })
        
        self.critic = CriticNetwork(shared_config)
        
        logger.info(f"SharedCritic initialized: {self.num_agents} agents, "
                   f"total_obs_dim={shared_config['obs_dim']}, "
                   f"total_action_dim={shared_config['action_dim']}")
    
    def forward(self, observations: Dict[str, torch.Tensor],
                actions: Dict[str, torch.Tensor],
                agent_id: Optional[str] = None) -> torch.Tensor:
        """
        Forward pass through shared critic.
        
        Args:
            observations: Dict mapping agent_id to observations
            actions: Dict mapping agent_id to actions
            agent_id: Specific agent to compute value for (if None, return for all)
            
        Returns:
            Critic values for specified agent or all agents
        """
        # Concatenate all observations and actions
        obs_list = [observations[f"agent_{i}"] for i in range(self.num_agents)]
        action_list = [actions[f"agent_{i}"] for i in range(self.num_agents)]
        
        global_obs = torch.cat(obs_list, dim=-1)
        global_actions = torch.cat(action_list, dim=-1)
        
        # Forward through critic
        output = self.critic(global_obs, global_actions)
        values = output["values"]
        
        if agent_id is not None:
            # Return value for specific agent (assuming single output per agent)
            agent_idx = int(agent_id.split("_")[1])
            return values  # In practice, might need agent-specific indexing
        
        return values