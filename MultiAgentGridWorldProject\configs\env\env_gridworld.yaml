# GridWorld Environment Configuration
env_name: "GridWorld"

# Spatial configuration
map_size: [12, 12]  # Larger for more complex spatial reasoning
num_agents: 4
obstacles: 8        # More obstacles for deadlock scenarios
wall_density: 0.15  # Percentage of walls in environment

# Dynamic elements
dynamic_events: true
hazard_frequency: 0.08
moving_obstacles: true
obstacle_speed: 0.1

# Goals and rewards
goal_locations: [[1,1], [10,10], [1,10], [10,1]]  # Corner goals for spatial distribution
multi_goal: true
goal_reward: 10.0
step_penalty: -0.01
collision_penalty: -0.5
cooperation_bonus: 2.0  # Reward for coordinated behavior

# Episode configuration
episode_limit: 250
max_steps_without_progress: 50

# Observation and action spaces
action_space: "discrete"  # [up, down, left, right, stay]
observation_type: "partial"
observation_radius: 3
include_agent_positions: true
include_goal_info: true
include_communication: true

# Rendering and debugging
render_mode: "rgb_array"
render_fps: 10
save_trajectories: true

# Environment seed
seed: 0