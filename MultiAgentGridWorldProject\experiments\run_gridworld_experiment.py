"""
GridWorld Hierarchical RL Experiment

Example script demonstrating how to run a complete hierarchical multi-agent
RL experiment using the training system.
"""

import os
import sys
import argparse
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.training.runner import TrainingRunner
from src.environment.grid_world import GridWorldEnv

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_experiment_config(args):
    """Create experiment configuration from command line arguments."""
    
    # Environment configuration
    env_config = {
        "grid_size": (args.grid_size, args.grid_size),
        "num_agents": args.num_agents,
        "max_steps": args.max_episode_steps,
        "reward_type": args.reward_type,
        "obstacle_density": args.obstacle_density,
        "goal_reward": args.goal_reward,
        "step_penalty": args.step_penalty
    }
    
    # Agent configuration
    agent_config = {
        "policy": {
            "num_options": args.num_options,
            "obs_dim": args.obs_dim,
            "action_dim": 4,  # GridWorld has 4 actions (up, down, left, right)
            "hidden_dim": args.hidden_dim,
            "num_layers": args.num_layers,
            "activation": args.activation
        },
        "use_communication": args.use_communication,
        "communication": {
            "num_agents": args.num_agents,
            "communication_dim": args.comm_dim,
            "d_model": args.comm_dim,
            "num_heads": args.attention_heads,
            "comm_layers": args.comm_layers
        } if args.use_communication else {},
        "max_option_duration": args.max_option_duration,
        "option_frequency": args.option_frequency,
        "buffer_capacity": args.buffer_capacity,
        "batch_size": args.batch_size,
        "deadlock_threshold": args.deadlock_threshold
    }
    
    # Training configuration
    training_config = {
        "learning_rate": args.learning_rate,
        "gamma": args.gamma,
        "gae_lambda": args.gae_lambda,
        "clip_grad_norm": args.clip_grad_norm,
        "entropy_coef": args.entropy_coef,
        "value_coef": args.value_coef,
        "option_entropy_coef": args.option_entropy_coef,
        "option_critic_coef": args.option_critic_coef,
        "termination_reg": args.termination_reg,
        "communication_coef": args.communication_coef,
        "use_mixed_precision": args.use_mixed_precision,
        "separate_optimizers": args.separate_optimizers,
        "horizon": args.steps_per_epoch,
        "use_early_stopping": args.use_early_stopping,
        "early_stopping_patience": args.early_stopping_patience,
        "early_stopping_min_delta": args.early_stopping_min_delta
    }
    
    # Runner configuration
    runner_config = {
        "num_epochs": args.num_epochs,
        "steps_per_epoch": args.steps_per_epoch,
        "eval_frequency": args.eval_frequency,
        "eval_episodes": args.eval_episodes,
        "save_frequency": args.save_frequency,
        "results_dir": args.results_dir,
        "experiment_name": args.experiment_name,
        "multi_agent": args.num_agents > 1
    }
    
    return {
        "env_config": env_config,
        "agent_config": agent_config,
        "training_config": training_config,
        "runner_config": runner_config
    }


def main():
    """Main experiment function."""
    parser = argparse.ArgumentParser(description="GridWorld Hierarchical RL Experiment")
    
    # Environment arguments
    parser.add_argument("--grid-size", type=int, default=8, help="Grid size (NxN)")
    parser.add_argument("--num-agents", type=int, default=2, help="Number of agents")
    parser.add_argument("--max-episode-steps", type=int, default=100, help="Max steps per episode")
    parser.add_argument("--reward-type", type=str, default="sparse", choices=["sparse", "dense"], help="Reward type")
    parser.add_argument("--obstacle-density", type=float, default=0.1, help="Obstacle density")
    parser.add_argument("--goal-reward", type=float, default=10.0, help="Goal reward")
    parser.add_argument("--step-penalty", type=float, default=-0.01, help="Step penalty")
    
    # Agent arguments
    parser.add_argument("--num-options", type=int, default=4, help="Number of options")
    parser.add_argument("--obs-dim", type=int, default=None, help="Observation dimension (auto-computed if None)")
    parser.add_argument("--hidden-dim", type=int, default=128, help="Hidden layer dimension")
    parser.add_argument("--num-layers", type=int, default=2, help="Number of layers")
    parser.add_argument("--activation", type=str, default="relu", choices=["relu", "tanh", "gelu"], help="Activation function")
    parser.add_argument("--max-option-duration", type=int, default=10, help="Maximum option duration")
    parser.add_argument("--option-frequency", type=int, default=4, help="Option selection frequency")
    parser.add_argument("--buffer-capacity", type=int, default=10000, help="Experience buffer capacity")
    parser.add_argument("--batch-size", type=int, default=32, help="Training batch size")
    parser.add_argument("--deadlock-threshold", type=int, default=50, help="Deadlock detection threshold")
    
    # Communication arguments
    parser.add_argument("--use-communication", action="store_true", help="Enable multi-agent communication")
    parser.add_argument("--comm-dim", type=int, default=64, help="Communication dimension")
    parser.add_argument("--attention-heads", type=int, default=4, help="Number of attention heads")
    parser.add_argument("--comm-layers", type=int, default=2, help="Number of communication layers")
    
    # Training arguments
    parser.add_argument("--learning-rate", type=float, default=3e-4, help="Learning rate")
    parser.add_argument("--gamma", type=float, default=0.99, help="Discount factor")
    parser.add_argument("--gae-lambda", type=float, default=0.95, help="GAE lambda")
    parser.add_argument("--clip-grad-norm", type=float, default=1.0, help="Gradient clipping norm")
    parser.add_argument("--entropy-coef", type=float, default=0.01, help="Entropy coefficient")
    parser.add_argument("--value-coef", type=float, default=0.5, help="Value loss coefficient")
    parser.add_argument("--option-entropy-coef", type=float, default=0.01, help="Option entropy coefficient")
    parser.add_argument("--option-critic-coef", type=float, default=1.0, help="Option critic coefficient")
    parser.add_argument("--termination-reg", type=float, default=0.01, help="Termination regularization")
    parser.add_argument("--communication-coef", type=float, default=0.1, help="Communication loss coefficient")
    parser.add_argument("--use-mixed-precision", action="store_true", help="Use mixed precision training")
    parser.add_argument("--separate-optimizers", action="store_true", default=True, help="Use separate optimizers")
    
    # Training schedule arguments
    parser.add_argument("--num-epochs", type=int, default=1000, help="Number of training epochs")
    parser.add_argument("--steps-per-epoch", type=int, default=2048, help="Steps per epoch")
    parser.add_argument("--eval-frequency", type=int, default=10, help="Evaluation frequency")
    parser.add_argument("--eval-episodes", type=int, default=10, help="Number of evaluation episodes")
    parser.add_argument("--save-frequency", type=int, default=50, help="Checkpoint save frequency")
    
    # Early stopping arguments
    parser.add_argument("--use-early-stopping", action="store_true", help="Use early stopping")
    parser.add_argument("--early-stopping-patience", type=int, default=50, help="Early stopping patience")
    parser.add_argument("--early-stopping-min-delta", type=float, default=0.01, help="Early stopping min delta")
    
    # Output arguments
    parser.add_argument("--results-dir", type=str, default="results", help="Results directory")
    parser.add_argument("--experiment-name", type=str, default="gridworld_hrl", help="Experiment name")
    
    # Logging arguments
    parser.add_argument("--log-level", type=str, default="INFO", choices=["DEBUG", "INFO", "WARNING", "ERROR"])
    parser.add_argument("--use-wandb", action="store_true", help="Use Weights & Biases logging")
    parser.add_argument("--wandb-project", type=str, default="hierarchical-rl", help="W&B project name")
    
    args = parser.parse_args()
    
    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # Auto-compute observation dimension if not provided
    if args.obs_dim is None:
        # GridWorld observation includes: grid state + agent position + goal position
        args.obs_dim = args.grid_size * args.grid_size + 2 + 2  # Simplified calculation
    
    logger.info("Starting GridWorld Hierarchical RL Experiment")
    logger.info(f"Configuration: {args}")
    
    # Create experiment configuration
    config = create_experiment_config(args)
    
    # Initialize training runner
    runner = TrainingRunner(config["runner_config"])
    
    # Setup environment
    logger.info("Setting up environment...")
    runner.setup_environment(config["env_config"])
    
    # Setup agents
    logger.info("Setting up agents...")
    runner.setup_agents(config["agent_config"])
    
    # Setup training
    logger.info("Setting up training...")
    runner.setup_training(config["training_config"])
    
    # Add W&B logging if requested
    if args.use_wandb:
        try:
            from src.training.callbacks import WandbLogger
            wandb_logger = WandbLogger(
                project=args.wandb_project,
                name=f"{args.experiment_name}_{args.num_agents}agents",
                config=config,
                tags=["hierarchical-rl", "multi-agent", "gridworld"]
            )
            runner.callback_manager.callbacks.append(wandb_logger)
            logger.info("W&B logging enabled")
        except ImportError:
            logger.warning("W&B not available, skipping W&B logging")
    
    # Run training
    logger.info("Starting training...")
    try:
        results = runner.train()
        
        # Print final results
        logger.info("Training completed successfully!")
        logger.info(f"Best reward: {results['best_reward']:.3f}")
        logger.info(f"Final evaluation: {results['final_evaluation']}")
        logger.info(f"Training time: {results['training_time']:.2f} seconds")
        logger.info(f"Results saved to: {runner.run_dir}")
        
        return 0
        
    except KeyboardInterrupt:
        logger.info("Training interrupted by user")
        return 1
    except Exception as e:
        logger.error(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)