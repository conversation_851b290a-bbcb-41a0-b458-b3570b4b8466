"""
Rollout Collection

Collects training data from environment-agent interaction.
Handles option intervals, termination, and multi-agent coordination.
"""

import torch
import numpy as np
from typing import Dict, List, Any, Optional, Tuple, Union
from collections import defaultdict, deque
import logging

from ..controllers.base_agent import BaseAgent
from ..controllers.hierarchical_agent import HierarchicalAgent

logger = logging.getLogger(__name__)


class RolloutCollector:
    """
    Collects rollouts from single-agent environment interactions.
    
    Tracks observations, options, actions, rewards, and termination signals
    for hierarchical RL training.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Rollout parameters
        self.horizon = config.get("horizon", 1000)
        self.track_attention = config.get("track_attention", False)
        self.track_options = config.get("track_options", True)
        
        # Storage
        self.reset_storage()
        
        logger.info(f"RolloutCollector initialized: horizon={self.horizon}")
    
    def reset_storage(self):
        """Reset rollout storage."""
        self.observations = []
        self.actions = []
        self.rewards = []
        self.dones = []
        self.next_observations = []
        
        # Hierarchical fields
        self.options = []
        self.option_durations = []
        self.terminations = []
        self.option_log_probs = []
        self.action_log_probs = []
        self.values = []
        
        # Optional tracking
        self.attention_weights = []
        self.communication_features = []
        
        # Episode tracking
        self.episode_rewards = []
        self.episode_lengths = []
        self.current_episode_reward = 0.0
        self.current_episode_length = 0
    
    def collect_rollout(self, env, agent: BaseAgent, 
                       max_steps: Optional[int] = None) -> Dict[str, Any]:
        """
        Collect a rollout from environment-agent interaction.
        
        Args:
            env: Environment instance
            agent: Agent instance
            max_steps: Maximum steps to collect (defaults to horizon)
            
        Returns:
            Dictionary containing rollout data with proper tensor shapes
        """
        if max_steps is None:
            max_steps = self.horizon
        
        self.reset_storage()
        
        # Reset environment and agent
        reset_result = env.reset()
        if isinstance(reset_result, tuple):
            observation, info = reset_result
        else:
            observation = reset_result
        agent.reset()
        
        step_count = 0
        episode_count = 0
        
        while step_count < max_steps:
            # Get action from agent
            if isinstance(observation, dict):
                # Multi-agent environment - get observation for the agent
                agent_obs = next(iter(observation.values()))
                action_dict = agent.act(agent_obs)
                action = action_dict["action"]
                # Convert to multi-agent action format
                agent_id = next(iter(observation.keys()))
                action_for_env = {agent_id: action}
                # Store the actual agent observation for processing
                obs_to_store = agent_obs
            else:
                action_dict = agent.act(observation)
                action = action_dict["action"]
                action_for_env = action
                obs_to_store = observation
            
            # Store current observation
            self.observations.append(self._process_observation(obs_to_store))
            
            # Store hierarchical information if available
            if isinstance(agent, HierarchicalAgent):
                self.options.append(agent.option_tracker.current_option)
                self.option_durations.append(agent.option_tracker.option_duration)
                
                # Get policy outputs for logging
                if isinstance(observation, dict):
                    agent_obs = next(iter(observation.values()))
                    obs_tensor = agent._dict_to_tensor(agent_obs)
                else:
                    obs_tensor = agent._dict_to_tensor(observation)
                with torch.no_grad():
                    option_output = agent.policy.option_policy(obs_tensor.unsqueeze(0))
                    option_log_prob = option_output["option_log_probs"].squeeze(0).item()
                    self.option_log_probs.append(option_log_prob)
                    
                    # Get worker policy outputs
                    option_tensor = torch.tensor([agent.option_tracker.current_option], dtype=torch.long)
                    worker_output = agent.policy.worker_policy(obs_tensor.unsqueeze(0), option_tensor)
                    action_log_prob = worker_output["action_log_probs"].squeeze(0).item()
                    value = worker_output["values"].squeeze(0).item()
                    
                    self.action_log_probs.append(action_log_prob)
                    self.values.append(value)
                    
                    # Get termination probability
                    termination_output = agent.policy.termination_fn(obs_tensor.unsqueeze(0), option_tensor)
                    termination_prob = termination_output["termination_probs"].squeeze(0).item()
                    self.terminations.append(termination_prob)
            else:
                # Default values for non-hierarchical agents
                self.options.append(0)
                self.option_durations.append(1)
                self.option_log_probs.append(0.0)
                self.action_log_probs.append(0.0)
                self.values.append(0.0)
                self.terminations.append(0.0)
            
            # Store action
            self.actions.append(action)
            
            # Step environment
            step_result = env.step(action_for_env)
            if len(step_result) == 5:
                # New gym format: obs, reward, terminated, truncated, info
                next_observation, reward, terminated, truncated, info = step_result
                done = terminated or truncated
            else:
                # Old gym format: obs, reward, done, info
                next_observation, reward, done, info = step_result
            
            # Process reward and done for single agent mode
            if isinstance(reward, dict):
                agent_reward = next(iter(reward.values()))
                agent_done = next(iter(done.values()))
            else:
                agent_reward = reward
                agent_done = done
            
            # Store transition
            self.rewards.append(agent_reward)
            self.dones.append(agent_done)
            # Process next observation (extract from dict if needed)
            if isinstance(next_observation, dict):
                next_agent_obs = next(iter(next_observation.values()))
                self.next_observations.append(self._process_observation(next_agent_obs))
            else:
                self.next_observations.append(self._process_observation(next_observation))
            
            # Update agent
            agent.step(reward=agent_reward, done=agent_done)
            
            # Track episode statistics
            self.current_episode_reward += agent_reward
            self.current_episode_length += 1
            step_count += 1
            
            # Handle episode termination
            if agent_done:
                self.episode_rewards.append(self.current_episode_reward)
                self.episode_lengths.append(self.current_episode_length)
                
                episode_count += 1
                self.current_episode_reward = 0.0
                self.current_episode_length = 0
                
                # Reset for next episode if we haven't reached max_steps
                if step_count < max_steps:
                    reset_result = env.reset()
                    if isinstance(reset_result, tuple):
                        observation, info = reset_result
                    else:
                        observation = reset_result
                    agent.reset()
                else:
                    break
            else:
                observation = next_observation
        
        # Convert to structured rollout data
        rollout_data = self._create_rollout_batch()
        
        logger.info(f"Collected rollout: {step_count} steps, {episode_count} episodes, "
                   f"avg_reward={np.mean(self.episode_rewards) if self.episode_rewards else 0:.3f}")
        
        return rollout_data
    
    def _process_observation(self, observation: Any) -> torch.Tensor:
        """Process observation into tensor format."""
        if isinstance(observation, dict):
            # Handle multi-agent observations - take first agent for single-agent mode
            if len(observation) == 1:
                # Single agent case - extract the observation
                agent_obs = next(iter(observation.values()))
                return self._process_single_observation(agent_obs)
            else:
                # Multi-agent case - concatenate all observations
                obs_parts = []
                for key in sorted(observation.keys()):
                    value = observation[key]
                    obs_parts.append(self._process_single_observation(value))
                return torch.cat(obs_parts)
        else:
            return self._process_single_observation(observation)
    
    def _process_single_observation(self, observation: Any) -> torch.Tensor:
        """Process a single observation into tensor format."""
        if isinstance(observation, torch.Tensor):
            return observation.flatten()
        elif isinstance(observation, np.ndarray):
            return torch.from_numpy(observation).flatten()
        elif isinstance(observation, dict):
            # Nested dict - concatenate values
            obs_parts = []
            for key in sorted(observation.keys()):
                value = observation[key]
                if isinstance(value, torch.Tensor):
                    obs_parts.append(value.flatten())
                elif isinstance(value, np.ndarray):
                    obs_parts.append(torch.from_numpy(value).flatten())
                else:
                    obs_parts.append(torch.tensor([value]).float())
            return torch.cat(obs_parts)
        else:
            return torch.tensor([observation]).float()
    
    def _create_rollout_batch(self) -> Dict[str, torch.Tensor]:
        """Create structured batch from collected data."""
        batch_size = len(self.observations)
        
        if batch_size == 0:
            return {}
        
        # Stack observations
        obs_tensor = torch.stack(self.observations)
        next_obs_tensor = torch.stack(self.next_observations)
        
        batch = {
            "observations": obs_tensor,
            "actions": torch.tensor(self.actions, dtype=torch.long),
            "rewards": torch.tensor(self.rewards, dtype=torch.float32),
            "dones": torch.tensor(self.dones, dtype=torch.bool),
            "next_observations": next_obs_tensor,
            
            # Hierarchical fields
            "options": torch.tensor(self.options, dtype=torch.long),
            "option_durations": torch.tensor(self.option_durations, dtype=torch.long),
            "terminations": torch.tensor(self.terminations, dtype=torch.float32),
            "option_log_probs": torch.tensor(self.option_log_probs, dtype=torch.float32),
            "action_log_probs": torch.tensor(self.action_log_probs, dtype=torch.float32),
            "values": torch.tensor(self.values, dtype=torch.float32),
            
            # Episode statistics
            "episode_rewards": self.episode_rewards.copy(),
            "episode_lengths": self.episode_lengths.copy(),
            "batch_size": batch_size
        }
        
        # Validate shapes
        expected_shape = (batch_size,)
        for key, tensor in batch.items():
            if isinstance(tensor, torch.Tensor) and tensor.dim() > 0:
                assert tensor.shape[0] == batch_size, f"Shape mismatch for {key}: {tensor.shape} vs expected batch_size {batch_size}"
        
        return batch


class MultiAgentRolloutCollector:
    """
    Collects rollouts from multi-agent environment interactions.
    
    Handles coordination, communication, and synchronized rollouts
    across multiple agents.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Multi-agent parameters
        self.num_agents = config.get("num_agents", 2)
        self.horizon = config.get("horizon", 1000)
        self.use_communication = config.get("use_communication", False)
        
        # Storage per agent
        self.agent_data = {
            f"agent_{i}": defaultdict(list) 
            for i in range(self.num_agents)
        }
        
        # Global episode tracking
        self.episode_rewards = []
        self.episode_lengths = []
        self.current_episode_reward = 0.0
        self.current_episode_length = 0
        
        logger.info(f"MultiAgentRolloutCollector initialized: {self.num_agents} agents, "
                   f"horizon={self.horizon}, communication={self.use_communication}")
    
    def reset_storage(self):
        """Reset rollout storage for all agents."""
        for agent_id in self.agent_data:
            self.agent_data[agent_id] = defaultdict(list)
        
        self.episode_rewards = []
        self.episode_lengths = []
        self.current_episode_reward = 0.0
        self.current_episode_length = 0
    
    def collect_rollout(self, env, agents: Dict[str, BaseAgent], 
                       max_steps: Optional[int] = None) -> Dict[str, Any]:
        """
        Collect multi-agent rollout.
        
        Args:
            env: Multi-agent environment
            agents: Dictionary of agent_id -> agent
            max_steps: Maximum steps to collect
            
        Returns:
            Dictionary containing multi-agent rollout data
        """
        if max_steps is None:
            max_steps = self.horizon
        
        self.reset_storage()
        
        # Reset environment and agents
        reset_result = env.reset()
        if isinstance(reset_result, tuple):
            observations, info = reset_result
        else:
            observations = reset_result
        
        # Handle case where observations might not be a dict
        if not isinstance(observations, dict):
            # Convert to dict format for consistency
            if hasattr(env, 'agents'):
                agent_ids = env.agents
            else:
                agent_ids = [f"agent_{i}" for i in range(len(agents))]
            
            if isinstance(observations, (list, tuple)):
                observations = {agent_id: obs for agent_id, obs in zip(agent_ids, observations)}
            else:
                # Single observation for all agents
                observations = {agent_id: observations for agent_id in agent_ids}
        
        for agent in agents.values():
            agent.reset()
        
        step_count = 0
        episode_count = 0
        
        while step_count < max_steps:
            # Store observations
            for agent_id, obs in observations.items():
                if agent_id in agents:
                    processed_obs = self._process_observation(obs)
                    self.agent_data[agent_id]["observations"].append(processed_obs)
            
            # Handle communication if enabled
            if self.use_communication:
                self._handle_communication(agents, observations)
            
            # Get actions from all agents
            actions = {}
            for agent_id, agent in agents.items():
                if agent_id in observations:
                    action_dict = agent.act(observations[agent_id])
                    actions[agent_id] = action_dict["action"]
                    
                    # Store hierarchical information
                    self._store_agent_data(agent_id, agent, observations[agent_id])
            
            # Step environment
            step_result = env.step(actions)
            if len(step_result) == 5:
                # New gym format: obs, reward, terminated, truncated, info
                next_observations, rewards, terminated, truncated, info = step_result
                dones = {agent_id: terminated.get(agent_id, False) or truncated.get(agent_id, False) 
                        for agent_id in terminated.keys()} if isinstance(terminated, dict) else terminated or truncated
            else:
                # Old gym format: obs, reward, done, info
                next_observations, rewards, dones, info = step_result
            
            # Handle case where results might not be dicts
            if not isinstance(next_observations, dict):
                agent_ids = list(agents.keys())
                if isinstance(next_observations, (list, tuple)):
                    next_observations = {agent_id: obs for agent_id, obs in zip(agent_ids, next_observations)}
                else:
                    next_observations = {agent_id: next_observations for agent_id in agent_ids}
            
            if not isinstance(rewards, dict):
                agent_ids = list(agents.keys())
                if isinstance(rewards, (list, tuple)):
                    rewards = {agent_id: reward for agent_id, reward in zip(agent_ids, rewards)}
                else:
                    rewards = {agent_id: rewards for agent_id in agent_ids}
            
            if not isinstance(dones, dict):
                agent_ids = list(agents.keys())
                if isinstance(dones, (list, tuple)):
                    dones = {agent_id: done for agent_id, done in zip(agent_ids, dones)}
                else:
                    dones = {agent_id: dones for agent_id in agent_ids}
            
            # Store transitions
            total_reward = 0.0
            for agent_id in agents.keys():
                if agent_id in rewards:
                    self.agent_data[agent_id]["actions"].append(actions.get(agent_id, 0))
                    self.agent_data[agent_id]["rewards"].append(rewards[agent_id])
                    self.agent_data[agent_id]["dones"].append(dones.get(agent_id, False))
                    
                    if agent_id in next_observations:
                        next_obs = self._process_observation(next_observations[agent_id])
                        self.agent_data[agent_id]["next_observations"].append(next_obs)
                    
                    total_reward += rewards[agent_id]
                    
                    # Update agent
                    agents[agent_id].step(reward=rewards[agent_id], done=dones.get(agent_id, False))
            
            # Track episode statistics
            self.current_episode_reward += total_reward
            self.current_episode_length += 1
            step_count += 1
            
            # Check if episode is done
            episode_done = all(dones.values()) if dones else False
            
            if episode_done:
                self.episode_rewards.append(self.current_episode_reward)
                self.episode_lengths.append(self.current_episode_length)
                
                episode_count += 1
                self.current_episode_reward = 0.0
                self.current_episode_length = 0
                
                # Reset for next episode
                if step_count < max_steps:
                    reset_result = env.reset()
                    if isinstance(reset_result, tuple):
                        observations, info = reset_result
                    else:
                        observations = reset_result
                    
                    # Handle case where observations might not be a dict
                    if not isinstance(observations, dict):
                        if hasattr(env, 'agents'):
                            agent_ids = env.agents
                        else:
                            agent_ids = list(agents.keys())
                        
                        if isinstance(observations, (list, tuple)):
                            observations = {agent_id: obs for agent_id, obs in zip(agent_ids, observations)}
                        else:
                            observations = {agent_id: observations for agent_id in agent_ids}
                    
                    for agent in agents.values():
                        agent.reset()
                else:
                    break
            else:
                observations = next_observations
        
        # Create structured rollout data
        rollout_data = self._create_multi_agent_batch()
        
        logger.info(f"Collected multi-agent rollout: {step_count} steps, {episode_count} episodes, "
                   f"avg_reward={np.mean(self.episode_rewards) if self.episode_rewards else 0:.3f}")
        
        return rollout_data
    
    def _handle_communication(self, agents: Dict[str, BaseAgent], observations: Dict[str, Any]):
        """Handle multi-agent communication."""
        # Extract communication features
        agent_features = {}
        agent_positions = {}
        
        for agent_id, agent in agents.items():
            if agent_id in observations and hasattr(agent, '_extract_communication_features'):
                features = agent._extract_communication_features(observations[agent_id])
                agent_features[agent_id] = features
                
                # Extract position if available
                if isinstance(observations[agent_id], dict) and "position" in observations[agent_id]:
                    agent_positions[agent_id] = observations[agent_id]["position"]
        
        # Perform communication if we have features
        if agent_features and len(agent_features) > 1:
            # Use first agent's communication module
            first_agent = next(iter(agents.values()))
            if hasattr(first_agent, 'communicate'):
                communication_mask = {agent_id: True for agent_id in agent_features.keys()}
                updated_features = first_agent.communicate(agent_features, agent_positions, communication_mask)
                
                # Store communication features for analysis
                for agent_id in updated_features:
                    if agent_id in self.agent_data:
                        self.agent_data[agent_id]["communication_features"].append(updated_features[agent_id])
    
    def _store_agent_data(self, agent_id: str, agent: BaseAgent, observation: Any):
        """Store agent-specific hierarchical data."""
        if isinstance(agent, HierarchicalAgent):
            self.agent_data[agent_id]["options"].append(agent.option_tracker.current_option)
            self.agent_data[agent_id]["option_durations"].append(agent.option_tracker.option_duration)
            
            # Get policy outputs
            obs_tensor = agent._dict_to_tensor(observation)
            with torch.no_grad():
                option_output = agent.policy.option_policy(obs_tensor.unsqueeze(0))
                self.agent_data[agent_id]["option_log_probs"].append(
                    option_output["option_log_probs"].squeeze(0).item()
                )
                
                option_tensor = torch.tensor([agent.option_tracker.current_option], dtype=torch.long)
                worker_output = agent.policy.worker_policy(obs_tensor.unsqueeze(0), option_tensor)
                self.agent_data[agent_id]["action_log_probs"].append(
                    worker_output["action_log_probs"].squeeze(0).item()
                )
                self.agent_data[agent_id]["values"].append(
                    worker_output["values"].squeeze(0).item()
                )
                
                termination_output = agent.policy.termination_fn(obs_tensor.unsqueeze(0), option_tensor)
                self.agent_data[agent_id]["terminations"].append(
                    termination_output["termination_probs"].squeeze(0).item()
                )
        else:
            # Default values for non-hierarchical agents
            self.agent_data[agent_id]["options"].append(0)
            self.agent_data[agent_id]["option_durations"].append(1)
            self.agent_data[agent_id]["option_log_probs"].append(0.0)
            self.agent_data[agent_id]["action_log_probs"].append(0.0)
            self.agent_data[agent_id]["values"].append(0.0)
            self.agent_data[agent_id]["terminations"].append(0.0)
    
    def _process_observation(self, observation: Any) -> torch.Tensor:
        """Process observation into tensor format."""
        if isinstance(observation, dict):
            obs_parts = []
            for key in sorted(observation.keys()):
                value = observation[key]
                if isinstance(value, torch.Tensor):
                    obs_parts.append(value.flatten())
                elif isinstance(value, np.ndarray):
                    obs_parts.append(torch.from_numpy(value).flatten())
                else:
                    obs_parts.append(torch.tensor([value]).float())
            return torch.cat(obs_parts)
        elif isinstance(observation, torch.Tensor):
            return observation.flatten()
        elif isinstance(observation, np.ndarray):
            return torch.from_numpy(observation).flatten()
        else:
            return torch.tensor([observation]).float()
    
    def _create_multi_agent_batch(self) -> Dict[str, Any]:
        """Create structured batch from multi-agent data."""
        batch = {
            "agents": {},
            "global_stats": {
                "episode_rewards": self.episode_rewards.copy(),
                "episode_lengths": self.episode_lengths.copy(),
                "num_agents": self.num_agents
            }
        }
        
        # Process each agent's data
        for agent_id, data in self.agent_data.items():
            if not data["observations"]:
                continue
                
            batch_size = len(data["observations"])
            
            agent_batch = {
                "observations": torch.stack(data["observations"]),
                "actions": torch.tensor(data["actions"], dtype=torch.long),
                "rewards": torch.tensor(data["rewards"], dtype=torch.float32),
                "dones": torch.tensor(data["dones"], dtype=torch.bool),
                "next_observations": torch.stack(data["next_observations"]),
                
                # Hierarchical fields
                "options": torch.tensor(data["options"], dtype=torch.long),
                "option_durations": torch.tensor(data["option_durations"], dtype=torch.long),
                "terminations": torch.tensor(data["terminations"], dtype=torch.float32),
                "option_log_probs": torch.tensor(data["option_log_probs"], dtype=torch.float32),
                "action_log_probs": torch.tensor(data["action_log_probs"], dtype=torch.float32),
                "values": torch.tensor(data["values"], dtype=torch.float32),
                
                "batch_size": batch_size
            }
            
            # Add communication features if available
            if data["communication_features"]:
                agent_batch["communication_features"] = torch.stack(data["communication_features"])
            
            batch["agents"][agent_id] = agent_batch
        
        return batch