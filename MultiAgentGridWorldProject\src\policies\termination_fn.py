"""
Termination Function (β_z)

Option termination functions that decide when to terminate the current option.
Supports different termination strategies including entropy-based, confidence-based,
and learned termination functions.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import <PERSON><PERSON><PERSON>
from typing import Dict, Tuple, Optional, Any, Union
import numpy as np
import logging

logger = logging.getLogger(__name__)


class TerminationFunction(nn.Module):
    """
    Option termination function β_z.
    
    Decides when to terminate the current option based on state,
    option, and optionally the history of states/actions.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.obs_dim = config.get("obs_dim", 128)
        self.num_options = config.get("num_options", 6)
        self.hidden_dim = config.get("hidden_dim", 256)
        self.num_layers = config.get("num_layers", 2)
        self.activation = config.get("activation", "relu")
        
        # Termination strategy
        self.termination_strategy = config.get("termination_strategy", "entropy")
        self.separate_terminators = config.get("separate_terminators", True)
        
        # Strategy-specific parameters
        self.entropy_threshold = config.get("entropy_threshold", 0.8)
        self.confidence_threshold = config.get("confidence_threshold", 0.9)
        self.fixed_duration = config.get("fixed_duration", 8)
        self.min_duration = config.get("min_duration", 2)
        self.max_duration = config.get("max_duration", 20)
        
        # Option conditioning
        self.option_conditioning = config.get("option_conditioning", True)
        self.option_embed_dim = config.get("option_embed_dim", 32)
        
        # Network architecture
        self._build_network()
        
        # State tracking for duration-based termination
        self.option_durations = {}  # Track duration for each option
        
        logger.info(f"TerminationFunction initialized: strategy={self.termination_strategy}, "
                   f"separate terminators: {self.separate_terminators}")
    
    def _build_network(self):
        """Build the termination network."""
        
        # Activation function
        if self.activation == "relu":
            activation_fn = nn.ReLU
        elif self.activation == "tanh":
            activation_fn = nn.Tanh
        elif self.activation == "gelu":
            activation_fn = nn.GELU
        else:
            activation_fn = nn.ReLU
        
        # Input dimension calculation
        input_dim = self.obs_dim
        if self.option_conditioning:
            input_dim += self.option_embed_dim
        
        if self.separate_terminators:
            # Separate termination networks for each option
            self.termination_networks = nn.ModuleDict()
            
            for option_id in range(self.num_options):
                layers = []
                current_dim = input_dim
                
                # Hidden layers
                for i in range(self.num_layers):
                    layers.extend([
                        nn.Linear(current_dim, self.hidden_dim),
                        activation_fn(),
                        nn.LayerNorm(self.hidden_dim) if self.config.get("use_layer_norm", True) else nn.Identity()
                    ])
                    current_dim = self.hidden_dim
                
                # Termination head (sigmoid output for Bernoulli)
                layers.append(nn.Linear(self.hidden_dim, 1))
                
                self.termination_networks[f"option_{option_id}"] = nn.Sequential(*layers)
        else:
            # Shared termination network for all options
            layers = []
            current_dim = input_dim
            
            # Hidden layers
            for i in range(self.num_layers):
                layers.extend([
                    nn.Linear(current_dim, self.hidden_dim),
                    activation_fn(),
                    nn.LayerNorm(self.hidden_dim) if self.config.get("use_layer_norm", True) else nn.Identity()
                ])
                current_dim = self.hidden_dim
            
            self.shared_feature_extractor = nn.Sequential(*layers)
            
            # Termination head
            self.termination_head = nn.Linear(self.hidden_dim, self.num_options)
        
        # Option embeddings
        if self.option_conditioning:
            self.option_embeddings = nn.Embedding(self.num_options, self.option_embed_dim)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                if self.config.get("use_orthogonal_init", True):
                    nn.init.orthogonal_(module.weight)
                else:
                    nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0.0, 0.1)
    
    def forward(self, observations: torch.Tensor,
                options: torch.Tensor,
                option_durations: Optional[torch.Tensor] = None,
                worker_entropy: Optional[torch.Tensor] = None,
                worker_confidence: Optional[torch.Tensor] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass for termination decision.
        
        Args:
            observations: Batch of observations [batch_size, obs_dim]
            options: Current options [batch_size]
            option_durations: Duration of current options [batch_size]
            worker_entropy: Entropy from worker policy [batch_size]
            worker_confidence: Confidence from worker policy [batch_size]
            
        Returns:
            Dictionary containing:
                - termination_probs: Termination probabilities [batch_size]
                - should_terminate: Boolean termination decisions [batch_size]
                - termination_logits: Raw termination logits [batch_size]
        """
        batch_size = observations.shape[0]
        
        if self.termination_strategy == "fixed":
            # Fixed duration termination
            return self._fixed_termination(options, option_durations)
        
        elif self.termination_strategy == "entropy":
            # Entropy-based termination
            return self._entropy_termination(observations, options, worker_entropy)
        
        elif self.termination_strategy == "confidence":
            # Confidence-based termination
            return self._confidence_termination(observations, options, worker_confidence)
        
        elif self.termination_strategy == "learned":
            # Learned termination function
            return self._learned_termination(observations, options)
        
        else:
            # Default to learned termination
            return self._learned_termination(observations, options)
    
    def _fixed_termination(self, options: torch.Tensor, 
                          option_durations: Optional[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Fixed duration termination strategy."""
        batch_size = options.shape[0]
        
        if option_durations is None:
            # If no duration provided, assume termination
            should_terminate = torch.ones(batch_size, dtype=torch.bool, device=options.device)
            termination_probs = torch.ones(batch_size, device=options.device)
        else:
            should_terminate = option_durations >= self.fixed_duration
            termination_probs = should_terminate.float()
        
        return {
            "termination_probs": termination_probs,
            "should_terminate": should_terminate,
            "termination_logits": torch.logit(termination_probs + 1e-8)
        }
    
    def _entropy_termination(self, observations: torch.Tensor,
                           options: torch.Tensor,
                           worker_entropy: Optional[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Entropy-based termination strategy."""
        batch_size = observations.shape[0]
        
        if worker_entropy is None:
            # If no entropy provided, use learned termination as fallback
            return self._learned_termination(observations, options)
        
        # Terminate when entropy is below threshold (high confidence)
        should_terminate = worker_entropy < self.entropy_threshold
        termination_probs = torch.sigmoid(-10 * (worker_entropy - self.entropy_threshold))
        
        return {
            "termination_probs": termination_probs,
            "should_terminate": should_terminate,
            "termination_logits": torch.logit(termination_probs + 1e-8)
        }
    
    def _confidence_termination(self, observations: torch.Tensor,
                              options: torch.Tensor,
                              worker_confidence: Optional[torch.Tensor]) -> Dict[str, torch.Tensor]:
        """Confidence-based termination strategy."""
        batch_size = observations.shape[0]
        
        if worker_confidence is None:
            # If no confidence provided, use learned termination as fallback
            return self._learned_termination(observations, options)
        
        # Terminate when confidence is above threshold
        should_terminate = worker_confidence > self.confidence_threshold
        termination_probs = torch.sigmoid(10 * (worker_confidence - self.confidence_threshold))
        
        return {
            "termination_probs": termination_probs,
            "should_terminate": should_terminate,
            "termination_logits": torch.logit(termination_probs + 1e-8)
        }
    
    def _learned_termination(self, observations: torch.Tensor,
                           options: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Learned termination function using neural networks."""
        batch_size = observations.shape[0]
        
        # Prepare input
        if self.option_conditioning:
            option_embeds = self.option_embeddings(options)
            network_input = torch.cat([observations, option_embeds], dim=-1)
        else:
            network_input = observations
        
        if self.separate_terminators:
            # Use separate networks for each option
            termination_logits_list = []
            
            for i in range(batch_size):
                option_id = options[i].item()
                single_input = network_input[i:i+1]
                
                # Get termination logit for this option
                termination_logit = self.termination_networks[f"option_{option_id}"](single_input)
                termination_logits_list.append(termination_logit)
            
            termination_logits = torch.cat(termination_logits_list, dim=0).squeeze(-1)
        else:
            # Use shared network
            features = self.shared_feature_extractor(network_input)
            all_termination_logits = self.termination_head(features)
            
            # Select termination logits for current options
            termination_logits = all_termination_logits.gather(1, options.unsqueeze(1)).squeeze(1)
        
        # Convert to probabilities and decisions
        termination_probs = torch.sigmoid(termination_logits)
        
        # Sample termination decisions
        if self.training:
            termination_dist = Bernoulli(probs=termination_probs)
            should_terminate = termination_dist.sample().bool()
        else:
            should_terminate = termination_probs > 0.5
        
        return {
            "termination_probs": termination_probs,
            "should_terminate": should_terminate,
            "termination_logits": termination_logits
        }
    
    def update_option_duration(self, agent_id: str, option_id: int, duration: int):
        """Update option duration tracking."""
        key = f"{agent_id}_option_{option_id}"
        self.option_durations[key] = duration
    
    def get_option_duration(self, agent_id: str, option_id: int) -> int:
        """Get current option duration."""
        key = f"{agent_id}_option_{option_id}"
        return self.option_durations.get(key, 0)
    
    def reset_option_duration(self, agent_id: str, option_id: int):
        """Reset option duration tracking."""
        key = f"{agent_id}_option_{option_id}"
        self.option_durations[key] = 0
    
    def should_force_termination(self, option_durations: torch.Tensor) -> torch.Tensor:
        """Force termination if option has run too long."""
        return option_durations >= self.max_duration
    
    def should_prevent_termination(self, option_durations: torch.Tensor) -> torch.Tensor:
        """Prevent termination if option hasn't run long enough."""
        return option_durations < self.min_duration


class MultiAgentTerminationFunction(nn.Module):
    """
    Multi-agent wrapper for termination functions.
    
    Supports both parameter sharing and individual functions per agent.
    """
    
    def __init__(self, config: Dict[str, Any], num_agents: int):
        super().__init__()
        
        self.config = config
        self.num_agents = num_agents
        self.parameter_sharing = config.get("parameter_sharing", False)
        
        if self.parameter_sharing:
            # Shared termination function for all agents
            self.shared_termination = TerminationFunction(config)
            logger.info(f"MultiAgentTerminationFunction: Parameter sharing enabled")
        else:
            # Individual termination functions per agent
            self.agent_terminations = nn.ModuleDict({
                f"agent_{i}": TerminationFunction(config) 
                for i in range(num_agents)
            })
            logger.info(f"MultiAgentTerminationFunction: Individual functions for {num_agents} agents")
    
    def forward(self, observations: Dict[str, torch.Tensor],
                options: Dict[str, torch.Tensor],
                agent_ids: Optional[list] = None,
                option_durations: Optional[Dict[str, torch.Tensor]] = None,
                worker_entropy: Optional[Dict[str, torch.Tensor]] = None,
                worker_confidence: Optional[Dict[str, torch.Tensor]] = None) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Forward pass for multiple agents.
        
        Args:
            observations: Dict mapping agent_id to observations
            options: Dict mapping agent_id to current options
            agent_ids: List of agent IDs to process (None for all)
            option_durations: Dict mapping agent_id to option durations
            worker_entropy: Dict mapping agent_id to worker entropy
            worker_confidence: Dict mapping agent_id to worker confidence
            
        Returns:
            Dict mapping agent_id to termination results
        """
        if agent_ids is None:
            agent_ids = list(observations.keys())
        
        results = {}
        
        for agent_id in agent_ids:
            if agent_id not in observations or agent_id not in options:
                continue
                
            obs = observations[agent_id]
            opt = options[agent_id]
            
            # Get optional inputs
            dur = option_durations.get(agent_id) if option_durations else None
            ent = worker_entropy.get(agent_id) if worker_entropy else None
            conf = worker_confidence.get(agent_id) if worker_confidence else None
            
            if self.parameter_sharing:
                result = self.shared_termination(obs, opt, dur, ent, conf)
            else:
                if agent_id in self.agent_terminations:
                    result = self.agent_terminations[agent_id](obs, opt, dur, ent, conf)
                else:
                    # Fallback to first agent's termination function
                    first_agent = list(self.agent_terminations.keys())[0]
                    result = self.agent_terminations[first_agent](obs, opt, dur, ent, conf)
            
            results[agent_id] = result
        
        return results