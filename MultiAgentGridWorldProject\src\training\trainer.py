"""
Trainer Module

Responsible for forward pass, loss calculation, and backpropagation.
Handles hierarchical policy updates, critic training, and communication learning.
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.nn.utils import clip_grad_norm_
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import logging
from collections import defaultdict

from ..controllers.hierarchical_agent import HierarchicalAgent
from ..models.critic_network import HierarchicalCritic
from ..models.replay_buffer import HierarchicalReplayBuffer

logger = logging.getLogger(__name__)


class HierarchicalTrainer:
    """
    Trainer for hierarchical reinforcement learning agents.
    
    Handles policy gradients, critic updates, and hierarchical loss computation
    with support for multi-agent communication.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Training parameters
        self.learning_rate = config.get("learning_rate", 3e-4)
        self.gamma = config.get("gamma", 0.99)
        self.gae_lambda = config.get("gae_lambda", 0.95)
        self.clip_grad_norm = config.get("clip_grad_norm", 1.0)
        self.entropy_coef = config.get("entropy_coef", 0.01)
        self.value_coef = config.get("value_coef", 0.5)
        self.option_entropy_coef = config.get("option_entropy_coef", 0.01)
        
        # Hierarchical parameters
        self.option_critic_coef = config.get("option_critic_coef", 1.0)
        self.termination_reg = config.get("termination_reg", 0.01)
        self.communication_coef = config.get("communication_coef", 0.1)
        
        # Optimization
        self.use_mixed_precision = config.get("use_mixed_precision", False)
        self.separate_optimizers = config.get("separate_optimizers", True)
        
        # Initialize components
        self.agents = {}
        self.optimizers = {}
        self.schedulers = {}
        self.scaler = torch.cuda.amp.GradScaler() if self.use_mixed_precision else None
        
        # Metrics tracking
        self.training_metrics = defaultdict(list)
        
        logger.info(f"HierarchicalTrainer initialized: lr={self.learning_rate}, "
                   f"gamma={self.gamma}, mixed_precision={self.use_mixed_precision}")
    
    def setup_agents(self, agents: Dict[str, HierarchicalAgent]):
        """Setup agents and their optimizers."""
        self.agents = agents
        
        for agent_id, agent in agents.items():
            if self.separate_optimizers:
                # Separate optimizers for different components
                self.optimizers[f"{agent_id}_option"] = optim.Adam(
                    agent.policy.option_policy.parameters(), lr=self.learning_rate
                )
                self.optimizers[f"{agent_id}_worker"] = optim.Adam(
                    agent.policy.worker_policy.parameters(), lr=self.learning_rate
                )
                self.optimizers[f"{agent_id}_termination"] = optim.Adam(
                    agent.policy.termination_fn.parameters(), lr=self.learning_rate
                )
                
                # Communication optimizer if enabled
                if agent.use_communication and agent.comm_module is not None:
                    self.optimizers[f"{agent_id}_communication"] = optim.Adam(
                        agent.comm_module.parameters(), lr=self.learning_rate
                    )
            else:
                # Single optimizer for all parameters
                all_params = []
                all_params.extend(agent.policy.option_policy.parameters())
                all_params.extend(agent.policy.worker_policy.parameters())
                all_params.extend(agent.policy.termination_fn.parameters())
                
                if agent.use_communication and agent.comm_module is not None:
                    all_params.extend(agent.comm_module.parameters())
                
                self.optimizers[agent_id] = optim.Adam(all_params, lr=self.learning_rate)
        
        logger.info(f"Setup optimizers for {len(agents)} agents")
    
    def train_step(self, batch: Dict[str, Any]) -> Dict[str, float]:
        """
        Perform a single training step.
        
        Args:
            batch: Batch of rollout data
            
        Returns:
            Dictionary of training metrics
        """
        if "agents" in batch:
            # Multi-agent batch
            return self._train_multi_agent_step(batch)
        else:
            # Single agent batch
            return self._train_single_agent_step(batch)
    
    def _train_single_agent_step(self, batch: Dict[str, Any]) -> Dict[str, float]:
        """Train single agent from batch data."""
        if len(self.agents) != 1:
            raise ValueError("Single agent training requires exactly one agent")
        
        agent_id, agent = next(iter(self.agents.items()))
        
        # Extract batch data
        observations = batch["observations"]
        actions = batch["actions"]
        rewards = batch["rewards"]
        dones = batch["dones"]
        next_observations = batch["next_observations"]
        options = batch["options"]
        option_log_probs = batch["option_log_probs"]
        action_log_probs = batch["action_log_probs"]
        values = batch["values"]
        terminations = batch["terminations"]
        
        batch_size = observations.shape[0]
        
        # Compute advantages and returns
        advantages, returns = self._compute_gae(rewards, values, dones)
        
        # Compute option-level returns
        option_returns = self._compute_option_returns(rewards, options, dones)
        
        metrics = {}
        
        if self.use_mixed_precision:
            with torch.cuda.amp.autocast():
                # Forward pass through policies
                option_output = agent.policy.option_policy(observations)
                worker_output = agent.policy.worker_policy(observations, options)
                termination_output = agent.policy.termination_fn(observations, options)
                
                # Compute losses
                losses = self._compute_losses(
                    option_output, worker_output, termination_output,
                    actions, options, advantages, returns, option_returns,
                    option_log_probs, action_log_probs, terminations
                )
                
                total_loss = sum(losses.values())
                
                # Communication loss if enabled
                if agent.use_communication and agent.comm_module is not None:
                    comm_loss = self._compute_communication_loss(agent, batch)
                    losses["communication_loss"] = comm_loss
                    total_loss += self.communication_coef * comm_loss
        else:
            # Forward pass through policies
            option_output = agent.policy.option_policy(observations)
            worker_output = agent.policy.worker_policy(observations, options)
            termination_output = agent.policy.termination_fn(observations, options)
            
            # Compute losses
            losses = self._compute_losses(
                option_output, worker_output, termination_output,
                actions, options, advantages, returns, option_returns,
                option_log_probs, action_log_probs, terminations
            )
            
            total_loss = sum(losses.values())
            
            # Communication loss if enabled
            if agent.use_communication and agent.comm_module is not None:
                comm_loss = self._compute_communication_loss(agent, batch)
                losses["communication_loss"] = comm_loss
                total_loss += self.communication_coef * comm_loss
        
        # Backward pass
        self._backward_and_step(agent_id, total_loss, losses)
        
        # Update metrics
        for key, value in losses.items():
            metrics[key] = value.item() if isinstance(value, torch.Tensor) else value
        
        metrics["total_loss"] = total_loss.item()
        metrics["advantages_mean"] = advantages.mean().item()
        metrics["returns_mean"] = returns.mean().item()
        
        return metrics
    
    def _train_multi_agent_step(self, batch: Dict[str, Any]) -> Dict[str, float]:
        """Train multiple agents from batch data."""
        all_metrics = {}
        total_losses = {}
        
        # Train each agent
        for agent_id, agent_batch in batch["agents"].items():
            if agent_id not in self.agents:
                continue
            
            agent = self.agents[agent_id]
            
            # Extract agent batch data
            observations = agent_batch["observations"]
            actions = agent_batch["actions"]
            rewards = agent_batch["rewards"]
            dones = agent_batch["dones"]
            options = agent_batch["options"]
            option_log_probs = agent_batch["option_log_probs"]
            action_log_probs = agent_batch["action_log_probs"]
            values = agent_batch["values"]
            terminations = agent_batch["terminations"]
            
            # Compute advantages and returns
            advantages, returns = self._compute_gae(rewards, values, dones)
            option_returns = self._compute_option_returns(rewards, options, dones)
            
            with torch.cuda.amp.autocast() if self.use_mixed_precision else torch.no_grad():
                # Forward pass
                option_output = agent.policy.option_policy(observations)
                worker_output = agent.policy.worker_policy(observations, options)
                termination_output = agent.policy.termination_fn(observations, options)
                
                # Compute losses
                losses = self._compute_losses(
                    option_output, worker_output, termination_output,
                    actions, options, advantages, returns, option_returns,
                    option_log_probs, action_log_probs, terminations
                )
                
                total_loss = sum(losses.values())
                
                # Communication loss
                if agent.use_communication and "communication_features" in agent_batch:
                    comm_loss = self._compute_communication_loss(agent, agent_batch)
                    losses["communication_loss"] = comm_loss
                    total_loss += self.communication_coef * comm_loss
            
            # Store for backward pass
            total_losses[agent_id] = total_loss
            
            # Update metrics
            for key, value in losses.items():
                all_metrics[f"{agent_id}_{key}"] = value.item() if isinstance(value, torch.Tensor) else value
            
            all_metrics[f"{agent_id}_total_loss"] = total_loss.item()
        
        # Backward pass for all agents
        for agent_id, total_loss in total_losses.items():
            self._backward_and_step(agent_id, total_loss, {})
        
        # Compute global metrics
        if batch["global_stats"]["episode_rewards"]:
            all_metrics["global_avg_reward"] = np.mean(batch["global_stats"]["episode_rewards"])
            all_metrics["global_avg_length"] = np.mean(batch["global_stats"]["episode_lengths"])
        
        return all_metrics
    
    def _compute_losses(self, option_output: Dict[str, torch.Tensor],
                       worker_output: Dict[str, torch.Tensor],
                       termination_output: Dict[str, torch.Tensor],
                       actions: torch.Tensor, options: torch.Tensor,
                       advantages: torch.Tensor, returns: torch.Tensor,
                       option_returns: torch.Tensor,
                       old_option_log_probs: torch.Tensor,
                       old_action_log_probs: torch.Tensor,
                       terminations: torch.Tensor) -> Dict[str, torch.Tensor]:
        """Compute all hierarchical losses."""
        
        losses = {}
        
        # Option policy loss (policy gradient)
        option_log_probs = option_output["option_log_probs"]
        option_advantages = self._compute_option_advantages(option_returns, options)
        
        option_policy_loss = -(option_log_probs * option_advantages).mean()
        option_entropy = -(option_output["option_probs"] * torch.log(option_output["option_probs"] + 1e-8)).sum(dim=-1).mean()
        
        losses["option_policy_loss"] = option_policy_loss
        losses["option_entropy_loss"] = -self.option_entropy_coef * option_entropy
        
        # Worker policy loss (policy gradient)
        action_log_probs = worker_output["action_log_probs"]
        worker_policy_loss = -(action_log_probs * advantages).mean()
        
        # Worker value loss
        worker_values = worker_output["values"]
        value_loss = nn.MSELoss()(worker_values, returns)
        
        losses["worker_policy_loss"] = worker_policy_loss
        losses["worker_value_loss"] = self.value_coef * value_loss
        
        # Termination loss
        termination_probs = termination_output["termination_probs"]
        
        # Encourage termination when advantages are low
        termination_targets = (advantages < 0).float()
        termination_loss = nn.BCELoss()(termination_probs, termination_targets)
        
        # Regularization to prevent too frequent termination
        termination_reg = termination_probs.mean()
        
        losses["termination_loss"] = termination_loss
        losses["termination_reg"] = self.termination_reg * termination_reg
        
        return losses
    
    def _compute_communication_loss(self, agent: HierarchicalAgent, 
                                   batch: Dict[str, Any]) -> torch.Tensor:
        """Compute communication loss."""
        if not agent.use_communication or agent.comm_module is None:
            return torch.tensor(0.0)
        
        # Get communication metrics
        comm_metrics = agent.comm_module.get_communication_metrics()
        
        # KL divergence loss for attention regularization
        kl_loss = comm_metrics.get("communication_kl_loss", 0.0)
        
        return torch.tensor(kl_loss)
    
    def _compute_gae(self, rewards: torch.Tensor, values: torch.Tensor, 
                    dones: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """Compute Generalized Advantage Estimation."""
        batch_size = rewards.shape[0]
        advantages = torch.zeros_like(rewards)
        returns = torch.zeros_like(rewards)
        
        gae = 0
        for t in reversed(range(batch_size)):
            if t == batch_size - 1:
                next_value = 0
                next_non_terminal = 1 - dones[t].float()
            else:
                next_value = values[t + 1]
                next_non_terminal = 1 - dones[t].float()
            
            delta = rewards[t] + self.gamma * next_value * next_non_terminal - values[t]
            gae = delta + self.gamma * self.gae_lambda * next_non_terminal * gae
            advantages[t] = gae
            returns[t] = advantages[t] + values[t]
        
        # Normalize advantages
        advantages = (advantages - advantages.mean()) / (advantages.std() + 1e-8)
        
        return advantages, returns
    
    def _compute_option_returns(self, rewards: torch.Tensor, options: torch.Tensor,
                               dones: torch.Tensor) -> torch.Tensor:
        """Compute returns for option-level learning."""
        batch_size = rewards.shape[0]
        option_returns = torch.zeros_like(rewards)
        
        # Simple discounted returns for now
        discounted_return = 0
        for t in reversed(range(batch_size)):
            if dones[t]:
                discounted_return = 0
            discounted_return = rewards[t] + self.gamma * discounted_return
            option_returns[t] = discounted_return
        
        return option_returns
    
    def _compute_option_advantages(self, option_returns: torch.Tensor,
                                  options: torch.Tensor) -> torch.Tensor:
        """Compute advantages for option selection."""
        # Simple baseline: mean return for each option
        unique_options = torch.unique(options)
        option_baselines = {}
        
        for option in unique_options:
            mask = (options == option)
            if mask.sum() > 0:
                option_baselines[option.item()] = option_returns[mask].mean()
        
        advantages = torch.zeros_like(option_returns)
        for i, option in enumerate(options):
            baseline = option_baselines.get(option.item(), 0.0)
            advantages[i] = option_returns[i] - baseline
        
        return advantages
    
    def _backward_and_step(self, agent_id: str, total_loss: torch.Tensor, 
                          losses: Dict[str, torch.Tensor]):
        """Perform backward pass and optimizer step."""
        
        if self.separate_optimizers:
            # Zero gradients for all optimizers
            for key in self.optimizers:
                if key.startswith(agent_id):
                    self.optimizers[key].zero_grad()
        else:
            self.optimizers[agent_id].zero_grad()
        
        # Backward pass
        if self.use_mixed_precision and self.scaler is not None:
            self.scaler.scale(total_loss).backward()
        else:
            total_loss.backward()
        
        # Gradient clipping
        agent = self.agents[agent_id]
        if self.clip_grad_norm > 0:
            if self.separate_optimizers:
                clip_grad_norm_(agent.policy.option_policy.parameters(), self.clip_grad_norm)
                clip_grad_norm_(agent.policy.worker_policy.parameters(), self.clip_grad_norm)
                clip_grad_norm_(agent.policy.termination_fn.parameters(), self.clip_grad_norm)
                if agent.use_communication and agent.comm_module is not None:
                    clip_grad_norm_(agent.comm_module.parameters(), self.clip_grad_norm)
            else:
                all_params = []
                all_params.extend(agent.policy.option_policy.parameters())
                all_params.extend(agent.policy.worker_policy.parameters())
                all_params.extend(agent.policy.termination_fn.parameters())
                if agent.use_communication and agent.comm_module is not None:
                    all_params.extend(agent.comm_module.parameters())
                clip_grad_norm_(all_params, self.clip_grad_norm)
        
        # Optimizer step
        if self.use_mixed_precision and self.scaler is not None:
            if self.separate_optimizers:
                for key in self.optimizers:
                    if key.startswith(agent_id):
                        self.scaler.step(self.optimizers[key])
            else:
                self.scaler.step(self.optimizers[agent_id])
            self.scaler.update()
        else:
            if self.separate_optimizers:
                for key in self.optimizers:
                    if key.startswith(agent_id):
                        self.optimizers[key].step()
            else:
                self.optimizers[agent_id].step()
    
    def get_training_metrics(self) -> Dict[str, float]:
        """Get current training metrics."""
        metrics = {}
        for key, values in self.training_metrics.items():
            if values:
                metrics[f"{key}_mean"] = np.mean(values[-100:])  # Last 100 values
                metrics[f"{key}_std"] = np.std(values[-100:])
        return metrics
    
    def reset_metrics(self):
        """Reset training metrics."""
        self.training_metrics.clear()
    
    def save_checkpoint(self, filepath: str):
        """Save training checkpoint."""
        checkpoint = {
            "config": self.config,
            "optimizers": {key: opt.state_dict() for key, opt in self.optimizers.items()},
            "training_metrics": dict(self.training_metrics)
        }
        
        if self.scaler is not None:
            checkpoint["scaler"] = self.scaler.state_dict()
        
        torch.save(checkpoint, filepath)
        logger.info(f"Saved training checkpoint to {filepath}")
    
    def load_checkpoint(self, filepath: str):
        """Load training checkpoint."""
        checkpoint = torch.load(filepath)
        
        for key, state_dict in checkpoint["optimizers"].items():
            if key in self.optimizers:
                self.optimizers[key].load_state_dict(state_dict)
        
        if "scaler" in checkpoint and self.scaler is not None:
            self.scaler.load_state_dict(checkpoint["scaler"])
        
        if "training_metrics" in checkpoint:
            self.training_metrics.update(checkpoint["training_metrics"])
        
        logger.info(f"Loaded training checkpoint from {filepath}")