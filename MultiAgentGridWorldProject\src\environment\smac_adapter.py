"""
StarCraft Multi-Agent Challenge (SMAC) Adapter

Unified adapter for SMAC environments with enhanced features
for multi-agent research and evaluation.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from gymnasium import spaces
import logging

from .base_env import BaseMultiAgentEnv

logger = logging.getLogger(__name__)

try:
    from smac.env import StarCraft2Env
    SMAC_AVAILABLE = True
except ImportError:
    SMAC_AVAILABLE = False
    logger.warning("SMAC not available. StarCraft environments will not work.")


class SMACAdapter(BaseMultiAgentEnv):
    """
    Adapter for StarCraft Multi-Agent Challenge environments.
    
    Provides unified interface with enhanced reward decomposition,
    visibility tracking, and research-oriented features.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not SMAC_AVAILABLE:
            raise ImportError("SMAC is required for StarCraft environments")
        
        # SMAC configuration
        self.map_name = config.get("map_name", "3m")
        self.difficulty = config.get("difficulty", "7")
        self.game_version = config.get("game_version", "4.10")
        self.step_mul = config.get("step_mul", 8)
        self.replay_dir = config.get("replay_dir", "./assets/replays/")
        self.replay_prefix = config.get("replay_prefix", "sdhrl")
        
        # Reward configuration
        self.use_global_reward = config.get("use_global_reward", True)
        self.reward_scale = config.get("reward_scale", 1.0)
        self.reward_scale_rate = config.get("reward_scale_rate", 200)
        self.reward_death_value = config.get("reward_death_value", 10)
        self.reward_win = config.get("reward_win", 200)
        self.reward_defeat = config.get("reward_defeat", 0)
        self.reward_negative_scale = config.get("reward_negative_scale", 0.5)
        
        # Observation configuration
        self.centralized_state = config.get("centralized_state", True)
        self.obs_allies_only = config.get("obs_allies_only", True)
        self.obs_own_health = config.get("obs_own_health", True)
        self.obs_last_action = config.get("obs_last_action", False)
        self.obs_pathing_grid = config.get("obs_pathing_grid", False)
        self.obs_terrain_height = config.get("obs_terrain_height", False)
        self.obs_timestep_number = config.get("obs_timestep_number", False)
        
        # State configuration
        self.state_last_action = config.get("state_last_action", True)
        self.state_timestep_number = config.get("state_timestep_number", False)
        
        # Action configuration
        self.move_amount = config.get("move_amount", 2)
        self.continuing_episode = config.get("continuing_episode", False)
        
        # Replay configuration
        self.save_replay_episodes = config.get("save_replay_episodes", 10)
        self.log_more_stats = config.get("log_more_stats", True)
        
        # Initialize SMAC environment
        self._create_env()
        self._setup_spaces()
        
        # Tracking variables
        self.unit_health = {}
        self.unit_positions = {}
        self.enemy_health = {}
        self.enemy_positions = {}
        self.last_actions = {}
        self.kill_count = 0
        self.damage_dealt = 0
        self.damage_taken = 0
        self.healing_done = 0
        
        logger.info(f"SMAC {self.map_name} initialized with {self.num_agents} agents")
    
    def _create_env(self):
        """Create the SMAC environment."""
        self.env = StarCraft2Env(
            map_name=self.map_name,
            difficulty=self.difficulty,
            game_version=self.game_version,
            step_mul=self.step_mul,
            continuing_episode=self.continuing_episode,
            obs_all_health=not self.obs_allies_only,
            obs_own_health=self.obs_own_health,
            obs_last_action=self.obs_last_action,
            obs_pathing_grid=self.obs_pathing_grid,
            obs_terrain_height=self.obs_terrain_height,
            obs_timestep_number=self.obs_timestep_number,
            state_last_action=self.state_last_action,
            state_timestep_number=self.state_timestep_number,
            reward_sparse=not self.use_global_reward,
            reward_scale=self.reward_scale,
            reward_scale_rate=self.reward_scale_rate,
            reward_death_value=self.reward_death_value,
            reward_win=self.reward_win,
            reward_defeat=self.reward_defeat,
            reward_negative_scale=self.reward_negative_scale,
            move_amount=self.move_amount,
            replay_dir=self.replay_dir,
            replay_prefix=self.replay_prefix,
            save_replay_episodes=self.save_replay_episodes,
            log_more_stats=self.log_more_stats
        )
        
        # Get environment info
        env_info = self.env.get_env_info()
        self.num_agents = env_info["n_agents"]
        self.episode_limit = env_info["episode_limit"]
        
        # Update agent IDs to match SMAC convention
        self.agent_ids = [f"agent_{i}" for i in range(self.num_agents)]
        self.active_agents = set(self.agent_ids)
        
        logger.info(f"SMAC environment info: {env_info}")
    
    def _setup_spaces(self):
        """Setup action and observation spaces."""
        env_info = self.env.get_env_info()
        
        # Action space
        self.action_space = spaces.Discrete(env_info["n_actions"])
        
        # Observation space
        obs_shape = env_info["obs_shape"]
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(obs_shape,), dtype=np.float32
        )
        
        # State space for centralized critic
        if self.centralized_state:
            state_shape = env_info["state_shape"]
            self.state_space = spaces.Box(
                low=0, high=1, shape=(state_shape,), dtype=np.float32
            )
        
        logger.debug(f"Action space: {self.action_space}")
        logger.debug(f"Observation space: {self.observation_space}")
    
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset environment to initial state."""
        self.current_step = 0
        self.episode_count += 1
        
        # Reset tracking variables
        self.unit_health.clear()
        self.unit_positions.clear()
        self.enemy_health.clear()
        self.enemy_positions.clear()
        self.last_actions.clear()
        self.kill_count = 0
        self.damage_dealt = 0
        self.damage_taken = 0
        self.healing_done = 0
        
        # Reset metrics
        self.episode_metrics = {}
        
        # Reset SMAC environment
        self.env.reset()
        
        # Get initial observations
        observations = self.get_obs()
        
        # Update tracking
        self._update_unit_info()
        
        # Get info
        info = self._get_info()
        
        logger.debug(f"SMAC environment reset, episode {self.episode_count}")
        return observations, info
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Execute one environment step."""
        self.current_step += 1
        self.total_steps += 1
        
        # Convert actions to SMAC format
        action_list = []
        for i in range(self.num_agents):
            agent_id = f"agent_{i}"
            if agent_id in actions and agent_id in self.active_agents:
                action_list.append(actions[agent_id])
            else:
                action_list.append(0)  # No-op action
        
        # Store last actions for tracking
        for i, action in enumerate(action_list):
            self.last_actions[f"agent_{i}"] = action
        
        # Step environment
        reward, terminated, env_info = self.env.step(action_list)
        
        # Update tracking
        self._update_unit_info()
        self._update_battle_stats(env_info)
        
        # Calculate individual rewards
        rewards = self._calculate_individual_rewards(reward, env_info)
        
        # Calculate dones
        dones = {}
        for agent_id in self.get_active_agents():
            # Agent is done if unit is dead or episode terminated
            agent_idx = int(agent_id.split('_')[1])
            unit_alive = self.env.get_unit_by_id(agent_idx).health > 0 if agent_idx < len(self.env.agents) else False
            dones[agent_id] = terminated or not unit_alive
        
        # Get observations
        observations = self.get_obs()
        
        # Update metrics
        step_info = self._calculate_step_metrics(env_info, rewards)
        self._update_metrics(step_info)
        
        # Finalize episode if terminated
        if terminated:
            for agent_id in self.get_active_agents():
                dones[agent_id] = True
            self._finalize_episode_metrics()
        
        # Get info
        info = self._get_info()
        info.update(step_info)
        info.update(env_info)
        
        return observations, rewards, dones, info
    
    def _update_unit_info(self):
        """Update unit health and position information."""
        self.unit_health.clear()
        self.unit_positions.clear()
        self.enemy_health.clear()
        self.enemy_positions.clear()
        
        # Update ally unit info
        for i in range(self.num_agents):
            if i < len(self.env.agents):
                unit = self.env.get_unit_by_id(i)
                if unit is not None:
                    self.unit_health[f"agent_{i}"] = unit.health / unit.health_max
                    self.unit_positions[f"agent_{i}"] = (unit.pos.x, unit.pos.y)
        
        # Update enemy unit info
        for i, enemy in enumerate(self.env.enemies):
            if enemy is not None:
                self.enemy_health[f"enemy_{i}"] = enemy.health / enemy.health_max
                self.enemy_positions[f"enemy_{i}"] = (enemy.pos.x, enemy.pos.y)
    
    def _update_battle_stats(self, env_info: Dict[str, Any]):
        """Update battle statistics from environment info."""
        if "battle_won" in env_info:
            self.episode_metrics["battle_won"] = env_info["battle_won"]
        
        # Track kills, damage, healing if available in env_info
        if "kills" in env_info:
            self.kill_count = env_info["kills"]
        if "damage_dealt" in env_info:
            self.damage_dealt = env_info["damage_dealt"]
        if "damage_taken" in env_info:
            self.damage_taken = env_info["damage_taken"]
        if "healing_done" in env_info:
            self.healing_done = env_info["healing_done"]
    
    def _calculate_individual_rewards(self, global_reward: float, 
                                    env_info: Dict[str, Any]) -> Dict[str, float]:
        """Calculate individual rewards for each agent."""
        rewards = {}
        
        if self.use_global_reward:
            # Shared global reward
            for agent_id in self.get_active_agents():
                rewards[agent_id] = global_reward
        else:
            # Individual reward decomposition
            for i, agent_id in enumerate(self.agent_ids):
                if agent_id in self.active_agents:
                    agent_reward = self._calculate_agent_reward(i, env_info)
                    rewards[agent_id] = agent_reward
                else:
                    rewards[agent_id] = 0.0
        
        return rewards
    
    def _calculate_agent_reward(self, agent_idx: int, env_info: Dict[str, Any]) -> float:
        """Calculate individual agent reward based on actions and outcomes."""
        reward = 0.0
        
        # Health-based reward
        agent_id = f"agent_{agent_idx}"
        if agent_id in self.unit_health:
            health_ratio = self.unit_health[agent_id]
            reward += health_ratio * 0.1  # Small bonus for staying alive
        
        # Action-based rewards
        if agent_id in self.last_actions:
            action = self.last_actions[agent_id]
            
            # Reward for attacking (if action is attack)
            if action > 5:  # Attack actions typically have higher indices
                reward += 0.05
            
            # Small penalty for no-op to encourage action
            if action == 0:
                reward -= 0.01
        
        # Proximity-based cooperation reward
        cooperation_reward = self._calculate_cooperation_reward(agent_idx)
        reward += cooperation_reward
        
        return reward
    
    def _calculate_cooperation_reward(self, agent_idx: int) -> float:
        """Calculate cooperation reward based on unit positioning."""
        agent_id = f"agent_{agent_idx}"
        if agent_id not in self.unit_positions:
            return 0.0
        
        agent_pos = self.unit_positions[agent_id]
        cooperation_score = 0.0
        
        # Reward for staying close to allies
        ally_count = 0
        for other_id, other_pos in self.unit_positions.items():
            if other_id != agent_id:
                distance = np.sqrt((agent_pos[0] - other_pos[0])**2 + (agent_pos[1] - other_pos[1])**2)
                if distance < 5.0:  # Close proximity threshold
                    cooperation_score += 0.01
                    ally_count += 1
        
        # Bonus for maintaining formation
        if ally_count > 0:
            cooperation_score += 0.005 * ally_count
        
        return cooperation_score
    
    def _calculate_step_metrics(self, env_info: Dict[str, Any], 
                              rewards: Dict[str, float]) -> Dict[str, Any]:
        """Calculate step-level metrics."""
        step_info = {}
        
        # Battle metrics
        step_info["kills"] = self.kill_count
        step_info["damage_dealt"] = self.damage_dealt
        step_info["damage_taken"] = self.damage_taken
        step_info["healing_done"] = self.healing_done
        
        # Unit survival metrics
        alive_units = sum(1 for health in self.unit_health.values() if health > 0)
        step_info["units_alive"] = alive_units
        step_info["survival_rate"] = alive_units / self.num_agents
        
        # Enemy metrics
        alive_enemies = sum(1 for health in self.enemy_health.values() if health > 0)
        step_info["enemies_alive"] = alive_enemies
        
        # Formation metrics
        if len(self.unit_positions) > 1:
            positions = list(self.unit_positions.values())
            center = np.mean(positions, axis=0)
            distances = [np.linalg.norm(np.array(pos) - center) for pos in positions]
            step_info["formation_spread"] = np.std(distances)
        
        return step_info
    
    def get_obs(self) -> Dict[str, np.ndarray]:
        """Get observations for all agents."""
        observations = {}
        
        # Get observations from SMAC
        obs_list = self.env.get_obs()
        
        for i, obs in enumerate(obs_list):
            agent_id = f"agent_{i}"
            if agent_id in self.active_agents:
                # Enhance observation with additional features
                enhanced_obs = self._enhance_observation(obs, i)
                observations[agent_id] = enhanced_obs.astype(np.float32)
            else:
                # Provide zero observation for inactive agents
                observations[agent_id] = np.zeros_like(obs, dtype=np.float32)
        
        return observations
    
    def _enhance_observation(self, base_obs: np.ndarray, agent_idx: int) -> np.ndarray:
        """Enhance base observation with additional features."""
        enhanced = base_obs.copy()
        
        # Add relative position information
        agent_id = f"agent_{agent_idx}"
        if agent_id in self.unit_positions:
            agent_pos = np.array(self.unit_positions[agent_id])
            
            # Add relative positions to allies
            ally_positions = []
            for other_id, other_pos in self.unit_positions.items():
                if other_id != agent_id:
                    rel_pos = np.array(other_pos) - agent_pos
                    ally_positions.extend(rel_pos)
            
            # Pad or truncate to fixed size
            max_allies = self.num_agents - 1
            while len(ally_positions) < max_allies * 2:
                ally_positions.append(0.0)
            ally_positions = ally_positions[:max_allies * 2]
            
            enhanced = np.concatenate([enhanced, ally_positions])
        
        # Add battle context
        battle_context = [
            self.kill_count / max(1, len(self.enemy_positions)),  # Kill ratio
            len(self.unit_positions) / self.num_agents,  # Survival ratio
            self.current_step / self.episode_limit  # Episode progress
        ]
        
        enhanced = np.concatenate([enhanced, battle_context])
        
        return enhanced
    
    def get_state(self) -> np.ndarray:
        """Get global state for centralized critic."""
        if self.centralized_state:
            state = self.env.get_state()
            
            # Enhance state with additional global information
            enhanced_state = []
            enhanced_state.extend(state)
            
            # Add global battle statistics
            enhanced_state.extend([
                self.kill_count,
                self.damage_dealt,
                self.damage_taken,
                self.healing_done,
                len(self.unit_positions),  # Alive allies
                len(self.enemy_positions),  # Alive enemies
                self.current_step / self.episode_limit  # Episode progress
            ])
            
            return np.array(enhanced_state, dtype=np.float32)
        else:
            # Construct state from observations
            obs_list = self.env.get_obs()
            return np.concatenate(obs_list).astype(np.float32)
    
    def get_avail_actions(self) -> Dict[str, np.ndarray]:
        """Get available actions for each agent."""
        avail_actions = {}
        avail_actions_list = self.env.get_avail_actions()
        
        for i, avail in enumerate(avail_actions_list):
            agent_id = f"agent_{i}"
            avail_actions[agent_id] = np.array(avail, dtype=np.int32)
        
        return avail_actions
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment."""
        # SMAC doesn't support direct rendering, but replays can be saved
        pass
    
    def close(self):
        """Close the environment."""
        if hasattr(self.env, 'close'):
            self.env.close()
    
    def save_replay(self):
        """Save replay of the current episode."""
        if hasattr(self.env, 'save_replay'):
            self.env.save_replay()
    
    def _get_info(self) -> Dict[str, Any]:
        """Get environment info."""
        info = {
            "step": self.current_step,
            "episode": self.episode_count,
            "map_name": self.map_name,
            "active_agents": len(self.active_agents),
            "unit_health": self.unit_health.copy(),
            "enemy_health": self.enemy_health.copy(),
            "kill_count": self.kill_count,
            "damage_dealt": self.damage_dealt,
            "damage_taken": self.damage_taken,
            "units_alive": len(self.unit_positions),
            "enemies_alive": len(self.enemy_positions)
        }
        
        return info