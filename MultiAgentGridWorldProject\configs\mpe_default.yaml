defaults:
  - env: env_mpe
  - agent: agent_hrl_icp
  - training: training_defaults
  - _self_

# Experiment identification
experiment_name: "mpe_hrl"
experiment_group: "multi_agent"
tags: ["hierarchical", "mpe", "multi_agent", "communication"]

# Environment settings
multi_agent: true
num_agents: 3
seed: 42

# Results and logging
results_dir: "results"
log_level: "INFO"

# Hardware configuration
device: "auto"
mixed_precision: false
num_workers: 4

# Reproducibility
deterministic: false

# Hydra configuration
hydra:
  run:
    dir: ./outputs/${experiment_name}/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: false
  sweep:
    dir: ./multirun/${experiment_name}
    subdir: ${hydra:job.num}