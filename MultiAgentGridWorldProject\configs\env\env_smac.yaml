# StarCraft Multi-Agent Challenge Configuration
env_name: "SMAC"

# Map configuration
map_name: "3m"  # 3 Marines vs 3 Marines
num_agents: 3
difficulty: "7"  # Hard difficulty

# Episode configuration
episode_limit: 120
step_mul: 8  # Number of game steps per environment step
game_version: "4.10"

# Reward configuration
use_global_reward: true
reward_scale: 1.0
reward_scale_rate: 200  # Divide rewards by this factor
reward_death_value: 10
reward_win: 200
reward_defeat: 0
reward_negative_scale: 0.5

# Observation configuration
centralized_state: true
obs_allies_only: true
obs_own_health: true
obs_last_action: false
obs_pathing_grid: false
obs_terrain_height: false
obs_timestep_number: false

# Action configuration
move_amount: 2
continuing_episode: false

# State and observation dimensions
state_last_action: true
state_timestep_number: false

# Replay and debugging
replay_dir: "./assets/replays/"
replay_prefix: "sdhrl"
save_replay_episodes: 10
log_more_stats: true

# Environment seed
seed: 0