# 🧠 MultiAgentGridWorldProject

> **Scalable Decentralized Hierarchical Reinforcement Learning: Advancing Multiagent Coordination in Dynamic Environments**
> *A Novel Attention-Based Framework for Implicit Multiagent Communication*

![GridWorld Demo](visualizations/gridworld_demo.gif)

---

## 📌 Abstract

This repository implements a decentralized hierarchical reinforcement learning (HRL) framework with implicit attention-based communication. The proposed system enables large populations of autonomous agents to coordinate efficiently in **dynamic**, **partially observable**, and **communication-constrained** environments.

By leveraging **high-level temporal abstractions**, **skill-based learning**, and **implicit attention-driven communication**, this framework provides a **scalable**, **robust**, and **adaptable** solution for multiagent systems (MAS). The core design philosophy emphasizes real-world applicability in domains like **swarm robotics**, **autonomous fleets**, **disaster response**, and **smart grids**.

---

## 🧠 Key Features

| Feature | Description |
|--------|-------------|
| ✅ **Hierarchical RL (HRL)** | Agents use multi-level policy structures: **high-level (skills/options)** and **low-level (actions)** |
| 🧭 **Decentralized Execution** | No centralized controller or training—scalable to large agent populations |
| ✨ **Attention-Based Communication** | Agents learn **implicit communication** by selectively attending to others |
| 🔄 **Deadlock Prevention** | Built-in deadlock detection and resolution ensures safe and continuous operation |
| 🌍 **Dynamic Environment Handling** | Environment supports stochasticity, moving targets, changing obstacles, and dynamic events |
| 📊 **Integrated Benchmarks** | Compare against QMIX, MADDPG, IPPO, and more using MPE, SMAC, and GridWorld |
| 🔬 **Evaluation & Visualization Tools** | Built-in logging, metrics, visualizations, and GIF export |
| ⚙️ **Modular Codebase** | Highly modular and extensible Python code organized by agents, env, training, and visualization layers |

---

## 📚 Background & Motivation

Multiagent systems are core to modern AI challenges: coordinating drones, fleets, power systems, or robotic teams. Yet, existing solutions often rely on:
- Centralized training setups (e.g., MADDPG with centralized critics)
- Explicit communication (high bandwidth, brittle under failure)
- Flat policies (lack of temporal abstraction, poor planning)

This project addresses these with a new approach:
- Learn **options** and **skills** hierarchically (temporal abstraction)
- Use **decentralized learning** for scalability and robustness
- Leverage **attention-based implicit communication** for bandwidth-efficient, flexible coordination

---

## 🔍 Architecture Overview

```

+-----------------------------+
|  High-Level Policy (Skill) |
|  - Option Selection        |
|  - Task Decomposition      |
+-----------------------------+
↓
+-----------------------------+
|  Low-Level Policy (Action) |
|  - Executes Skills         |
|  - Learns with PPO/A3C     |
+-----------------------------+
↓
+-----------------------------+
|   Attention Mechanism      |
|   - Selective Info Flow    |
|   - Implicit Coordination  |
+-----------------------------+
↓
+-----------------------------+
|      Dynamic Environment   |
|  - Changing Obstacles      |
|  - Partial Observability   |
|  - Real-World Benchmarks   |
+-----------------------------+

```

---

## 📂 File Structure

```

MultiAgentGridWorldProject/
│
├── benchmarks/               # Run evaluations, baselines, and comparisons
│   └── run_benchmarks.py
│
├── checkpoints/              # Stores trained model weights
│
├── config/                   # YAML configuration for training, agents, environments
│   ├── agent/
│   │   └── default.yaml
│   ├── env/
│   │   └── dynamic_gridworld.yaml
│   ├── evaluation/
│   │   └── default.yaml
│   ├── training/
│   │   └── default.yaml
│   └── main.yaml
│
├── docs/                     # Paper-ready architecture diagrams, API explanations
│
├── environment.yml           # Conda environment
├── requirements.txt          # pip fallback
│
├── logs/                     # TensorBoard logs and training outputs
│
├── results/                  # Evaluation results, metrics, tables
│
├── RUN_PROJECT.md            # Step-by-step how to run everything
│
├── visualizations/           # GIFs, plots, attention heatmaps
│   ├── gridworld_demo.gif
│   ├── communication_patterns.png
│
├── src/                      # Core implementation
│   ├── agents/               # Agents, attention, policies
│   │   ├── agent.py
│   │   ├── attention.py
│   │   ├── networks.py
│   │   ├── deadlock.py
│   │   └── __init__.py
│   ├── environment/          # GridWorld, dynamic events, task definitions
│   │   ├── grid_world.py
│   │   ├── tasks.py
│   │   ├── obstacles.py
│   │   ├── resources.py
│   │   ├── wrappers.py
│   │   └── __init__.py
│   ├── training/             # Lightning trainer, callbacks, loss functions
│   │   ├── lit_model.py
│   │   ├── callbacks.py
│   │   ├── train.py
│   │   └── __init__.py
│   ├── visualization/        # GIFs, attention plots, metrics dashboards
│   │   ├── animator.py
│   │   ├── gif_export.py
│   │   ├── plotter.py
│   │   └── __init__.py
│
├── tests/                    # Unit tests for agents and environments
│   └── test_environment.py
│
└── README.md

```

---

## ⚙️ Installation

### ✅ Option 1: Recommended – Conda

```bash
conda env create -f environment.yml
conda activate sdhrl
```

### 🐍 Option 2: pip

```bash
pip install -r requirements.txt
```

---

## 🚀 Run Training

### Train on Dynamic GridWorld

```bash
python experiments/run_gridworld.py --config-name sdhrl_default
```

### Evaluate a Saved Model

```bash
python src/training/evaluate.py --model-path checkpoints/sdhrl.ckpt
```

---

## 🧪 Benchmarks Included

| Environment           | Agents | Metric                    | Comparison Baselines |
| --------------------- | ------ | ------------------------- | -------------------- |
| GridWorld             | 5–50   | Task completion, reward   | QMIX, PPO, Random    |
| MPE (Coop Navigation) | 3–10   | Reward, collisions        | MADDPG, IPPO, MAPPO  |
| SMAC                  | 8–25   | Win rate, time to win     | QMIX, COMA, QTRAN    |
| CityFlow (Traffic)    | 4–20   | Throughput, congestion    | MA2C, CoLight        |
| IEEE-33 Grid Sim      | 5–30   | Load balancing, stability | DDPG, MARL-ResNet    |

---

## 🖼 Example Visualizations

| GridWorld                              | Communication Attention                        |
| -------------------------------------- | ---------------------------------------------- |
| ![](visualizations/gridworld_demo.gif) | ![](visualizations/communication_patterns.png) |

---

## 📝 Citation

Please cite this repository if you use it in your own research:

```bibtex
@misc{MultiAgentGridWorld2025,
  title={Scalable Decentralized Hierarchical RL: MultiAgent Coordination in Dynamic Environments},
  author={Md Shoaib Uddin Chanda},
  year={2025},
  note={https://github.com/your-repo-link},
}
```

---

## 🙋 Acknowledgements

* [Sutton et al., 1999] — Temporal Abstraction
* [Busoniu et al., 2008] — Multiagent RL Survey
* [Lowe et al., 2017] — MADDPG

---

## 📬 Contact

Developed by **Md Shoaib Uddin Chanda**
For queries, issues, or collaboration: [[<EMAIL>](mailto:<EMAIL>)]

---

## ⭐️ Star this repo if you find it useful!

---