# 🧠 MultiAgentGridWorldProject

> **Scalable Decentralized Hierarchical Reinforcement Learning: Advancing Multiagent Coordination in Dynamic Environments**
> *A Novel Attention-Based Framework for Implicit Multiagent Communication*

![GridWorld Demo](visualizations/gridworld_demo.gif)

---

## 📌 Abstract

This repository implements a decentralized hierarchical reinforcement learning (HRL) framework with implicit attention-based communication. The proposed system enables large populations of autonomous agents to coordinate efficiently in **dynamic**, **partially observable**, and **communication-constrained** environments.

By leveraging **high-level temporal abstractions**, **skill-based learning**, and **implicit attention-driven communication**, this framework provides a **scalable**, **robust**, and **adaptable** solution for multiagent systems (MAS). The core design philosophy emphasizes real-world applicability in domains like **swarm robotics**, **autonomous fleets**, **disaster response**, and **smart grids**.

---

## 🧠 Key Features
___________________________________________________________________________________________________________________________________________________
| **Feature**                              | **Description**                                                                                       |
|------------------------------------------|-------------------------------------------------------------------------------------------------------|
| ✅ **Hierarchical RL (HRL)**            | Agents use multi-level policy structures: **high-level (skills/options)** and **low-level (actions)**  |
| 🧭 **Decentralized Execution**          | No centralized controller or training—scalable to large agent populations                              |
| ✨ **Attention-Based Communication**    | Agents learn **implicit communication** by selectively attending to others                             |
| 🔄 **Deadlock Prevention**              | Built-in deadlock detection and resolution ensures safe and continuous operation                       |
| 🌍 **Dynamic Environment Handling**     | Environment supports stochasticity, moving targets, changing obstacles, and dynamic events             |
| 📊 **Integrated Benchmarks**            | Compare against QMIX, MADDPG, IPPO, and more using MPE, SMAC, and GridWorld                            |
| 🔬 **Evaluation & Visualization Tools** | Built-in logging, metrics, visualizations, and GIF export                                              |
| ⚙️ **Modular Codebase**                 | Highly modular and extensible Python code organized by agents, env, training, and visualization layers |
____________________________________________________________________________________________________________________________________________________

---

## 📚 Background & Motivation

Multiagent systems are core to modern AI challenges: coordinating drones, fleets, power systems, or robotic teams. Yet, existing solutions often rely on:
- Centralized training setups (e.g., MADDPG with centralized critics)
- Explicit communication (high bandwidth, brittle under failure)
- Flat policies (lack of temporal abstraction, poor planning)

This project addresses these with a new approach:
- Learn **options** and **skills** hierarchically (temporal abstraction)
- Use **decentralized learning** for scalability and robustness
- Leverage **attention-based implicit communication** for bandwidth-efficient, flexible coordination

---

## 🔍 Architecture Overview

```

                            +-----------------------------+
                            |  High-Level Policy (Skill) |
                            |  - Option Selection        |
                            |  - Task Decomposition      |
                            +-----------------------------+
                                        ↓
                            +-----------------------------+
                            |  Low-Level Policy (Action) |
                            |  - Executes Skills         |
                            |  - Learns with PPO/A3C     |
                            +-----------------------------+
                                        ↓
                            +-----------------------------+
                            |   Attention Mechanism      |
                            |   - Selective Info Flow    |
                            |   - Implicit Coordination  |
                            +-----------------------------+
                                         ↓
                            +-----------------------------+
                            |      Dynamic Environment   |
                            |  - Changing Obstacles      |
                            |  - Partial Observability   |
                            |  - Real-World Benchmarks   |
                            +-----------------------------+

                                        ```

---

## 📂 File Structure

```
MultiAgentGridWorldProject/
├── README.md                             ← Project overview and documentation
├── RUN_PROJECT.md                        ← Step-by-step execution guide
├── LICENSE                               ← Creative Commons Attribution-NonCommercial 4.0
├── setup.py                              ← Package installation configuration
├── requirements.txt                      ← Python dependencies
├── environment.yml                       ← Conda environment specification
│
├── configs/                              ← All experiment and model configs
│   ├── sdhrl_default.yaml                ← Base config: env, agent, training, eval
│   ├── env_gridworld.yaml                ← GridWorld parameters: size, dynamics
│   ├── env_mpe.yaml                      ← PettingZoo MPE adapter settings
│   ├── env_smac.yaml                     ← SMAC adapter settings
│   ├── agent_hrl_icp.yaml                ← Hierarchical + implicit comm settings
│   └── training_defaults.yaml            ← Trainer: epochs, batch, GPUs, seeds
│
├── src/                                  ← Main library code
│   ├── __init__.py
│   │
│   ├── environment/                      ← Env wrappers, dynamics, failures
│   │   ├── __init__.py
│   │   ├── grid_world.py                 ← Static grid world base
│   │   ├── dynamic_events.py             ← Time‑based obstacles, moving hazards
│   │   ├── mpe_adapter.py                ← PettingZoo MPE → our API
│   │   ├── smac_adapter.py               ← SMAC API wrapper
│   │   └── wrappers.py                   ← TimeLimit, failure injection, normalization
│   │
│   ├── policies/                         ← High‑ and low‑level policy modules
│   │   ├── __init__.py
│   │   ├── option_policy.py              ← π^H: selects options every K steps
│   │   ├── worker_policy.py              ← π^L_z: executes primitive actions
│   │   └── termination_fn.py             ← Option termination criteria
│   │
│   ├── comms/                            ← Implicit attention‑based communication
│   │   ├── __init__.py
│   │   └── graph_attention.py            ← GAT layer, threshold τ, sparsity KL
│   │
│   ├── controllers/                      ← Agent logic tying policies & comms
│   │   ├── __init__.py
│   │   ├── base_agent.py                 ← Abstract interface: act(), learn()
│   │   └── hierarchical_agent.py         ← HRL agent: manager + worker + comm
│   │
│   ├── models/                           ← Neural network definitions
│   │   ├── __init__.py
│   │   ├── option_selector.py            ← MLP/Gumbel‑Softmax for π^H
│   │   ├── attention_module.py           ← Q/K/V projections + message fusion
│   │   ├── critic_network.py             ← Spatially‑discounted local critic
│   │   └── replay_buffer.py              ← Off‑policy + Reset Replay support
│   │
│   ├── training/                         ← Training loop, loss, callbacks
│   │   ├── __init__.py
│   │   ├── rollout.py                    ← Collect trajectories per agent
│   │   ├── trainer.py                    ← Orchestrates epochs, validation
│   │   ├── runner.py                     ← Single‑episode runner + logging
│   │   └── callbacks.py                  ← Checkpointing, early‑stop, metrics
│   │
│   └── utils/                            ← Logging, metrics, plotting
│       ├── __init__.py
│       ├── logger.py                     ← TensorBoard / CSV / WandB
│       ├── metrics.py                    ← Task completion, deadlocks, robustness
│       └── visualization.py              ← Animate episodes + highlight deadlocks
│
├── experiments/                          ← Launch scripts for benchmarks
│   ├── run_gridworld.py                  ← GridWorld + dynamic events benchmark
│   ├── run_mpe.py                        ← MPE scenarios: Spread, Gather, Tag
│   ├── run_smac.py                       ← SMAC tasks: 3m, 8m, 27m, 100m
│   └── sweep_hyperparams.py              ← Example Ray Tune / Optuna sweep
│
├── benchmarks/                           ← Baseline comparisons & result loaders
│   ├── baselines.py                      ← QMIX, MADDPG, IPPO implementations
│   ├── compare_results.py                ← Plot performance & communication overhead
│   └── realworld_case.py                 ← CityFlow traffic, power‑grid (IEEE‑33) tests
│
├── visualizations/                       ← Generated GIFs, plots, metrics dashboards
│   ├── gridworld_demo.gif
│   ├── smac_performance.png
│   └── communication_patterns.png
│
├── tests/                                ← Unit & integration tests
│   ├── test_environment.py               ← grid, dynamics, failures
│   ├── test_policies.py                  ← option & worker policies
│   ├── test_comms.py                     ← attention output & mask behavior
│   ├── test_agents.py                    ← hierarchical agent act/learn
│   └── test_training.py                  ← trainer runs 1 epoch end‑to‑end
│
└── docs/                                 ← Detailed docs for readers & reviewers
    ├── architecture.md                   ← UML diagrams, dataflow charts
    ├── protocols.md                      ← API usage & config spec
    ├── benchmarks.md                     ← Dataset details & evaluation metrics
    ├── citation.bib                      ← BibTeX entries for all references
    └── glossary.md                       ← Definitions: HRL, CTDE, ICP, IDS, UPR
```

---

## ⚙️ Installation

### ✅ Option 1: Recommended – Conda

```bash
conda env create -f environment.yml
conda activate sdhrl
```

### 🐍 Option 2: pip

```bash
pip install -r requirements.txt
```

### 📦 Development Installation

```bash
pip install -e .
```

---

## 🚀 Quick Start

### 1. Train on GridWorld with Dynamic Events

```bash
python experiments/run_gridworld.py --config configs/sdhrl_default.yaml
```

### 2. Run MPE Multi-Particle Environment

```bash
python experiments/run_mpe.py --config configs/env_mpe.yaml
```

### 3. Execute SMAC StarCraft Benchmark

```bash
python experiments/run_smac.py --config configs/env_smac.yaml
```

### 4. Hyperparameter Sweep

```bash
python experiments/sweep_hyperparams.py --sweep-config configs/training_defaults.yaml
```

### 5. Compare with Baselines

```bash
python benchmarks/compare_results.py --methods QMIX,MADDPG,IPPO --env gridworld
```

---

## 🧪 Benchmarks & Environments
_____________________________________________________________________________________________________________________________________________________________________________
| **Environment**                | **Script**                     | **Agents** | **Key Metrics**                                          | **Baselines Available**          |
|--------------------------------|--------------------------------|------------|----------------------------------------------------------|----------------------------------|
| **GridWorld + Dynamic Events** | `experiments/run_gridworld.py` | 5–50       | Task completion, deadlock rate, communication efficiency | QMIX, PPO, Random                |
| **MPE (Multi-Particle Env)**   | `experiments/run_mpe.py`       | 3–10       | Reward, collision rate, coordination success             | MADDPG, IPPO, MAPPO              |
| **SMAC (StarCraft II)**        | `experiments/run_smac.py`      | 8–25       | Win rate, battle duration, unit survival                 | QMIX, COMA, QTRAN                |
| **Real-World Cases**           | `benchmarks/realworld_case.py` | 4–30       | Domain-specific KPIs                                     | Custom baselines                 |
_____________________________________________________________________________________________________________________________________________________________________________

### 🔧 Configuration Files

Each environment has dedicated config files in `configs/`:
- `env_gridworld.yaml` - Grid size, obstacle density, dynamic event frequency
- `env_mpe.yaml` - PettingZoo MPE scenarios (Spread, Gather, Tag)
- `env_smac.yaml` - StarCraft II map configurations
- `agent_hrl_icp.yaml` - Hierarchical agent with implicit communication
- `training_defaults.yaml` - Training hyperparameters and GPU settings

---

## 🖼 Example Visualizations

| GridWorld                              | Communication Attention                        |
| -------------------------------------- | ---------------------------------------------- |
| ![](visualizations/gridworld_demo.gif) | ![](visualizations/communication_patterns.png) |

---

## 📝 Citation

Please cite this repository if you use it in your own research:

```bibtex
@misc{MultiAgentGridWorld2025,
  title={Scalable Decentralized Hierarchical RL: MultiAgent Coordination in Dynamic Environments},
  author={Md Shoaib Uddin Chanda},
  year={2025},
  note={https://github.com/your-repo-link},
}
```

---

## 🙋 Acknowledgements

* [Sutton et al., 1999] — Temporal Abstraction
* [Busoniu et al., 2008] — Multiagent RL Survey
* [Lowe et al., 2017] — MADDPG

---

## 📬 Contact

Developed by **Md Shoaib Uddin Chanda**
For queries, issues, or collaboration: [[<EMAIL>]]

---

## ⭐️ Star this repo if you find it useful!

---