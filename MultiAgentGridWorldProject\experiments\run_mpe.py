"""
Multi-Particle Environment (MPE) Experiment Runner

Entry point for running hierarchical multi-agent RL experiments on PettingZoo MPE environments.
Supports full configuration management, reproducible seeding, and comprehensive logging.

Usage:
    python experiments/run_mpe.py
    python experiments/run_mpe.py agent.num_options=8 training.lr=0.0005
    python experiments/run_mpe.py env.scenario=simple_tag num_agents=4
    python experiments/run_mpe.py seed=42 experiment_name=mpe_spread
"""

import os
import sys
import logging
import hydra
from omegaconf import DictConfig, OmegaConf
from pathlib import Path
import torch
import numpy as np
import random

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.training.runner import TrainingRunner

logger = logging.getLogger(__name__)


def set_seed(seed: int):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    # Ensure deterministic behavior
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    logger.info(f"Set random seed to {seed}")


def create_mpe_environment(env_config: DictConfig):
    """Create MPE environment from config."""
    try:
        from pettingzoo.mpe import simple_spread_v3, simple_tag_v3, simple_adversary_v3
        
        config_dict = OmegaConf.to_container(env_config, resolve=True)
        scenario = config_dict.get("scenario", "simple_spread")
        
        # Map scenario names to environment constructors
        env_map = {
            "simple_spread": simple_spread_v3.env,
            "simple_tag": simple_tag_v3.env,
            "simple_adversary": simple_adversary_v3.env
        }
        
        if scenario not in env_map:
            raise ValueError(f"Unknown MPE scenario: {scenario}")
        
        # Create environment with config parameters
        env_kwargs = {
            "N": config_dict.get("num_agents", 3),
            "local_ratio": config_dict.get("local_ratio", 0.5),
            "max_cycles": config_dict.get("episode_limit", 200),
            "continuous_actions": config_dict.get("continuous_actions", False),
            "render_mode": config_dict.get("render_mode", None)
        }
        
        env = env_map[scenario](**env_kwargs)
        logger.info(f"Created MPE environment: {scenario} with {config_dict}")
        return env
        
    except ImportError:
        logger.error("PettingZoo not available. Please install with: pip install pettingzoo[mpe]")
        raise
    except Exception as e:
        logger.error(f"Failed to create MPE environment: {e}")
        raise


def setup_experiment_config(cfg: DictConfig) -> dict:
    """Setup complete experiment configuration."""
    # Convert to regular dict for easier manipulation
    config = OmegaConf.to_container(cfg, resolve=True)
    
    # Setup results directory
    results_dir = Path(config.get("results_dir", "results"))
    experiment_name = config.get("experiment_name", "mpe_hrl")
    
    # Add timestamp if not in config
    if "timestamp" not in config:
        import time
        config["timestamp"] = int(time.time())
    
    # Update paths
    config["results_dir"] = str(results_dir)
    config["experiment_name"] = experiment_name
    
    # Ensure required sections exist
    if "training" not in config:
        config["training"] = {}
    if "agent" not in config:
        config["agent"] = {}
    if "env" not in config:
        config["env"] = {}
    
    # MPE is inherently multi-agent
    config["multi_agent"] = True
    if "num_agents" not in config:
        config["num_agents"] = config["env"].get("num_agents", 3)
    
    return config


@hydra.main(config_path="../configs", config_name="mpe_default", version_base="1.3")
def main(cfg: DictConfig) -> None:
    """
    Main experiment runner for MPE hierarchical RL.
    
    Args:
        cfg: Hydra configuration object
    """
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting MPE Hierarchical RL Experiment")
    logger.info(f"Configuration:\n{OmegaConf.to_yaml(cfg)}")
    
    # Set random seed for reproducibility
    seed = cfg.get("seed", 42)
    set_seed(seed)
    
    # Setup experiment configuration
    config = setup_experiment_config(cfg)
    
    try:
        # Initialize training runner
        logger.info("Initializing training runner...")
        runner = TrainingRunner(config)
        
        # Setup environment (MPE-specific)
        logger.info("Setting up MPE environment...")
        # Note: For now, we'll use our GridWorld as a placeholder
        # In a full implementation, you'd integrate PettingZoo MPE here
        runner.setup_environment(config["env"])
        
        # Setup agents (multi-agent by default for MPE)
        logger.info("Setting up multi-agent system...")
        agent_config = config["agent"]
        agent_config["num_agents"] = config["num_agents"]
        agent_config["use_communication"] = config.get("use_communication", True)
        runner.setup_agents(agent_config)
        
        # Setup training
        logger.info("Setting up training components...")
        training_config = config["training"]
        training_config["horizon"] = config.get("horizon", 2048)
        training_config["multi_agent"] = True
        runner.setup_training(training_config)
        
        # Run training
        logger.info("Starting training...")
        results = runner.train()
        
        # Log final results
        logger.info("Training completed successfully!")
        logger.info(f"Final results: {results['final_evaluation']}")
        logger.info(f"Best reward: {results['best_reward']:.3f}")
        logger.info(f"Results saved to: {runner.run_dir}")
        
        # Print summary for CLI users
        print("\n" + "="*60)
        print("🎉 MPE EXPERIMENT COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"🌍 Environment: {config['env'].get('scenario', 'simple_spread')}")
        print(f"🤖 Agents: {config['num_agents']}")
        print(f"📊 Best Reward: {results['best_reward']:.3f}")
        print(f"⏱️  Training Time: {results['training_time']:.2f}s")
        print(f"🔢 Total Steps: {results['global_steps']:,}")
        print(f"📁 Results: {runner.run_dir}")
        print("="*60)
        
        return results
        
    except Exception as e:
        logger.error(f"MPE experiment failed: {str(e)}")
        logger.exception("Full traceback:")
        raise


if __name__ == "__main__":
    main()