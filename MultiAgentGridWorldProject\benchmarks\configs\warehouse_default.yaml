# Multi-Robot Warehouse Configuration
environment: warehouse

# Warehouse Parameters
warehouse_size: [20, 20]
num_robots: 6
num_shelves: 50
num_orders: 10
episode_limit: 800

# SD-HRL Agent Configuration
agent:
  num_options: 5  # Navigation and task options
  option_freq: 6
  hidden_dim: 128
  use_communication: true
  comm_range: 5  # Communication radius

# Training Parameters
training:
  num_episodes: 1200
  batch_size: 64
  learning_rate: 0.0005
  gamma: 0.99

# Performance Metrics
metrics:
  - average_completion_time
  - throughput
  - efficiency
  - collision_rate

# Evaluation
evaluation:
  num_episodes: 100
  render: false
  save_trajectories: true