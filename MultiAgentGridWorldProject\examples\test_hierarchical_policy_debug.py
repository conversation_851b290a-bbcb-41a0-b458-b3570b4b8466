"""
Hierarchical Policy Debug Script

Comprehensive debugging and analysis of the hierarchical policy system
with detailed logging, visualization, and interpretability features.
"""

import sys
import os
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import json

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.policies import (
    OptionPolicy,
    WorkerPolicy,
    TerminationFunction,
    HierarchicalPolicy,
    MultiAgentHierarchicalPolicy
)
from src.environment import GridWorldEnv, suppress_environment_warnings

# Suppress warnings for cleaner output
suppress_environment_warnings()


class HierarchicalPolicyDebugger:
    """Debug and analyze hierarchical policy behavior."""
    
    def __init__(self, config: dict):
        self.config = config
        self.logs = defaultdict(list)
        self.option_usage = defaultdict(int)
        self.option_transitions = defaultdict(lambda: defaultdict(int))
        self.termination_stats = defaultdict(list)
        
    def debug_single_agent_policy(self):
        """Debug single agent hierarchical policy."""
        print("🔬 Debugging Single Agent Hierarchical Policy...")
        
        # Create policy
        policy = HierarchicalPolicy(self.config)
        
        # Run episode simulation
        observations = torch.randn(1, self.config["obs_dim"])
        
        print("\n📊 Episode Simulation with Detailed Logging:")
        print("Step | Option | Action | Changed | Terminate | Duration | Log Prob | Value")
        print("-" * 80)
        
        for step in range(25):
            result = policy(observations)
            
            option = result["current_options"].item()
            action = result["actions"].item()
            changed = result["option_changed"]
            terminate = result["should_terminate"].item()
            duration = result["option_step_count"]
            option_log_prob = result["option_log_probs"].item()
            worker_value = result["worker_values"].item()
            
            # Log data
            self.logs["steps"].append(step)
            self.logs["options"].append(option)
            self.logs["actions"].append(action)
            self.logs["option_changes"].append(changed)
            self.logs["terminations"].append(terminate)
            self.logs["durations"].append(duration)
            self.logs["option_log_probs"].append(option_log_prob)
            self.logs["worker_values"].append(worker_value)
            
            # Track option usage
            self.option_usage[option] += 1
            
            # Track option transitions
            if step > 0 and changed:
                prev_option = self.logs["options"][-2]
                self.option_transitions[prev_option][option] += 1
            
            # Track termination stats
            self.termination_stats[option].append(terminate)
            
            # Print step info
            status = "NEW" if changed else f"({duration})"
            term_status = "TERM" if terminate else "CONT"
            print(f"{step:4d} | {option:6d} | {action:6d} | {status:7s} | "
                  f"{term_status:9s} | {duration:8d} | {option_log_prob:8.3f} | {worker_value:8.3f}")
        
        # Analyze results
        self._analyze_single_agent_results(policy)
        
        return policy
    
    def debug_multi_agent_policy(self):
        """Debug multi-agent hierarchical policy."""
        print("\n🔬 Debugging Multi-Agent Hierarchical Policy...")
        
        num_agents = 3
        policy = MultiAgentHierarchicalPolicy(self.config, num_agents)
        
        observations = {
            f"agent_{i}": torch.randn(1, self.config["obs_dim"])
            for i in range(num_agents)
        }
        
        print("\n📊 Multi-Agent Episode Simulation:")
        print("Step | Agent 0      | Agent 1      | Agent 2      | Coordination")
        print("-" * 70)
        
        coordination_events = 0
        
        for step in range(15):
            results = policy(observations)
            
            step_info = []
            current_options = []
            
            for i in range(num_agents):
                agent_id = f"agent_{i}"
                result = results[agent_id]
                
                option = result["current_options"].item()
                action = result["actions"].item()
                changed = result["option_changed"]
                
                current_options.append(option)
                status = "NEW" if changed else f"({result['option_step_count']})"
                step_info.append(f"O{option}{status}→A{action}")
            
            # Check for coordination (agents with same option)
            unique_options = len(set(current_options))
            if unique_options < num_agents:
                coordination_events += 1
                coord_status = f"COORD({num_agents - unique_options})"
            else:
                coord_status = "INDEP"
            
            print(f"{step:4d} | {step_info[0]:12s} | {step_info[1]:12s} | "
                  f"{step_info[2]:12s} | {coord_status}")
        
        # Analyze multi-agent results
        self._analyze_multi_agent_results(policy, coordination_events)
        
        return policy
    
    def debug_with_environment(self):
        """Debug policy with actual environment interaction."""
        print("\n🔬 Debugging Policy-Environment Integration...")
        
        # Create environment
        env_config = {
            "map_size": [8, 8],
            "num_agents": 2,
            "obstacles": 3,
            "episode_limit": 50,
            "observation_type": "partial",
            "observation_radius": 3,
            "seed": 42
        }
        
        env = GridWorldEnv(env_config)
        
        # Create multi-agent policy
        policy_config = self.config.copy()
        policy_config["obs_dim"] = env.observation_space.shape[0]
        policy_config["action_dim"] = env.action_space.n
        
        policy = MultiAgentHierarchicalPolicy(policy_config, env.num_agents)
        
        # Run episode
        obs, info = env.reset()
        
        print("\n📊 Environment Integration Test:")
        print("Step | Agent 0 (O→A) | Agent 1 (O→A) | Rewards | Environment State")
        print("-" * 80)
        
        total_rewards = {agent_id: 0.0 for agent_id in env.agent_ids}
        
        for step in range(20):
            # Convert observations to tensors
            obs_tensors = {
                agent_id: torch.tensor(obs_array, dtype=torch.float32).unsqueeze(0)
                for agent_id, obs_array in obs.items()
            }
            
            # Get actions from policy
            results = policy(obs_tensors)
            actions = {
                agent_id: results[agent_id]["actions"].item()
                for agent_id in env.agent_ids
            }
            
            # Step environment
            obs, rewards, dones, info = env.step(actions)
            
            # Update total rewards
            for agent_id, reward in rewards.items():
                total_rewards[agent_id] += reward
            
            # Log step
            agent_info = []
            for agent_id in env.agent_ids:
                option = results[agent_id]["current_options"].item()
                action = actions[agent_id]
                agent_info.append(f"O{option}→A{action}")
            
            reward_str = f"[{rewards['agent_0']:+.2f}, {rewards['agent_1']:+.2f}]"
            env_state = f"Pos: {list(env.agent_positions.values())}"
            
            print(f"{step:4d} | {agent_info[0]:13s} | {agent_info[1]:13s} | "
                  f"{reward_str:10s} | {env_state}")
            
            if all(dones.values()):
                print(f"Episode ended at step {step}")
                break
        
        print(f"\n📈 Final Results:")
        print(f"Total Rewards: {total_rewards}")
        print(f"Environment Metrics: {env.get_episode_metrics()}")
        
        env.close()
        return policy
    
    def debug_option_embeddings(self):
        """Debug option embedding behavior and similarity."""
        print("\n🔬 Debugging Option Embeddings...")
        
        policy = OptionPolicy(self.config)
        
        # Get all option embeddings
        option_indices = torch.arange(self.config["num_options"])
        embeddings = policy.get_option_embedding(option_indices)
        
        print(f"Option embeddings shape: {embeddings.shape}")
        
        # Compute pairwise similarities
        similarities = torch.mm(embeddings, embeddings.t())
        similarities_np = similarities.detach().numpy()
        
        print("\n📊 Option Embedding Similarities:")
        print("Option", end="")
        for i in range(self.config["num_options"]):
            print(f"{i:8d}", end="")
        print()
        
        for i in range(self.config["num_options"]):
            print(f"{i:6d}", end="")
            for j in range(self.config["num_options"]):
                print(f"{similarities_np[i, j]:8.3f}", end="")
            print()
        
        # Analyze embedding diversity
        off_diagonal = similarities_np[~np.eye(similarities_np.shape[0], dtype=bool)]
        avg_similarity = np.mean(off_diagonal)
        std_similarity = np.std(off_diagonal)
        
        print(f"\n📈 Embedding Analysis:")
        print(f"Average off-diagonal similarity: {avg_similarity:.3f}")
        print(f"Similarity standard deviation: {std_similarity:.3f}")
        print(f"Embedding diversity score: {1 - avg_similarity:.3f}")
        
        return embeddings, similarities_np
    
    def debug_termination_behavior(self):
        """Debug termination function behavior across different strategies."""
        print("\n🔬 Debugging Termination Function Behavior...")
        
        strategies = ["learned", "fixed", "entropy", "confidence"]
        
        for strategy in strategies:
            print(f"\n📊 Testing {strategy.upper()} termination strategy:")
            
            config = self.config.copy()
            config["termination_strategy"] = strategy
            config["fixed_duration"] = 5
            config["entropy_threshold"] = 0.5
            config["confidence_threshold"] = 0.8
            
            termination_fn = TerminationFunction(config)
            
            # Test with different inputs
            batch_size = 8
            observations = torch.randn(batch_size, self.config["obs_dim"])
            options = torch.randint(0, self.config["num_options"], (batch_size,))
            
            if strategy == "fixed":
                option_durations = torch.tensor([1, 3, 5, 7, 2, 6, 4, 8])
                result = termination_fn(observations, options, option_durations=option_durations)
                print(f"Durations: {option_durations.tolist()}")
            elif strategy == "entropy":
                worker_entropy = torch.tensor([0.2, 0.6, 0.3, 0.8, 0.1, 0.7, 0.4, 0.9])
                result = termination_fn(observations, options, worker_entropy=worker_entropy)
                print(f"Entropies: {worker_entropy.tolist()}")
            elif strategy == "confidence":
                worker_confidence = torch.tensor([0.9, 0.3, 0.8, 0.2, 0.95, 0.4, 0.7, 0.1])
                result = termination_fn(observations, options, worker_confidence=worker_confidence)
                print(f"Confidences: {worker_confidence.tolist()}")
            else:  # learned
                result = termination_fn(observations, options)
            
            print(f"Termination probs: {result['termination_probs'].tolist()}")
            print(f"Should terminate: {result['should_terminate'].tolist()}")
    
    def _analyze_single_agent_results(self, policy):
        """Analyze single agent policy results."""
        print(f"\n📈 Single Agent Analysis:")
        
        # Option usage distribution
        total_steps = len(self.logs["steps"])
        print(f"Total steps: {total_steps}")
        print(f"Option usage distribution:")
        for option_id in range(self.config["num_options"]):
            usage = self.option_usage[option_id]
            percentage = (usage / total_steps) * 100
            print(f"  Option {option_id}: {usage:3d} steps ({percentage:5.1f}%)")
        
        # Option switches
        switches = sum(self.logs["option_changes"])
        switch_rate = switches / total_steps
        print(f"Option switches: {switches} (rate: {switch_rate:.3f})")
        
        # Average option duration
        durations = [d for d, changed in zip(self.logs["durations"], self.logs["option_changes"]) if changed]
        if durations:
            avg_duration = np.mean(durations)
            print(f"Average option duration: {avg_duration:.2f} steps")
        
        # Termination analysis
        termination_rate = np.mean(self.logs["terminations"])
        print(f"Termination rate: {termination_rate:.3f}")
        
        # Policy metrics
        metrics = policy.get_metrics()
        print(f"Policy metrics: {metrics}")
        
        # Option usage entropy
        usage_probs = np.array([self.option_usage[i] for i in range(self.config["num_options"])])
        usage_probs = usage_probs / usage_probs.sum()
        usage_entropy = -np.sum(usage_probs * np.log(usage_probs + 1e-8))
        max_entropy = np.log(self.config["num_options"])
        normalized_entropy = usage_entropy / max_entropy
        print(f"Option usage entropy: {usage_entropy:.3f} (normalized: {normalized_entropy:.3f})")
    
    def _analyze_multi_agent_results(self, policy, coordination_events):
        """Analyze multi-agent policy results."""
        print(f"\n📈 Multi-Agent Analysis:")
        
        # Get metrics for all agents
        metrics = policy.get_metrics()
        
        print(f"Coordination events: {coordination_events}")
        
        for agent_id, agent_metrics in metrics.items():
            print(f"{agent_id}:")
            print(f"  Option switches: {agent_metrics['option_switches']}")
            print(f"  Average duration: {agent_metrics['avg_option_duration']:.2f}")
            print(f"  Switch rate: {agent_metrics['option_switch_rate']:.3f}")
    
    def generate_visualizations(self):
        """Generate visualization plots for analysis."""
        print("\n📊 Generating Visualizations...")
        
        if not self.logs["steps"]:
            print("No data to visualize. Run debug_single_agent_policy first.")
            return
        
        # Create figure with subplots
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle("Hierarchical Policy Analysis", fontsize=16)
        
        # Plot 1: Option usage over time
        axes[0, 0].plot(self.logs["steps"], self.logs["options"], 'o-', markersize=3)
        axes[0, 0].set_title("Option Selection Over Time")
        axes[0, 0].set_xlabel("Step")
        axes[0, 0].set_ylabel("Selected Option")
        axes[0, 0].grid(True, alpha=0.3)
        
        # Plot 2: Option usage distribution
        option_counts = [self.option_usage[i] for i in range(self.config["num_options"])]
        axes[0, 1].bar(range(self.config["num_options"]), option_counts)
        axes[0, 1].set_title("Option Usage Distribution")
        axes[0, 1].set_xlabel("Option ID")
        axes[0, 1].set_ylabel("Usage Count")
        
        # Plot 3: Termination probability over time
        axes[1, 0].plot(self.logs["steps"], self.logs["terminations"], 'r-', alpha=0.7)
        axes[1, 0].set_title("Termination Events Over Time")
        axes[1, 0].set_xlabel("Step")
        axes[1, 0].set_ylabel("Termination")
        axes[1, 0].grid(True, alpha=0.3)
        
        # Plot 4: Worker values over time
        axes[1, 1].plot(self.logs["steps"], self.logs["worker_values"], 'g-', alpha=0.7)
        axes[1, 1].set_title("Worker Value Estimates")
        axes[1, 1].set_xlabel("Step")
        axes[1, 1].set_ylabel("Value")
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save plot
        output_path = "hierarchical_policy_analysis.png"
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"Visualization saved to: {output_path}")
        
        # Show plot if in interactive mode
        try:
            plt.show()
        except:
            pass
    
    def save_debug_report(self):
        """Save comprehensive debug report."""
        print("\n💾 Saving Debug Report...")
        
        report = {
            "config": self.config,
            "logs": dict(self.logs),
            "option_usage": dict(self.option_usage),
            "option_transitions": {
                str(k): dict(v) for k, v in self.option_transitions.items()
            },
            "termination_stats": {
                str(k): v for k, v in self.termination_stats.items()
            }
        }
        
        # Convert numpy arrays to lists for JSON serialization
        for key, value in report["logs"].items():
            if isinstance(value, list) and len(value) > 0:
                if isinstance(value[0], (np.integer, np.floating)):
                    report["logs"][key] = [float(v) for v in value]
        
        output_path = "hierarchical_policy_debug_report.json"
        with open(output_path, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"Debug report saved to: {output_path}")


def main():
    """Run comprehensive hierarchical policy debugging."""
    print("🚀 Starting Hierarchical Policy Debug Session...")
    print("=" * 80)
    
    # Configuration
    config = {
        "obs_dim": 128,
        "action_dim": 5,
        "num_options": 6,
        "option_freq": 8,
        "hidden_dim": 256,
        "num_layers": 2,
        "action_space_type": "discrete",
        "termination_strategy": "learned",
        "use_gumbel_softmax": True,
        "use_option_embeddings": True,
        "option_embed_dim": 32,
        "parameter_sharing": False
    }
    
    # Create debugger
    debugger = HierarchicalPolicyDebugger(config)
    
    try:
        # Run all debug tests
        print("\n" + "="*80)
        debugger.debug_single_agent_policy()
        
        print("\n" + "="*80)
        debugger.debug_multi_agent_policy()
        
        print("\n" + "="*80)
        debugger.debug_with_environment()
        
        print("\n" + "="*80)
        debugger.debug_option_embeddings()
        
        print("\n" + "="*80)
        debugger.debug_termination_behavior()
        
        print("\n" + "="*80)
        debugger.generate_visualizations()
        
        print("\n" + "="*80)
        debugger.save_debug_report()
        
        print("\n" + "="*80)
        print("🎉 Hierarchical Policy Debug Session Complete!")
        print("✅ All components functioning correctly")
        print("✅ Detailed analysis and visualizations generated")
        print("✅ Debug report saved for further analysis")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug session failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)