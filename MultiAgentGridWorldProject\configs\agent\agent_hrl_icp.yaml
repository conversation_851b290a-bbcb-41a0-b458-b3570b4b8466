# SD-HRL Agent Configuration
agent_type: "HierarchicalAgent"

# Hierarchical Reinforcement Learning
option_freq: 8                    # K steps before choosing new option (increased for stability)
num_options: 6                    # More options for complex spatial tasks
option_dim: 32                    # Option embedding dimension
termination_strategy: "entropy"   # "entropy", "fixed", "random", "confidence"
termination_threshold: 0.8        # Entropy threshold for termination
use_option_critic: true           # Option-Critic architecture
intra_option_learning: true       # Learn within options

# Network Architecture
hidden_dim: 256                   # Larger for complex spatial reasoning
num_layers: 3                     # Deeper networks
activation: "relu"                # "relu", "tanh", "gelu"
init_std: 0.1
use_orthogonal_init: true
layer_norm_eps: 1e-5

# Communication Module (Implicit Communication Protocol)
use_attention: true
comm_type: "gat"                  # "none", "mlp", "gat", "transformer"
attention_heads: 4                # More heads for richer communication
comm_hidden_dim: 128
comm_threshold: 0.3               # Lower threshold for more communication
use_sparse_attention: true
attention_dropout: 0.1
kl_sparsity_weight: 0.005         # Regularization for sparse communication

# Spatial Reasoning
use_spatial_embedding: true       # Encode spatial relationships
spatial_embed_dim: 64
use_positional_encoding: true
max_distance: 20                  # For relative position encoding

# Training Configuration
parameter_sharing: false          # Individual agent parameters
centralized_critic: true         # CTDE paradigm
use_rnn: true                     # For temporal dependencies
rnn_type: "gru"                   # "lstm", "gru"
rnn_hidden_dim: 128
use_layer_norm: true
normalize_input: true
normalize_advantages: true

# Exploration
exploration_strategy: "epsilon_greedy"  # "epsilon_greedy", "boltzmann", "ucb"
epsilon_start: 1.0
epsilon_end: 0.05
epsilon_decay: 0.995
temperature: 1.0                  # For Boltzmann exploration

# Advanced Features
use_curiosity: false              # Intrinsic motivation
use_self_attention: true          # Self-attention over time
use_residual_connections: true    # Skip connections
gradient_checkpointing: false     # Memory optimization

# Deadlock Prevention
deadlock_threshold: 30            # Force option change after this many steps without reward
stuck_detection: true             # Enable stuck detection