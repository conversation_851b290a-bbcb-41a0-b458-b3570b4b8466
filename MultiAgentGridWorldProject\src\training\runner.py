"""
Training Runner

The experiment manager that orchestrates rollout collection, training, evaluation,
and logging for hierarchical multi-agent RL experiments.
"""

import os
import time
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional
from pathlib import Path
import logging
from collections import defaultdict, deque

from .rollout import RolloutCollector, MultiAgentRolloutCollector
from .trainer import HierarchicalTrainer
from .callbacks import CallbackManager, EarlyStopping, ModelCheckpoint, MetricsLogger
from ..controllers.hierarchical_agent import HierarchicalAgent
from ..environment.grid_world import GridWorldEnv

logger = logging.getLogger(__name__)


class TrainingRunner:
    """
    Main training runner for hierarchical multi-agent RL experiments.
    
    Manages the complete training loop including rollout collection,
    policy updates, evaluation, and logging.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Training parameters
        self.num_epochs = config.get("num_epochs", 1000)
        self.steps_per_epoch = config.get("steps_per_epoch", 2048)
        self.eval_frequency = config.get("eval_frequency", 10)
        self.eval_episodes = config.get("eval_episodes", 10)
        self.save_frequency = config.get("save_frequency", 50)
        
        # Environment and agents
        self.env = None
        self.eval_env = None
        self.agents = {}
        self.is_multi_agent = config.get("multi_agent", False)
        
        # Training components
        self.rollout_collector = None
        self.trainer = None
        self.callback_manager = None
        
        # Results tracking
        self.results_dir = Path(config.get("results_dir", "results"))
        self.experiment_name = config.get("experiment_name", "hierarchical_rl")
        self.run_dir = self.results_dir / self.experiment_name / f"run_{int(time.time())}"
        
        # Metrics
        self.training_metrics = defaultdict(list)
        self.evaluation_metrics = defaultdict(list)
        self.best_reward = float('-inf')
        self.global_step = 0
        
        # Setup directories
        self._setup_directories()
        
        # Setup logging
        self._setup_logging()
        
        logger.info(f"TrainingRunner initialized: {self.experiment_name}")
        logger.info(f"Results directory: {self.run_dir}")
    
    def _setup_directories(self):
        """Create necessary directories."""
        self.run_dir.mkdir(parents=True, exist_ok=True)
        (self.run_dir / "checkpoints").mkdir(exist_ok=True)
        (self.run_dir / "plots").mkdir(exist_ok=True)
        (self.run_dir / "logs").mkdir(exist_ok=True)
        (self.run_dir / "videos").mkdir(exist_ok=True)
    
    def _setup_logging(self):
        """Setup file logging."""
        log_file = self.run_dir / "logs" / "training.log"
        self.file_handler = logging.FileHandler(log_file)
        self.file_handler.setLevel(logging.INFO)
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        self.file_handler.setFormatter(formatter)
        logger.addHandler(self.file_handler)
    
    def setup_environment(self, env_config: Dict[str, Any]):
        """Setup training and evaluation environments."""
        self.env = GridWorldEnv(env_config)
        self.eval_env = GridWorldEnv(env_config)  # Separate env for evaluation
        
        logger.info(f"Environment setup: {env_config}")
    
    def _get_observation_dimension(self) -> int:
        """Get observation dimension from environment."""
        if not hasattr(self, 'env') or self.env is None:
            return 128  # Default fallback
        
        try:
            # Reset environment to get sample observation
            reset_result = self.env.reset()
            if isinstance(reset_result, tuple):
                obs, _ = reset_result
            else:
                obs = reset_result
            
            if isinstance(obs, dict):
                # Multi-agent environment - get dimension from first agent
                sample_obs = next(iter(obs.values()))
            else:
                sample_obs = obs
            
            if hasattr(sample_obs, 'shape'):
                return sample_obs.shape[0] if len(sample_obs.shape) > 0 else 1
            elif isinstance(sample_obs, (list, tuple)):
                return len(sample_obs)
            else:
                return 1
        except Exception as e:
            logger.warning(f"Could not determine observation dimension: {e}, using default 128")
            return 128
    
    def setup_agents(self, agent_configs: Dict[str, Any]):
        """Setup agents for training."""
        # Get observation dimension from environment
        obs_dim = self._get_observation_dimension()
        agent_configs = agent_configs.copy()
        agent_configs["obs_dim"] = obs_dim
        
        if self.is_multi_agent:
            num_agents = agent_configs.get("num_agents", 2)
            for i in range(num_agents):
                agent_id = f"agent_{i}"
                self.agents[agent_id] = HierarchicalAgent(agent_id, agent_configs)
        else:
            self.agents["agent_0"] = HierarchicalAgent("agent_0", agent_configs)
        
        logger.info(f"Setup {len(self.agents)} agents with obs_dim={obs_dim}")
    
    def setup_training(self, training_config: Dict[str, Any]):
        """Setup training components."""
        # Rollout collector
        if self.is_multi_agent:
            rollout_config = training_config.copy()
            rollout_config["num_agents"] = len(self.agents)
            self.rollout_collector = MultiAgentRolloutCollector(rollout_config)
        else:
            self.rollout_collector = RolloutCollector(training_config)
        
        # Trainer
        self.trainer = HierarchicalTrainer(training_config)
        self.trainer.setup_agents(self.agents)
        
        # Callbacks
        callbacks = []
        
        # Early stopping
        if training_config.get("use_early_stopping", True):
            early_stopping = EarlyStopping(
                metric="avg_reward",
                patience=training_config.get("early_stopping_patience", 50),
                min_delta=training_config.get("early_stopping_min_delta", 0.01)
            )
            callbacks.append(early_stopping)
        
        # Model checkpointing
        checkpoint_callback = ModelCheckpoint(
            checkpoint_dir=self.run_dir / "checkpoints",
            save_frequency=self.save_frequency,
            save_best=True,
            metric="avg_reward"
        )
        callbacks.append(checkpoint_callback)
        
        # Metrics logging
        metrics_logger = MetricsLogger(
            log_dir=self.run_dir / "logs",
            plot_dir=self.run_dir / "plots"
        )
        callbacks.append(metrics_logger)
        
        self.callback_manager = CallbackManager(callbacks)
        
        logger.info("Training components setup complete")
    
    def train(self) -> Dict[str, Any]:
        """
        Run the complete training loop.
        
        Returns:
            Dictionary containing final training results
        """
        logger.info("Starting training...")
        start_time = time.time()
        
        # Training loop
        for epoch in range(self.num_epochs):
            epoch_start_time = time.time()
            
            # Collect rollouts
            logger.info(f"Epoch {epoch + 1}/{self.num_epochs}: Collecting rollouts...")
            if self.is_multi_agent:
                rollout_data = self.rollout_collector.collect_rollout(
                    self.env, self.agents, max_steps=self.steps_per_epoch
                )
            else:
                # For single agent, pass the single agent
                agent = next(iter(self.agents.values()))
                rollout_data = self.rollout_collector.collect_rollout(
                    self.env, agent, max_steps=self.steps_per_epoch
                )
            
            # Training step
            logger.info(f"Epoch {epoch + 1}/{self.num_epochs}: Training...")
            training_metrics = self.trainer.train_step(rollout_data)
            
            # Update global step
            if "batch_size" in rollout_data:
                self.global_step += rollout_data["batch_size"]
            elif "global_stats" in rollout_data:
                total_steps = sum(
                    batch["batch_size"] for batch in rollout_data["agents"].values()
                )
                self.global_step += total_steps
            
            # Store training metrics
            for key, value in training_metrics.items():
                self.training_metrics[key].append(value)
            
            # Evaluation
            eval_metrics = {}
            if (epoch + 1) % self.eval_frequency == 0:
                logger.info(f"Epoch {epoch + 1}/{self.num_epochs}: Evaluating...")
                eval_metrics = self.evaluate()
                
                # Store evaluation metrics
                for key, value in eval_metrics.items():
                    self.evaluation_metrics[key].append(value)
                
                # Update best reward
                if "avg_reward" in eval_metrics:
                    if eval_metrics["avg_reward"] > self.best_reward:
                        self.best_reward = eval_metrics["avg_reward"]
                        logger.info(f"New best reward: {self.best_reward:.3f}")
            
            # Combine metrics for callbacks
            epoch_metrics = {
                "epoch": epoch + 1,
                "global_step": self.global_step,
                **training_metrics,
                **eval_metrics
            }
            
            # Callback handling
            callback_result = self.callback_manager.on_epoch_end(epoch, epoch_metrics, self.agents)
            
            # Log epoch summary
            epoch_time = time.time() - epoch_start_time
            self._log_epoch_summary(epoch + 1, epoch_metrics, epoch_time)
            
            # Check for early stopping
            if callback_result.get("stop_training", False):
                logger.info("Early stopping triggered")
                break
        
        # Training complete
        total_time = time.time() - start_time
        logger.info(f"Training completed in {total_time:.2f} seconds")
        
        # Final evaluation
        final_eval = self.evaluate()
        
        # Save final results
        results = self._compile_results(final_eval, total_time)
        self._save_results(results)
        
        # Generate plots
        self._generate_plots()
        
        # Cleanup logging
        self._cleanup_logging()
        
        return results
    
    def evaluate(self) -> Dict[str, float]:
        """
        Evaluate current agents.
        
        Returns:
            Dictionary of evaluation metrics
        """
        # Set agents to evaluation mode
        for agent in self.agents.values():
            if hasattr(agent.policy, 'eval'):
                agent.policy.eval()
        
        episode_rewards = []
        episode_lengths = []
        option_usage = defaultdict(int)
        
        for episode in range(self.eval_episodes):
            if self.is_multi_agent:
                observations = self.eval_env.reset()
                for agent in self.agents.values():
                    agent.reset()
                
                episode_reward = 0.0
                episode_length = 0
                done = False
                
                while not done:
                    actions = {}
                    for agent_id, agent in self.agents.items():
                        if agent_id in observations:
                            action_dict = agent.act(observations[agent_id])
                            actions[agent_id] = action_dict["action"]
                            
                            # Track option usage
                            if isinstance(agent, HierarchicalAgent):
                                option = agent.option_tracker.current_option
                                if option is not None:
                                    option_usage[f"{agent_id}_option_{option}"] += 1
                    
                    next_observations, rewards, dones, info = self.eval_env.step(actions)
                    
                    # Update agents
                    total_reward = 0.0
                    for agent_id, agent in self.agents.items():
                        if agent_id in rewards:
                            agent.step(reward=rewards[agent_id], done=dones.get(agent_id, False))
                            total_reward += rewards[agent_id]
                    
                    episode_reward += total_reward
                    episode_length += 1
                    
                    done = all(dones.values()) if dones else False
                    observations = next_observations
                
            else:
                # Single agent evaluation
                reset_result = self.eval_env.reset()
                if isinstance(reset_result, tuple):
                    observation, info = reset_result
                else:
                    observation = reset_result
                
                # Extract agent observation if environment returns dict
                if isinstance(observation, dict):
                    agent_obs = next(iter(observation.values()))
                else:
                    agent_obs = observation
                
                agent = next(iter(self.agents.values()))
                agent.reset()
                
                episode_reward = 0.0
                episode_length = 0
                done = False
                
                while not done:
                    action_dict = agent.act(agent_obs)
                    action = action_dict["action"]
                    
                    # Track option usage
                    if isinstance(agent, HierarchicalAgent):
                        option = agent.option_tracker.current_option
                        if option is not None:
                            option_usage[f"option_{option}"] += 1
                    
                    # Handle environment step
                    if isinstance(observation, dict):
                        # Multi-agent environment format
                        agent_id = next(iter(observation.keys()))
                        action_for_env = {agent_id: action}
                    else:
                        action_for_env = action
                    
                    step_result = self.eval_env.step(action_for_env)
                    if len(step_result) == 5:
                        # New gym format
                        next_observation, reward, terminated, truncated, info = step_result
                        done = terminated or truncated
                    else:
                        # Old gym format
                        next_observation, reward, done, info = step_result
                    
                    # Extract reward and done for single agent
                    if isinstance(reward, dict):
                        agent_reward = next(iter(reward.values()))
                        agent_done = next(iter(done.values())) if isinstance(done, dict) else done
                    else:
                        agent_reward = reward
                        agent_done = done
                    
                    agent.step(reward=agent_reward, done=agent_done)
                    
                    episode_reward += agent_reward
                    episode_length += 1
                    
                    done = agent_done
                    
                    # Extract next observation
                    if isinstance(next_observation, dict):
                        agent_obs = next(iter(next_observation.values()))
                    else:
                        agent_obs = next_observation
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
        
        # Set agents back to training mode
        for agent in self.agents.values():
            if hasattr(agent.policy, 'train'):
                agent.policy.train()
        
        # Compute metrics
        metrics = {
            "avg_reward": np.mean(episode_rewards),
            "std_reward": np.std(episode_rewards),
            "min_reward": np.min(episode_rewards),
            "max_reward": np.max(episode_rewards),
            "avg_length": np.mean(episode_lengths),
            "std_length": np.std(episode_lengths)
        }
        
        # Add option usage metrics
        total_steps = sum(option_usage.values())
        if total_steps > 0:
            for option_key, count in option_usage.items():
                metrics[f"option_usage_{option_key}"] = count / total_steps
        
        return metrics
    
    def _log_epoch_summary(self, epoch: int, metrics: Dict[str, Any], epoch_time: float):
        """Log epoch summary."""
        summary_parts = [f"Epoch {epoch}"]
        
        if "avg_reward" in metrics:
            summary_parts.append(f"Reward: {metrics['avg_reward']:.3f}")
        
        if "total_loss" in metrics:
            summary_parts.append(f"Loss: {metrics['total_loss']:.4f}")
        
        if "option_policy_loss" in metrics:
            summary_parts.append(f"Option Loss: {metrics['option_policy_loss']:.4f}")
        
        if "worker_policy_loss" in metrics:
            summary_parts.append(f"Worker Loss: {metrics['worker_policy_loss']:.4f}")
        
        summary_parts.append(f"Time: {epoch_time:.2f}s")
        
        logger.info(" | ".join(summary_parts))
    
    def _compile_results(self, final_eval: Dict[str, float], total_time: float) -> Dict[str, Any]:
        """Compile final training results."""
        results = {
            "experiment_name": self.experiment_name,
            "config": self.config,
            "training_time": total_time,
            "global_steps": self.global_step,
            "best_reward": self.best_reward,
            "final_evaluation": final_eval,
            "training_metrics": {
                key: values[-100:] if len(values) > 100 else values
                for key, values in self.training_metrics.items()
            },
            "evaluation_metrics": dict(self.evaluation_metrics)
        }
        
        return results
    
    def _save_results(self, results: Dict[str, Any]):
        """Save results to file."""
        results_file = self.run_dir / "results.json"
        
        # Convert numpy arrays to lists for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            else:
                return obj
        
        results_json = convert_numpy(results)
        
        with open(results_file, 'w') as f:
            json.dump(results_json, f, indent=2)
        
        logger.info(f"Results saved to {results_file}")
    
    def _generate_plots(self):
        """Generate training plots."""
        plots_dir = self.run_dir / "plots"
        
        # Reward curve
        if "avg_reward" in self.evaluation_metrics:
            plt.figure(figsize=(10, 6))
            rewards = self.evaluation_metrics["avg_reward"]
            epochs = np.arange(1, len(rewards) + 1) * self.eval_frequency
            
            plt.plot(epochs, rewards, 'b-', linewidth=2, label='Average Reward')
            if "std_reward" in self.evaluation_metrics:
                stds = self.evaluation_metrics["std_reward"]
                plt.fill_between(epochs, 
                               np.array(rewards) - np.array(stds),
                               np.array(rewards) + np.array(stds),
                               alpha=0.3, color='blue')
            
            plt.xlabel('Epoch')
            plt.ylabel('Average Reward')
            plt.title('Training Progress')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig(plots_dir / "reward_curve.png", dpi=150, bbox_inches='tight')
            plt.close()
        
        # Loss curves
        if "total_loss" in self.training_metrics:
            plt.figure(figsize=(12, 8))
            
            # Total loss
            plt.subplot(2, 2, 1)
            plt.plot(self.training_metrics["total_loss"], 'r-', linewidth=1)
            plt.title('Total Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.grid(True, alpha=0.3)
            
            # Option policy loss
            if "option_policy_loss" in self.training_metrics:
                plt.subplot(2, 2, 2)
                plt.plot(self.training_metrics["option_policy_loss"], 'g-', linewidth=1)
                plt.title('Option Policy Loss')
                plt.xlabel('Epoch')
                plt.ylabel('Loss')
                plt.grid(True, alpha=0.3)
            
            # Worker policy loss
            if "worker_policy_loss" in self.training_metrics:
                plt.subplot(2, 2, 3)
                plt.plot(self.training_metrics["worker_policy_loss"], 'b-', linewidth=1)
                plt.title('Worker Policy Loss')
                plt.xlabel('Epoch')
                plt.ylabel('Loss')
                plt.grid(True, alpha=0.3)
            
            # Value loss
            if "worker_value_loss" in self.training_metrics:
                plt.subplot(2, 2, 4)
                plt.plot(self.training_metrics["worker_value_loss"], 'm-', linewidth=1)
                plt.title('Value Loss')
                plt.xlabel('Epoch')
                plt.ylabel('Loss')
                plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(plots_dir / "loss_curves.png", dpi=150, bbox_inches='tight')
            plt.close()
        
        # Option usage
        option_metrics = {k: v for k, v in self.evaluation_metrics.items() if k.startswith("option_usage")}
        if option_metrics:
            plt.figure(figsize=(10, 6))
            
            for option_key, usage_values in option_metrics.items():
                if usage_values:
                    epochs = np.arange(1, len(usage_values) + 1) * self.eval_frequency
                    plt.plot(epochs, usage_values, linewidth=2, label=option_key.replace("option_usage_", ""))
            
            plt.xlabel('Epoch')
            plt.ylabel('Usage Frequency')
            plt.title('Option Usage Over Time')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig(plots_dir / "option_usage.png", dpi=150, bbox_inches='tight')
            plt.close()
        
        logger.info(f"Plots saved to {plots_dir}")
    
    def _cleanup_logging(self):
        """Cleanup logging handlers."""
        if hasattr(self, 'file_handler'):
            logger.removeHandler(self.file_handler)
            self.file_handler.close()
    
    def save_checkpoint(self, epoch: int):
        """Save training checkpoint."""
        checkpoint_dir = self.run_dir / "checkpoints"
        
        # Save agents
        for agent_id, agent in self.agents.items():
            agent_checkpoint = {
                "agent_state": agent.save_state(),
                "policy_state": agent.policy.state_dict() if hasattr(agent.policy, 'state_dict') else None
            }
            torch.save(agent_checkpoint, checkpoint_dir / f"{agent_id}_epoch_{epoch}.pt")
        
        # Save trainer
        self.trainer.save_checkpoint(checkpoint_dir / f"trainer_epoch_{epoch}.pt")
        
        # Save metrics
        metrics_checkpoint = {
            "epoch": epoch,
            "global_step": self.global_step,
            "best_reward": self.best_reward,
            "training_metrics": dict(self.training_metrics),
            "evaluation_metrics": dict(self.evaluation_metrics)
        }
        torch.save(metrics_checkpoint, checkpoint_dir / f"metrics_epoch_{epoch}.pt")
        
        logger.info(f"Checkpoint saved for epoch {epoch}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """Load training checkpoint."""
        # Implementation for loading checkpoints
        # This would restore agents, trainer, and metrics
        pass