"""
Policy Module Development Test

Manual testing script for hierarchical policy components
to verify functionality before integration.
"""

import sys
import os
import torch
import numpy as np

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.policies import (
    OptionPolicy,
    WorkerPolicy,
    TerminationFunction,
    HierarchicalPolicy,
    MultiAgentHierarchicalPolicy
)


def test_option_policy():
    """Test option selection policy."""
    print("🧪 Testing Option Policy (π^H)...")
    
    config = {
        "obs_dim": 64,
        "num_options": 6,
        "hidden_dim": 128,
        "use_gumbel_softmax": True,
        "use_option_embeddings": True
    }
    
    policy = OptionPolicy(config)
    print(f"✅ Created OptionPolicy: {policy.num_options} options")
    
    # Test forward pass
    batch_size = 4
    observations = torch.randn(batch_size, 64)
    
    # Deterministic selection
    result = policy(observations, deterministic=True)
    print(f"✅ Deterministic selection: options {result['selected_options'].tolist()}")
    
    # Stochastic selection
    result = policy(observations, deterministic=False)
    print(f"✅ Stochastic selection: options {result['selected_options'].tolist()}")
    
    # Test option embeddings
    option_indices = torch.tensor([0, 1, 2, 3])
    embeddings = policy.get_option_embedding(option_indices)
    print(f"✅ Option embeddings shape: {embeddings.shape}")
    
    # Test exploration update
    initial_epsilon = policy.current_epsilon
    policy.update_exploration()
    print(f"✅ Exploration updated: ε {initial_epsilon:.3f} → {policy.current_epsilon:.3f}")
    
    return True


def test_worker_policy():
    """Test worker execution policy."""
    print("\n🧪 Testing Worker Policy (π^L_z)...")
    
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 6,
        "hidden_dim": 128,
        "action_space_type": "discrete",
        "option_conditioning": True,
        "separate_workers": False
    }
    
    policy = WorkerPolicy(config)
    print(f"✅ Created WorkerPolicy: {policy.action_dim} actions, option conditioning: {policy.option_conditioning}")
    
    # Test action selection
    batch_size = 4
    observations = torch.randn(batch_size, 64)
    options = torch.randint(0, 6, (batch_size,))
    
    result = policy(observations, options, deterministic=True)
    print(f"✅ Action selection: actions {result['actions'].tolist()}")
    print(f"✅ Values: {result['values'].tolist()}")
    
    # Test with separate workers
    config["separate_workers"] = True
    separate_policy = WorkerPolicy(config)
    result = separate_policy(observations, options)
    print(f"✅ Separate workers: actions {result['actions'].tolist()}")
    
    return True


def test_termination_function():
    """Test option termination function."""
    print("\n🧪 Testing Termination Function (β_z)...")
    
    config = {
        "obs_dim": 64,
        "num_options": 6,
        "hidden_dim": 128,
        "termination_strategy": "learned",
        "separate_terminators": True
    }
    
    termination_fn = TerminationFunction(config)
    print(f"✅ Created TerminationFunction: strategy={termination_fn.termination_strategy}")
    
    # Test learned termination
    batch_size = 4
    observations = torch.randn(batch_size, 64)
    options = torch.randint(0, 6, (batch_size,))
    
    result = termination_fn(observations, options)
    print(f"✅ Termination probs: {result['termination_probs'].tolist()}")
    print(f"✅ Should terminate: {result['should_terminate'].tolist()}")
    
    # Test fixed termination
    config["termination_strategy"] = "fixed"
    config["fixed_duration"] = 5
    fixed_termination = TerminationFunction(config)
    
    option_durations = torch.tensor([3, 5, 7, 2])
    result = fixed_termination(observations, options, option_durations=option_durations)
    expected = option_durations >= 5
    print(f"✅ Fixed termination: durations {option_durations.tolist()} → {result['should_terminate'].tolist()}")
    
    # Test entropy termination
    config["termination_strategy"] = "entropy"
    config["entropy_threshold"] = 0.5
    entropy_termination = TerminationFunction(config)
    
    worker_entropy = torch.tensor([0.3, 0.7, 0.2, 0.8])
    result = entropy_termination(observations, options, worker_entropy=worker_entropy)
    print(f"✅ Entropy termination: entropy {worker_entropy.tolist()} → {result['should_terminate'].tolist()}")
    
    return True


def test_hierarchical_policy():
    """Test complete hierarchical policy."""
    print("\n🧪 Testing Hierarchical Policy...")
    
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 6,
        "option_freq": 8,
        "hidden_dim": 128,
        "action_space_type": "discrete",
        "termination_strategy": "learned"
    }
    
    policy = HierarchicalPolicy(config)
    print(f"✅ Created HierarchicalPolicy: K={policy.option_freq} steps")
    
    # Test episode simulation
    observations = torch.randn(1, 64)
    
    print("\n📊 Episode Simulation:")
    for step in range(15):
        result = policy(observations)
        
        option = result["current_options"].item()
        action = result["actions"].item()
        changed = result["option_changed"]
        step_count = result["option_step_count"]
        
        status = "NEW" if changed else f"({step_count})"
        print(f"  Step {step:2d}: Option {option} {status} → Action {action}")
        
        # Force termination test
        if step == 10:
            result = policy(observations, force_option_selection=True)
            print(f"  Step {step:2d}: FORCED option selection → Option {result['current_options'].item()}")
    
    # Test metrics
    metrics = policy.get_metrics()
    print(f"\n📈 Metrics:")
    print(f"  Option switches: {metrics['option_switches']}")
    print(f"  Total steps: {metrics['total_steps']}")
    print(f"  Avg option duration: {metrics['avg_option_duration']:.2f}")
    print(f"  Switch rate: {metrics['option_switch_rate']:.3f}")
    
    # Test reset
    policy.reset()
    print(f"✅ Policy reset: current_option={policy.current_option}")
    
    return True


def test_multi_agent_hierarchical():
    """Test multi-agent hierarchical policy."""
    print("\n🧪 Testing Multi-Agent Hierarchical Policy...")
    
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 4,
        "option_freq": 5,
        "hidden_dim": 64,
        "parameter_sharing": False
    }
    
    num_agents = 3
    policy = MultiAgentHierarchicalPolicy(config, num_agents)
    print(f"✅ Created MultiAgentHierarchicalPolicy: {num_agents} agents")
    
    # Test multi-agent episode
    observations = {
        "agent_0": torch.randn(1, 64),
        "agent_1": torch.randn(1, 64),
        "agent_2": torch.randn(1, 64)
    }
    
    print("\n📊 Multi-Agent Episode:")
    for step in range(8):
        results = policy(observations)
        
        step_info = []
        for agent_id in ["agent_0", "agent_1", "agent_2"]:
            option = results[agent_id]["current_options"].item()
            action = results[agent_id]["actions"].item()
            changed = results[agent_id]["option_changed"]
            status = "NEW" if changed else f"({results[agent_id]['option_step_count']})"
            step_info.append(f"{agent_id[-1]}:O{option}{status}→A{action}")
        
        print(f"  Step {step:2d}: {' | '.join(step_info)}")
    
    # Test metrics
    metrics = policy.get_metrics()
    print(f"\n📈 Multi-Agent Metrics:")
    for agent_id, agent_metrics in metrics.items():
        print(f"  {agent_id}: switches={agent_metrics['option_switches']}, "
              f"avg_duration={agent_metrics['avg_option_duration']:.2f}")
    
    # Test parameter sharing
    config["parameter_sharing"] = True
    shared_policy = MultiAgentHierarchicalPolicy(config, num_agents)
    print(f"✅ Parameter sharing policy created")
    
    return True


def test_policy_with_environment():
    """Test policy integration with environment observations."""
    print("\n🧪 Testing Policy-Environment Integration...")
    
    # Simulate environment-like observations
    config = {
        "obs_dim": 128,  # Larger obs like from GridWorld
        "action_dim": 5,
        "num_options": 6,
        "option_freq": 8,
        "hidden_dim": 256
    }
    
    policy = HierarchicalPolicy(config)
    
    # Simulate varying observations (like agent moving in environment)
    base_obs = torch.randn(1, 128)
    
    print("🎮 Simulated Environment Interaction:")
    for episode_step in range(12):
        # Add some noise to simulate changing environment
        noise = torch.randn(1, 128) * 0.1
        observations = base_obs + noise
        
        result = policy(observations)
        
        option = result["current_options"].item()
        action = result["actions"].item()
        changed = result["option_changed"]
        
        # Simulate environment response
        reward = np.random.uniform(-0.1, 1.0)  # Random reward
        
        status = "NEW" if changed else f"({result['option_step_count']})"
        print(f"  Step {episode_step:2d}: Obs→Policy: O{option}{status}→A{action} | Reward: {reward:+.2f}")
    
    print("✅ Policy-environment integration successful")
    return True


def main():
    """Run all policy tests."""
    print("🚀 Starting Policy Module Development Tests...")
    print("=" * 60)
    
    try:
        tests = [
            test_option_policy,
            test_worker_policy,
            test_termination_function,
            test_hierarchical_policy,
            test_multi_agent_hierarchical,
            test_policy_with_environment
        ]
        
        results = []
        for test_func in tests:
            try:
                result = test_func()
                results.append(result)
            except Exception as e:
                print(f"❌ Test {test_func.__name__} failed: {e}")
                import traceback
                traceback.print_exc()
                results.append(False)
        
        # Summary
        print("\n" + "=" * 60)
        print("🎯 Policy Test Summary:")
        print(f"✅ Passed: {sum(results)}/{len(results)} tests")
        
        if all(results):
            print("🎉 All policy tests passed! Hierarchical system is ready.")
            return True
        else:
            print("❌ Some tests failed. Check the output above.")
            return False
            
    except Exception as e:
        print(f"❌ Critical error in policy test suite: {e}")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)