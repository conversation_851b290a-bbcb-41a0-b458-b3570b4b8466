"""
Attention Visualization and Analysis Tools

This module provides comprehensive tools for visualizing and analyzing
attention patterns in the SD-HRL framework's communication system.
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import torch
import networkx as nx
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path
import pandas as pd
from matplotlib.animation import FuncAnimation
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots

logger = logging.getLogger(__name__)


class AttentionVisualizer:
    """
    Comprehensive attention visualization and analysis tool.
    
    Provides methods for visualizing attention patterns, communication graphs,
    and analyzing the effectiveness of the attention mechanism.
    """
    
    def __init__(self, save_dir: str = "visualizations"):
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(exist_ok=True)
        
        # Set up plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # Store attention data for analysis
        self.attention_history = []
        self.communication_stats = {}
        
    def plot_attention_heatmap(
        self, 
        attention_matrix: np.ndarray,
        agent_positions: Optional[np.ndarray] = None,
        timestep: int = 0,
        save_path: Optional[str] = None
    ) -> plt.Figure:
        """
        Create a heatmap visualization of attention weights.
        
        Args:
            attention_matrix: NxN matrix of attention weights
            agent_positions: Optional agent positions for spatial context
            timestep: Current timestep for labeling
            save_path: Optional path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Attention heatmap
        im = axes[0].imshow(attention_matrix, cmap='viridis', aspect='auto')
        axes[0].set_title(f'Attention Matrix (t={timestep})')
        axes[0].set_xlabel('Target Agent')
        axes[0].set_ylabel('Source Agent')
        
        # Add colorbar
        cbar = plt.colorbar(im, ax=axes[0])
        cbar.set_label('Attention Weight')
        
        # Add text annotations
        for i in range(attention_matrix.shape[0]):
            for j in range(attention_matrix.shape[1]):
                text = axes[0].text(j, i, f'{attention_matrix[i, j]:.2f}',
                                  ha="center", va="center", color="white")
        
        # Communication graph
        if agent_positions is not None:
            self._plot_communication_graph(
                attention_matrix, agent_positions, axes[1], timestep
            )
        else:
            # Network graph without spatial positions
            G = nx.from_numpy_array(attention_matrix)
            pos = nx.spring_layout(G)
            nx.draw(G, pos, ax=axes[1], with_labels=True, 
                   node_color='lightblue', node_size=500)
            axes[1].set_title(f'Communication Graph (t={timestep})')
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Attention heatmap saved to {save_path}")
        
        return fig
    
    def _plot_communication_graph(
        self,
        attention_matrix: np.ndarray,
        agent_positions: np.ndarray,
        ax: plt.Axes,
        timestep: int
    ):
        """Plot communication graph with spatial positions."""
        # Create graph from attention matrix
        G = nx.from_numpy_array(attention_matrix)
        
        # Use agent positions as node positions
        pos = {i: (agent_positions[i, 0], agent_positions[i, 1]) 
               for i in range(len(agent_positions))}
        
        # Draw nodes
        nx.draw_networkx_nodes(G, pos, ax=ax, node_color='lightblue', 
                              node_size=300, alpha=0.8)
        
        # Draw edges with weights as thickness
        edges = G.edges()
        weights = [G[u][v]['weight'] for u, v in edges]
        
        # Normalize weights for visualization
        if weights:
            max_weight = max(weights)
            normalized_weights = [w/max_weight * 5 for w in weights]
            
            nx.draw_networkx_edges(G, pos, ax=ax, width=normalized_weights,
                                 alpha=0.6, edge_color='gray')
        
        # Draw labels
        nx.draw_networkx_labels(G, pos, ax=ax, font_size=8)
        
        ax.set_title(f'Spatial Communication (t={timestep})')
        ax.set_aspect('equal')
    
    def create_attention_timeline(
        self,
        attention_history: List[np.ndarray],
        agent_positions_history: Optional[List[np.ndarray]] = None,
        save_path: Optional[str] = None
    ) -> str:
        """
        Create an animated timeline of attention patterns.
        
        Args:
            attention_history: List of attention matrices over time
            agent_positions_history: Optional list of agent positions
            save_path: Path to save the animation
            
        Returns:
            Path to saved animation file
        """
        if not attention_history:
            raise ValueError("No attention history provided")
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        def animate(frame):
            axes[0].clear()
            axes[1].clear()
            
            attention_matrix = attention_history[frame]
            positions = (agent_positions_history[frame] 
                        if agent_positions_history else None)
            
            # Attention heatmap
            im = axes[0].imshow(attention_matrix, cmap='viridis', aspect='auto')
            axes[0].set_title(f'Attention Matrix (t={frame})')
            axes[0].set_xlabel('Target Agent')
            axes[0].set_ylabel('Source Agent')
            
            # Communication graph
            if positions is not None:
                self._plot_communication_graph(
                    attention_matrix, positions, axes[1], frame
                )
            else:
                G = nx.from_numpy_array(attention_matrix)
                pos = nx.spring_layout(G, seed=42)  # Fixed seed for consistency
                nx.draw(G, pos, ax=axes[1], with_labels=True,
                       node_color='lightblue', node_size=500)
                axes[1].set_title(f'Communication Graph (t={frame})')
        
        # Create animation
        anim = FuncAnimation(fig, animate, frames=len(attention_history),
                           interval=500, blit=False)
        
        # Save animation
        if save_path is None:
            save_path = self.save_dir / "attention_timeline.gif"
        
        anim.save(save_path, writer='pillow', fps=2)
        logger.info(f"Attention timeline saved to {save_path}")
        
        plt.close(fig)
        return str(save_path)
    
    def analyze_communication_patterns(
        self,
        attention_history: List[np.ndarray],
        agent_positions_history: Optional[List[np.ndarray]] = None
    ) -> Dict[str, Any]:
        """
        Analyze communication patterns and compute statistics.
        
        Args:
            attention_history: List of attention matrices over time
            agent_positions_history: Optional agent positions
            
        Returns:
            Dictionary of communication statistics
        """
        if not attention_history:
            return {}
        
        num_agents = attention_history[0].shape[0]
        num_timesteps = len(attention_history)
        
        # Initialize statistics
        stats = {
            'num_agents': num_agents,
            'num_timesteps': num_timesteps,
            'communication_frequency': np.zeros((num_agents, num_agents)),
            'average_attention': np.zeros((num_agents, num_agents)),
            'attention_variance': np.zeros((num_agents, num_agents)),
            'communication_efficiency': 0.0,
            'network_density': [],
            'clustering_coefficient': [],
            'path_length': []
        }
        
        # Compute per-timestep statistics
        for t, attention_matrix in enumerate(attention_history):
            # Communication frequency (non-zero attention)
            comm_mask = attention_matrix > 0.01  # Threshold for "communication"
            stats['communication_frequency'] += comm_mask.astype(float)
            
            # Average attention weights
            stats['average_attention'] += attention_matrix
            
            # Network analysis
            G = nx.from_numpy_array(attention_matrix)
            
            # Network density
            density = nx.density(G)
            stats['network_density'].append(density)
            
            # Clustering coefficient
            try:
                clustering = nx.average_clustering(G, weight='weight')
                stats['clustering_coefficient'].append(clustering)
            except:
                stats['clustering_coefficient'].append(0.0)
            
            # Average path length
            try:
                if nx.is_connected(G):
                    path_length = nx.average_shortest_path_length(G, weight='weight')
                else:
                    # For disconnected graphs, compute for largest component
                    largest_cc = max(nx.connected_components(G), key=len)
                    subgraph = G.subgraph(largest_cc)
                    path_length = nx.average_shortest_path_length(subgraph, weight='weight')
                stats['path_length'].append(path_length)
            except:
                stats['path_length'].append(float('inf'))
        
        # Normalize by number of timesteps
        stats['communication_frequency'] /= num_timesteps
        stats['average_attention'] /= num_timesteps
        
        # Compute variance
        attention_stack = np.stack(attention_history)
        stats['attention_variance'] = np.var(attention_stack, axis=0)
        
        # Communication efficiency (ratio of effective communication)
        total_possible_comm = num_agents * (num_agents - 1) * num_timesteps
        actual_comm = np.sum(stats['communication_frequency'] > 0.1) * num_timesteps
        stats['communication_efficiency'] = actual_comm / total_possible_comm
        
        # Convert lists to arrays for easier analysis
        stats['network_density'] = np.array(stats['network_density'])
        stats['clustering_coefficient'] = np.array(stats['clustering_coefficient'])
        stats['path_length'] = np.array(stats['path_length'])
        
        # Additional derived statistics
        stats['avg_network_density'] = np.mean(stats['network_density'])
        stats['avg_clustering'] = np.mean(stats['clustering_coefficient'])
        stats['avg_path_length'] = np.mean(stats['path_length'][stats['path_length'] < float('inf')])
        
        return stats
    
    def plot_communication_statistics(
        self,
        stats: Dict[str, Any],
        save_path: Optional[str] = None
    ) -> plt.Figure:
        """
        Plot comprehensive communication statistics.
        
        Args:
            stats: Statistics dictionary from analyze_communication_patterns
            save_path: Optional path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Communication frequency heatmap
        im1 = axes[0, 0].imshow(stats['communication_frequency'], cmap='Blues')
        axes[0, 0].set_title('Communication Frequency')
        axes[0, 0].set_xlabel('Target Agent')
        axes[0, 0].set_ylabel('Source Agent')
        plt.colorbar(im1, ax=axes[0, 0])
        
        # Average attention heatmap
        im2 = axes[0, 1].imshow(stats['average_attention'], cmap='viridis')
        axes[0, 1].set_title('Average Attention Weights')
        axes[0, 1].set_xlabel('Target Agent')
        axes[0, 1].set_ylabel('Source Agent')
        plt.colorbar(im2, ax=axes[0, 1])
        
        # Attention variance heatmap
        im3 = axes[0, 2].imshow(stats['attention_variance'], cmap='Reds')
        axes[0, 2].set_title('Attention Variance')
        axes[0, 2].set_xlabel('Target Agent')
        axes[0, 2].set_ylabel('Source Agent')
        plt.colorbar(im3, ax=axes[0, 2])
        
        # Network density over time
        axes[1, 0].plot(stats['network_density'])
        axes[1, 0].set_title('Network Density Over Time')
        axes[1, 0].set_xlabel('Timestep')
        axes[1, 0].set_ylabel('Density')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Clustering coefficient over time
        axes[1, 1].plot(stats['clustering_coefficient'])
        axes[1, 1].set_title('Clustering Coefficient Over Time')
        axes[1, 1].set_xlabel('Timestep')
        axes[1, 1].set_ylabel('Clustering')
        axes[1, 1].grid(True, alpha=0.3)
        
        # Path length over time
        valid_path_lengths = stats['path_length'][stats['path_length'] < float('inf')]
        axes[1, 2].plot(valid_path_lengths)
        axes[1, 2].set_title('Average Path Length Over Time')
        axes[1, 2].set_xlabel('Timestep')
        axes[1, 2].set_ylabel('Path Length')
        axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Communication statistics plot saved to {save_path}")
        
        return fig
    
    def create_interactive_attention_plot(
        self,
        attention_history: List[np.ndarray],
        agent_positions_history: Optional[List[np.ndarray]] = None,
        save_path: Optional[str] = None
    ) -> str:
        """
        Create an interactive Plotly visualization of attention patterns.
        
        Args:
            attention_history: List of attention matrices over time
            agent_positions_history: Optional agent positions
            save_path: Path to save HTML file
            
        Returns:
            Path to saved HTML file
        """
        if not attention_history:
            raise ValueError("No attention history provided")
        
        # Create subplots
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=('Attention Heatmap', 'Communication Network'),
            specs=[[{"type": "heatmap"}, {"type": "scatter"}]]
        )
        
        # Add heatmap
        attention_matrix = attention_history[0]
        heatmap = go.Heatmap(
            z=attention_matrix,
            colorscale='Viridis',
            showscale=True
        )
        fig.add_trace(heatmap, row=1, col=1)
        
        # Add network graph
        if agent_positions_history:
            positions = agent_positions_history[0]
            
            # Create network traces
            edge_trace = go.Scatter(
                x=[], y=[],
                line=dict(width=0.5, color='#888'),
                hoverinfo='none',
                mode='lines'
            )
            
            node_trace = go.Scatter(
                x=positions[:, 0], y=positions[:, 1],
                mode='markers',
                hoverinfo='text',
                marker=dict(
                    showscale=True,
                    colorscale='YlGnBu',
                    reversescale=True,
                    color=[],
                    size=10,
                    colorbar=dict(
                        thickness=15,
                        len=0.5,
                        x=1.1,
                        xanchor="left",
                        title="Node Connections"
                    ),
                    line_width=2
                )
            )
            
            fig.add_trace(edge_trace, row=1, col=2)
            fig.add_trace(node_trace, row=1, col=2)
        
        # Update layout
        fig.update_layout(
            title="Interactive Attention Visualization",
            showlegend=False,
            hovermode='closest',
            margin=dict(b=20,l=5,r=5,t=40),
            annotations=[ dict(
                text="Attention patterns and communication network",
                showarrow=False,
                xref="paper", yref="paper",
                x=0.005, y=-0.002,
                xanchor='left', yanchor='bottom',
                font=dict(color='#888', size=12)
            )],
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False)
        )
        
        # Save as HTML
        if save_path is None:
            save_path = self.save_dir / "interactive_attention.html"
        
        fig.write_html(save_path)
        logger.info(f"Interactive attention plot saved to {save_path}")
        
        return str(save_path)


def plot_attention_heatmap(
    attention_matrix: np.ndarray,
    save_path: Optional[str] = None,
    **kwargs
) -> plt.Figure:
    """
    Convenience function to create attention heatmap.
    
    Args:
        attention_matrix: NxN attention weight matrix
        save_path: Optional path to save figure
        **kwargs: Additional arguments for AttentionVisualizer
        
    Returns:
        matplotlib Figure object
    """
    visualizer = AttentionVisualizer()
    return visualizer.plot_attention_heatmap(
        attention_matrix, save_path=save_path, **kwargs
    )


def analyze_communication_patterns(
    attention_history: List[np.ndarray],
    **kwargs
) -> Dict[str, Any]:
    """
    Convenience function to analyze communication patterns.
    
    Args:
        attention_history: List of attention matrices over time
        **kwargs: Additional arguments for AttentionVisualizer
        
    Returns:
        Dictionary of communication statistics
    """
    visualizer = AttentionVisualizer()
    return visualizer.analyze_communication_patterns(attention_history, **kwargs)


if __name__ == "__main__":
    # Example usage
    import argparse
    
    parser = argparse.ArgumentParser(description="Attention Visualization Tool")
    parser.add_argument("--mode", choices=["heatmap", "timeline", "analysis"],
                       default="heatmap", help="Visualization mode")
    parser.add_argument("--data", type=str, help="Path to attention data")
    parser.add_argument("--output", type=str, default="attention_viz.png",
                       help="Output file path")
    
    args = parser.parse_args()
    
    # Create sample data for demonstration
    num_agents = 6
    num_timesteps = 50
    
    # Generate sample attention matrices
    attention_history = []
    for t in range(num_timesteps):
        # Create realistic attention pattern
        attention = np.random.exponential(0.3, (num_agents, num_agents))
        np.fill_diagonal(attention, 0)  # No self-attention
        
        # Normalize rows to sum to 1
        row_sums = attention.sum(axis=1, keepdims=True)
        attention = np.divide(attention, row_sums, 
                            out=np.zeros_like(attention), where=row_sums!=0)
        
        attention_history.append(attention)
    
    # Create visualizer
    visualizer = AttentionVisualizer()
    
    if args.mode == "heatmap":
        fig = visualizer.plot_attention_heatmap(
            attention_history[0], save_path=args.output
        )
        print(f"Attention heatmap saved to {args.output}")
        
    elif args.mode == "timeline":
        path = visualizer.create_attention_timeline(
            attention_history, save_path=args.output
        )
        print(f"Attention timeline saved to {path}")
        
    elif args.mode == "analysis":
        stats = visualizer.analyze_communication_patterns(attention_history)
        fig = visualizer.plot_communication_statistics(
            stats, save_path=args.output
        )
        print(f"Communication analysis saved to {args.output}")
        print(f"Communication efficiency: {stats['communication_efficiency']:.3f}")
        print(f"Average network density: {stats['avg_network_density']:.3f}")