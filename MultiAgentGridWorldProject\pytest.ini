[tool:pytest]
# Pytest configuration for SD-HRL project

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Warning filters
filterwarnings =
    ignore::DeprecationWarning:pettingzoo.*
    ignore::DeprecationWarning:.*SwigPy.*
    ignore::DeprecationWarning:.*swigvarlink.*
    ignore::DeprecationWarning:.*builtin type.*
    ignore::UserWarning:gymnasium.*

# Output options
addopts = 
    -v
    --tb=short
    --strict-markers
    --disable-warnings

# Markers
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests