# MultiAgentGridWorldProject - GPU-Accelerated Requirements
# Scalable Decentralized Hierarchical Reinforcement Learning Framework

# ============================================================================
# CORE DEEP LEARNING WITH GPU ACCELERATION
# ============================================================================
torch>=2.2.0                    # PyTorch with CUDA support for neural networks
torchvision>=0.17.0             # Computer vision utilities
torchaudio>=2.2.0               # Audio processing (for future multimodal agents)
pytorch-lightning>=2.2.1       # High-level training framework for HRL agents

# ============================================================================
# REINFORCEMENT LEARNING & MULTI-AGENT ENVIRONMENTS
# ============================================================================
gymnasium>=0.29.1               # Modern OpenAI Gym for environment interface
stable-baselines3>=2.2.1        # Baseline RL algorithms (PPO, A2C, etc.)
pettingzoo>=1.24.0              # Multi-agent environment suite (MPE scenarios)
supersuit>=3.9.0                # Environment wrappers for multi-agent systems

# SMAC (StarCraft Multi-Agent Challenge) - Optional
# pysc2>=3.0.0                  # Uncomment if using StarCraft II environments
# smac>=1.0.0                   # Uncomment for SMAC benchmarks

# ============================================================================
# HIERARCHICAL RL & ATTENTION MECHANISMS
# ============================================================================
einops>=0.7.0                   # Tensor operations for attention mechanisms
transformers>=4.36.0            # Transformer models for communication
xformers>=0.0.23                # Memory-efficient attention (GPU-optimized)

# ============================================================================
# CONFIGURATION MANAGEMENT
# ============================================================================
hydra-core>=1.3.2              # Configuration management for experiments
omegaconf>=2.3.0                # Configuration parsing and validation
pyyaml>=6.0                     # YAML file processing

# ============================================================================
# EXPERIMENT TRACKING & LOGGING
# ============================================================================
tensorboard>=2.15.0             # Training visualization and metrics
wandb>=0.16.0                   # Experiment tracking and collaboration
rich>=13.7.0                    # Beautiful terminal output and progress bars

# ============================================================================
# HYPERPARAMETER OPTIMIZATION
# ============================================================================
ray[tune]>=2.8.0                # Distributed hyperparameter optimization
optuna>=3.5.0                   # Bayesian optimization for hyperparameters

# ============================================================================
# GPU-ACCELERATED COMPUTING
# ============================================================================
cupy-cuda12x>=12.3.0            # GPU-accelerated NumPy (adjust CUDA version)
numba>=0.58.0                   # JIT compilation with CUDA support

# ============================================================================
# SCIENTIFIC COMPUTING & DATA PROCESSING
# ============================================================================
numpy>=1.24.0                   # Numerical computing foundation
scipy>=1.11.0                   # Scientific computing algorithms
pandas>=2.1.0                   # Data manipulation and analysis
networkx>=3.2.0                 # Graph algorithms for communication networks

# ============================================================================
# VISUALIZATION & ANIMATION
# ============================================================================
matplotlib>=3.8.0               # Plotting and visualization
seaborn>=0.13.0                 # Statistical data visualization
plotly>=5.17.0                  # Interactive plots and dashboards
Pillow>=10.1.0                  # Image processing for visualizations
imageio>=2.33.0                 # GIF creation and video I/O
moviepy>=1.0.3                  # Video editing for agent demonstrations

# ============================================================================
# ENVIRONMENT SIMULATION
# ============================================================================
pygame>=2.5.0                   # Game engine for GridWorld visualization
Box2D>=2.3.10                   # Physics simulation for dynamic environments

# ============================================================================
# TESTING & DEVELOPMENT
# ============================================================================
pytest>=7.4.0                  # Unit testing framework
pytest-mock>=3.12.0            # Mocking utilities for tests
pytest-cov>=4.1.0              # Code coverage analysis
black>=23.11.0                 # Code formatting
flake8>=6.1.0                   # Code linting

# ============================================================================
# UTILITIES
# ============================================================================
tqdm>=4.66.0                    # Progress bars for training loops
click>=8.1.0                    # Command-line interface utilities
colorama>=0.4.6                 # Cross-platform colored terminal text

# ============================================================================
# OPTIONAL: ADVANCED GPU ACCELERATION
# ============================================================================
 torch-tensorrt                # TensorRT integration for inference
 onnx>=1.15.0                  # Model export and interoperability
 onnxruntime-gpu>=1.16.0       # GPU-accelerated ONNX runtime