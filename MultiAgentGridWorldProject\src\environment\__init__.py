"""
SD-HRL Environment Module

Research-grade multi-agent environments with unified API, dynamic events,
curriculum learning, and comprehensive evaluation metrics.
"""

# Import utilities first to suppress warnings
from .utils import suppress_environment_warnings, configure_environment_logging

from .base_env import BaseMultiAgentEnv
from .grid_world import GridWorldEnv
from .dynamic_events import EventScheduler, MovingWallEvent, HazardPulseEvent, GoalRelocationEvent
from .mpe_adapter import MPEAdapter
from .smac_adapter import SM<PERSON><PERSON>dapter
from .wrappers import (
    TimeLimitWrapper,
    FailureInjectionWrapper,
    RewardClipWrapper,
    ObsNormalizationWrapper,
    MaskInvalidActionsWrapper
)
from .curriculum import CurriculumScheduler

__all__ = [
    "BaseMultiAgentEnv",
    "GridWorldEnv",
    "EventScheduler",
    "MovingWallEvent",
    "HazardPulseEvent", 
    "GoalRelocationEvent",
    "MPEAdapter",
    "SMACAdapter",
    "TimeLimitWrapper",
    "FailureInjectionWrapper",
    "RewardClipWrapper",
    "ObsNormalizationWrapper",
    "MaskInvalidActionsWrapper",
    "CurriculumScheduler",
    "suppress_environment_warnings",
    "configure_environment_logging"
]