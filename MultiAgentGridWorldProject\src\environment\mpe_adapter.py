"""
Multi-Particle Environment (MPE) Adapter

Unified adapter for PettingZoo MPE scenarios with enhanced features
for multi-agent research and evaluation.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from gymnasium import spaces
import logging

from .base_env import BaseMultiAgentEnv

logger = logging.getLogger(__name__)

try:
    # Try new import path first (mpe2)
    try:
        from pettingzoo.mpe2 import simple_spread_v3, simple_tag_v3, simple_adversary_v3
    except ImportError:
        # Fallback to old import path for backward compatibility
        import warnings
        with warnings.catch_warnings():
            warnings.simplefilter("ignore", DeprecationWarning)
            from pettingzoo.mpe import simple_spread_v3, simple_tag_v3, simple_adversary_v3
    PETTINGZOO_AVAILABLE = True
except ImportError:
    PETTINGZOO_AVAILABLE = False
    logger.warning("PettingZoo not available. MPE environments will not work.")


class MPEAdapter(BaseMultiAgentEnv):
    """
    Adapter for PettingZoo Multi-Particle Environment scenarios.
    
    Supports multiple MPE scenarios with unified interface and enhanced
    features for research evaluation.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        if not PETTINGZOO_AVAILABLE:
            raise ImportError("PettingZoo is required for MPE environments")
        
        # MPE configuration
        self.scenario = config.get("scenario", "simple_spread")
        self.continuous_actions = config.get("continuous_actions", False)
        self.max_cycles = config.get("max_cycles", 200)
        
        # Environment-specific parameters
        self.agent_radius = config.get("agent_radius", 0.05)
        self.agent_mass = config.get("agent_mass", 1.0)
        self.max_speed = config.get("max_speed", 0.1)
        self.world_size = config.get("world_size", 2.0)
        
        # Landmarks configuration
        self.landmark_size = config.get("landmark_size", 0.08)
        self.num_landmarks = config.get("num_landmarks", self.num_agents)
        self.landmark_movable = config.get("landmark_movable", False)
        
        # Reward configuration
        self.shared_reward = config.get("shared_reward", True)
        self.collision_penalty = config.get("collision_penalty", -1.0)
        self.distance_reward_scale = config.get("distance_reward_scale", 1.0)
        self.occupancy_reward = config.get("occupancy_reward", 0.5)
        
        # Observation configuration
        self.observe_landmarks = config.get("observe_landmarks", True)
        self.observe_other_agents = config.get("observe_other_agents", True)
        self.communication_range = config.get("communication_range", 0.5)
        
        # Initialize environment
        self._create_env()
        self._setup_spaces()
        
        # Tracking variables
        self.agent_positions = {}
        self.landmark_positions = {}
        self.collision_count = 0
        self.coverage_history = []
        
        logger.info(f"MPE {self.scenario} initialized with {self.num_agents} agents")
    
    def _create_env(self):
        """Create the PettingZoo MPE environment."""
        env_kwargs = {
            'N': self.num_agents,
            'max_cycles': self.max_cycles,
            'continuous_actions': self.continuous_actions
        }
        
        if self.scenario == "simple_spread":
            self.env = simple_spread_v3.parallel_env(**env_kwargs)
        elif self.scenario == "simple_tag":
            env_kwargs['num_good'] = max(1, self.num_agents - 1)
            env_kwargs['num_adversaries'] = 1
            env_kwargs['num_obstacles'] = 2
            self.env = simple_tag_v3.parallel_env(**env_kwargs)
        elif self.scenario == "simple_adversary":
            env_kwargs['N'] = max(2, self.num_agents)
            self.env = simple_adversary_v3.parallel_env(**env_kwargs)
        else:
            raise ValueError(f"Unsupported MPE scenario: {self.scenario}")
        
        # Reset to get agent list
        self.env.reset()
        self.agent_ids = list(self.env.agents)
        self.num_agents = len(self.agent_ids)
        self.active_agents = set(self.agent_ids)
    
    def _setup_spaces(self):
        """Setup action and observation spaces."""
        # Get spaces from the underlying environment
        sample_agent = self.agent_ids[0]
        self.action_space = self.env.action_space(sample_agent)
        self.observation_space = self.env.observation_space(sample_agent)
        
        logger.debug(f"Action space: {self.action_space}")
        logger.debug(f"Observation space: {self.observation_space}")
    
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset environment to initial state."""
        self.current_step = 0
        self.episode_count += 1
        self.collision_count = 0
        self.coverage_history.clear()
        
        # Reset metrics
        self.episode_metrics = {}
        
        # Reset underlying environment
        observations, infos = self.env.reset()
        
        # Update tracking
        self._update_positions(observations)
        
        # Process observations
        processed_obs = self._process_observations(observations)
        
        # Get info
        info = self._get_info()
        
        logger.debug(f"MPE environment reset, episode {self.episode_count}")
        return processed_obs, info
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Execute one environment step."""
        self.current_step += 1
        self.total_steps += 1
        
        # Filter actions for active agents
        filtered_actions = {
            agent_id: actions.get(agent_id, 0) 
            for agent_id in self.get_active_agents()
        }
        
        # Step environment
        observations, rewards, terminations, truncations, infos = self.env.step(filtered_actions)
        
        # Process terminations and truncations
        dones = {}
        for agent_id in self.get_active_agents():
            dones[agent_id] = terminations.get(agent_id, False) or truncations.get(agent_id, False)
        
        # Update tracking
        self._update_positions(observations)
        
        # Calculate enhanced rewards
        enhanced_rewards = self._calculate_enhanced_rewards(rewards, observations)
        
        # Process observations
        processed_obs = self._process_observations(observations)
        
        # Update metrics
        step_info = self._calculate_step_metrics(observations, enhanced_rewards)
        self._update_metrics(step_info)
        
        # Check episode termination
        episode_done = self.is_episode_done() or all(dones.values())
        if episode_done:
            for agent_id in self.get_active_agents():
                dones[agent_id] = True
            self._finalize_episode_metrics()
        
        # Get info
        info = self._get_info()
        info.update(step_info)
        
        return processed_obs, enhanced_rewards, dones, info
    
    def _update_positions(self, observations: Dict[str, np.ndarray]):
        """Update agent and landmark positions from observations."""
        # Extract positions from observations
        # This is scenario-specific and may need adjustment
        for agent_id, obs in observations.items():
            if agent_id in self.get_active_agents():
                # For simple_spread, first 2 elements are agent position
                if len(obs) >= 2:
                    self.agent_positions[agent_id] = obs[:2]
        
        # Extract landmark positions (scenario-specific)
        if self.scenario == "simple_spread" and len(observations) > 0:
            sample_obs = next(iter(observations.values()))
            # Landmarks are typically after agent positions in observation
            landmark_start = 4  # After agent pos (2) and velocity (2)
            self.landmark_positions = {}
            for i in range(self.num_landmarks):
                if landmark_start + i * 2 + 1 < len(sample_obs):
                    self.landmark_positions[f"landmark_{i}"] = sample_obs[landmark_start + i * 2:landmark_start + i * 2 + 2]
    
    def _process_observations(self, observations: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Process and enhance observations."""
        processed = {}
        
        for agent_id in self.get_active_agents():
            if agent_id in observations:
                obs = observations[agent_id].copy()
                
                # Add communication features if enabled
                if self.include_communication:
                    comm_features = self._get_communication_features(agent_id)
                    obs = np.concatenate([obs, comm_features])
                
                # Add coverage information
                coverage_info = self._get_coverage_info(agent_id)
                obs = np.concatenate([obs, coverage_info])
                
                processed[agent_id] = obs.astype(np.float32)
            else:
                # Provide zero observation for inactive agents
                processed[agent_id] = np.zeros(self.observation_space.shape, dtype=np.float32)
        
        return processed
    
    def _get_communication_features(self, agent_id: str) -> np.ndarray:
        """Get communication features for an agent."""
        comm_features = []
        
        if agent_id in self.agent_positions:
            agent_pos = self.agent_positions[agent_id]
            
            for other_id in self.agent_ids:
                if other_id != agent_id and other_id in self.agent_positions:
                    other_pos = self.agent_positions[other_id]
                    distance = np.linalg.norm(agent_pos - other_pos)
                    
                    # Communication strength based on distance
                    if distance <= self.communication_range:
                        comm_strength = 1.0 - (distance / self.communication_range)
                    else:
                        comm_strength = 0.0
                    
                    comm_features.append(comm_strength)
                else:
                    comm_features.append(0.0)
        else:
            comm_features = [0.0] * (self.num_agents - 1)
        
        return np.array(comm_features, dtype=np.float32)
    
    def _get_coverage_info(self, agent_id: str) -> np.ndarray:
        """Get coverage information for an agent."""
        coverage_info = []
        
        if agent_id in self.agent_positions:
            agent_pos = self.agent_positions[agent_id]
            
            # Distance to each landmark
            for landmark_id, landmark_pos in self.landmark_positions.items():
                distance = np.linalg.norm(agent_pos - landmark_pos)
                coverage_info.append(distance)
            
            # Coverage quality (how well landmarks are covered by all agents)
            total_coverage = self._calculate_total_coverage()
            coverage_info.append(total_coverage)
        else:
            coverage_info = [0.0] * (len(self.landmark_positions) + 1)
        
        return np.array(coverage_info, dtype=np.float32)
    
    def _calculate_enhanced_rewards(self, base_rewards: Dict[str, float], 
                                  observations: Dict[str, np.ndarray]) -> Dict[str, float]:
        """Calculate enhanced rewards with additional components."""
        enhanced_rewards = base_rewards.copy()
        
        # Add occupancy rewards
        occupancy_bonus = self._calculate_occupancy_bonus()
        
        # Add collision penalties
        collision_penalty = self._calculate_collision_penalty()
        
        # Add cooperation rewards
        cooperation_bonus = self._calculate_cooperation_bonus()
        
        for agent_id in self.get_active_agents():
            if agent_id in enhanced_rewards:
                enhanced_rewards[agent_id] += (
                    occupancy_bonus * self.occupancy_reward +
                    collision_penalty * self.collision_penalty +
                    cooperation_bonus
                )
        
        return enhanced_rewards
    
    def _calculate_occupancy_bonus(self) -> float:
        """Calculate bonus for landmark occupancy."""
        if not self.landmark_positions or not self.agent_positions:
            return 0.0
        
        occupied_landmarks = 0
        for landmark_pos in self.landmark_positions.values():
            min_distance = float('inf')
            for agent_pos in self.agent_positions.values():
                distance = np.linalg.norm(agent_pos - landmark_pos)
                min_distance = min(min_distance, distance)
            
            # Consider landmark occupied if an agent is close enough
            if min_distance < self.landmark_size + self.agent_radius:
                occupied_landmarks += 1
        
        return occupied_landmarks / len(self.landmark_positions)
    
    def _calculate_collision_penalty(self) -> float:
        """Calculate penalty for agent collisions."""
        collision_count = 0
        agent_positions = list(self.agent_positions.values())
        
        for i in range(len(agent_positions)):
            for j in range(i + 1, len(agent_positions)):
                distance = np.linalg.norm(agent_positions[i] - agent_positions[j])
                if distance < 2 * self.agent_radius:
                    collision_count += 1
        
        self.collision_count += collision_count
        return collision_count
    
    def _calculate_cooperation_bonus(self) -> float:
        """Calculate bonus for cooperative behavior."""
        if len(self.agent_positions) < 2:
            return 0.0
        
        # Bonus for maintaining good formation
        agent_positions = list(self.agent_positions.values())
        center = np.mean(agent_positions, axis=0)
        
        # Calculate spread around center
        distances_to_center = [np.linalg.norm(pos - center) for pos in agent_positions]
        spread_variance = np.var(distances_to_center)
        
        # Reward balanced spread (not too clustered, not too dispersed)
        optimal_spread = 0.5
        spread_bonus = max(0, 1.0 - abs(np.sqrt(spread_variance) - optimal_spread))
        
        return spread_bonus
    
    def _calculate_total_coverage(self) -> float:
        """Calculate total coverage quality."""
        if not self.landmark_positions or not self.agent_positions:
            return 0.0
        
        total_coverage = 0.0
        for landmark_pos in self.landmark_positions.values():
            min_distance = float('inf')
            for agent_pos in self.agent_positions.values():
                distance = np.linalg.norm(agent_pos - landmark_pos)
                min_distance = min(min_distance, distance)
            
            # Coverage quality decreases with distance
            coverage = max(0, 1.0 - min_distance / self.world_size)
            total_coverage += coverage
        
        return total_coverage / len(self.landmark_positions)
    
    def _calculate_step_metrics(self, observations: Dict[str, np.ndarray], 
                              rewards: Dict[str, float]) -> Dict[str, Any]:
        """Calculate step-level metrics."""
        step_info = {}
        
        # Coverage metrics
        coverage = self._calculate_total_coverage()
        step_info["coverage"] = coverage
        self.coverage_history.append(coverage)
        
        # Collision metrics
        step_info["collisions"] = self.collision_count
        
        # Cooperation metrics
        step_info["cooperation_events"] = self._calculate_cooperation_bonus()
        
        # Distance metrics
        if self.agent_positions and self.landmark_positions:
            total_distance = 0.0
            for agent_pos in self.agent_positions.values():
                min_dist = min(
                    np.linalg.norm(agent_pos - landmark_pos)
                    for landmark_pos in self.landmark_positions.values()
                )
                total_distance += min_dist
            step_info["avg_distance_to_landmarks"] = total_distance / len(self.agent_positions)
        
        return step_info
    
    def get_obs(self) -> Dict[str, np.ndarray]:
        """Get current observations for all agents."""
        # This is handled in the step method for MPE
        return {}
    
    def get_state(self) -> np.ndarray:
        """Get global state for centralized critic."""
        state = []
        
        # Agent positions and velocities
        for agent_id in self.agent_ids:
            if agent_id in self.agent_positions:
                state.extend(self.agent_positions[agent_id])
                # Add velocity if available (would need to track)
                state.extend([0.0, 0.0])  # Placeholder
            else:
                state.extend([0.0, 0.0, 0.0, 0.0])
        
        # Landmark positions
        for landmark_pos in self.landmark_positions.values():
            state.extend(landmark_pos)
        
        # Global metrics
        state.append(self._calculate_total_coverage())
        state.append(self.collision_count / max(1, self.current_step))
        
        return np.array(state, dtype=np.float32)
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment."""
        return self.env.render()
    
    def close(self):
        """Close the environment."""
        if hasattr(self.env, 'close'):
            self.env.close()
    
    def _get_info(self) -> Dict[str, Any]:
        """Get environment info."""
        info = {
            "step": self.current_step,
            "episode": self.episode_count,
            "scenario": self.scenario,
            "active_agents": len(self.active_agents),
            "agent_positions": self.agent_positions.copy(),
            "landmark_positions": self.landmark_positions.copy(),
            "collision_count": self.collision_count,
            "coverage": self._calculate_total_coverage()
        }
        
        return info