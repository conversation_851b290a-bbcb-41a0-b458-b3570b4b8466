defaults:
  - env: env_gridworld
  - agent: agent_hrl_icp
  - training: training_defaults
  - _self_

# Experiment identification
experiment_name: "sdhrl_gridworld"
experiment_group: "baseline"
tags: ["hierarchical", "communication", "gridworld"]

# Directory structure
log_dir: "logs/"
result_dir: "results/"
checkpoint_dir: "checkpoints/"
video_dir: "videos/"

# Hardware configuration
device: "cuda"
num_gpus: 1
mixed_precision: true

# Reproducibility
global_seed: 42
deterministic: false  # Set true for exact reproducibility (slower)

hydra:
  run:
    dir: ./outputs/${experiment_name}/${experiment_group}/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: true
  sweep:
    dir: ./multirun/${experiment_name}/${experiment_group}
    subdir: ${hydra:job.num}