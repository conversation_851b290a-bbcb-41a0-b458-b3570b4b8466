"""
Dynamic Event System

Provides event scheduling, failure injection, and environmental changes
for creating realistic and challenging multi-agent scenarios.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
import numpy as np
import json
import logging
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class EventType(Enum):
    """Types of dynamic events."""
    MOVING_WALL = "moving_wall"
    HAZARD_PULSE = "hazard_pulse"
    GOAL_RELOCATION = "goal_relocation"
    AGENT_FAILURE = "agent_failure"
    COMMUNICATION_NOISE = "communication_noise"
    REWARD_CHANGE = "reward_change"


@dataclass
class EventLog:
    """Log entry for a dynamic event."""
    timestep: int
    event_type: str
    event_id: str
    parameters: Dict[str, Any]
    success: bool = True
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


class Event(ABC):
    """Base class for dynamic events."""
    
    def __init__(self, event_id: str, trigger_probability: float = 1.0):
        self.event_id = event_id
        self.trigger_probability = trigger_probability
        self.is_active = False
        self.activation_step = -1
        
    @abstractmethod
    def trigger(self, env, timestep: int) -> bool:
        """
        Trigger the event in the environment.
        
        Args:
            env: Environment instance
            timestep: Current timestep
            
        Returns:
            bool: True if event was successfully triggered
        """
        pass
    
    @abstractmethod
    def update(self, env, timestep: int):
        """Update event state each timestep."""
        pass
    
    @abstractmethod
    def deactivate(self, env):
        """Deactivate the event."""
        pass
    
    def should_trigger(self, timestep: int) -> bool:
        """Check if event should trigger based on probability."""
        return np.random.random() < self.trigger_probability
    
    def get_parameters(self) -> Dict[str, Any]:
        """Get event parameters for logging."""
        return {
            "event_id": self.event_id,
            "trigger_probability": self.trigger_probability,
            "is_active": self.is_active,
            "activation_step": self.activation_step
        }


class MovingWallEvent(Event):
    """Event that creates moving obstacles in the environment."""
    
    def __init__(self, event_id: str, wall_positions: List[Tuple[int, int]], 
                 movement_pattern: str = "linear", speed: float = 0.1,
                 trigger_probability: float = 1.0):
        super().__init__(event_id, trigger_probability)
        self.wall_positions = wall_positions
        self.movement_pattern = movement_pattern
        self.speed = speed
        self.current_positions = wall_positions.copy()
        self.direction = 1  # 1 for forward, -1 for backward
        
    def trigger(self, env, timestep: int) -> bool:
        """Add moving walls to the environment."""
        if not hasattr(env, 'grid') or not self.should_trigger(timestep):
            return False
            
        try:
            # Add walls to grid
            for pos in self.wall_positions:
                if 0 <= pos[0] < env.grid.shape[0] and 0 <= pos[1] < env.grid.shape[1]:
                    env.grid[pos[0], pos[1]] = 1  # Wall marker
            
            self.is_active = True
            self.activation_step = timestep
            logger.info(f"MovingWallEvent {self.event_id} triggered at step {timestep}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to trigger MovingWallEvent {self.event_id}: {e}")
            return False
    
    def update(self, env, timestep: int):
        """Update wall positions based on movement pattern."""
        if not self.is_active or not hasattr(env, 'grid'):
            return
            
        # Clear old positions
        for pos in self.current_positions:
            if 0 <= pos[0] < env.grid.shape[0] and 0 <= pos[1] < env.grid.shape[1]:
                env.grid[pos[0], pos[1]] = 0
        
        # Update positions based on pattern
        if self.movement_pattern == "linear":
            self._update_linear_movement(env)
        elif self.movement_pattern == "circular":
            self._update_circular_movement(env, timestep)
        
        # Set new positions
        for pos in self.current_positions:
            if 0 <= pos[0] < env.grid.shape[0] and 0 <= pos[1] < env.grid.shape[1]:
                env.grid[pos[0], pos[1]] = 1
    
    def _update_linear_movement(self, env):
        """Update positions with linear back-and-forth movement."""
        new_positions = []
        for pos in self.current_positions:
            new_x = pos[0] + self.direction * self.speed
            
            # Bounce off boundaries
            if new_x <= 0 or new_x >= env.grid.shape[0] - 1:
                self.direction *= -1
                new_x = pos[0] + self.direction * self.speed
            
            new_positions.append((int(new_x), pos[1]))
        
        self.current_positions = new_positions
    
    def _update_circular_movement(self, env, timestep: int):
        """Update positions with circular movement."""
        center_x, center_y = env.grid.shape[0] // 2, env.grid.shape[1] // 2
        radius = min(center_x, center_y) // 2
        angle = (timestep - self.activation_step) * self.speed
        
        new_positions = []
        for i, _ in enumerate(self.current_positions):
            offset_angle = angle + (i * 2 * np.pi / len(self.current_positions))
            new_x = center_x + radius * np.cos(offset_angle)
            new_y = center_y + radius * np.sin(offset_angle)
            new_positions.append((int(new_x), int(new_y)))
        
        self.current_positions = new_positions
    
    def deactivate(self, env):
        """Remove moving walls from environment."""
        if hasattr(env, 'grid'):
            for pos in self.current_positions:
                if 0 <= pos[0] < env.grid.shape[0] and 0 <= pos[1] < env.grid.shape[1]:
                    env.grid[pos[0], pos[1]] = 0
        
        self.is_active = False
        logger.info(f"MovingWallEvent {self.event_id} deactivated")


class HazardPulseEvent(Event):
    """Event that creates pulsing hazard zones."""
    
    def __init__(self, event_id: str, hazard_zones: List[Tuple[int, int, int]], 
                 pulse_frequency: int = 10, damage: float = -1.0,
                 trigger_probability: float = 1.0):
        super().__init__(event_id, trigger_probability)
        self.hazard_zones = hazard_zones  # (x, y, radius)
        self.pulse_frequency = pulse_frequency
        self.damage = damage
        self.pulse_active = False
        
    def trigger(self, env, timestep: int) -> bool:
        """Activate hazard pulse system."""
        if not self.should_trigger(timestep):
            return False
            
        self.is_active = True
        self.activation_step = timestep
        logger.info(f"HazardPulseEvent {self.event_id} triggered at step {timestep}")
        return True
    
    def update(self, env, timestep: int):
        """Update hazard pulse state."""
        if not self.is_active:
            return
            
        # Toggle pulse based on frequency
        steps_since_activation = timestep - self.activation_step
        self.pulse_active = (steps_since_activation % self.pulse_frequency) < (self.pulse_frequency // 2)
        
        # Apply damage to agents in hazard zones
        if self.pulse_active and hasattr(env, 'agent_positions'):
            self._apply_hazard_damage(env)
    
    def _apply_hazard_damage(self, env):
        """Apply damage to agents in active hazard zones."""
        for agent_id, pos in env.agent_positions.items():
            for hx, hy, radius in self.hazard_zones:
                distance = np.sqrt((pos[0] - hx)**2 + (pos[1] - hy)**2)
                if distance <= radius:
                    # Apply damage through environment's reward system
                    if hasattr(env, 'apply_agent_penalty'):
                        env.apply_agent_penalty(agent_id, self.damage)
    
    def deactivate(self, env):
        """Deactivate hazard pulse."""
        self.is_active = False
        self.pulse_active = False
        logger.info(f"HazardPulseEvent {self.event_id} deactivated")


class GoalRelocationEvent(Event):
    """Event that relocates goals during episodes."""
    
    def __init__(self, event_id: str, new_goal_positions: List[Tuple[int, int]],
                 trigger_probability: float = 1.0):
        super().__init__(event_id, trigger_probability)
        self.new_goal_positions = new_goal_positions
        self.original_goals = None
        
    def trigger(self, env, timestep: int) -> bool:
        """Relocate goals to new positions."""
        if not hasattr(env, 'goal_locations') or not self.should_trigger(timestep):
            return False
            
        try:
            # Store original goals
            self.original_goals = env.goal_locations.copy()
            
            # Set new goal positions
            env.goal_locations = self.new_goal_positions.copy()
            
            # Update environment state
            if hasattr(env, 'update_goal_visualization'):
                env.update_goal_visualization()
            
            self.is_active = True
            self.activation_step = timestep
            logger.info(f"GoalRelocationEvent {self.event_id} triggered at step {timestep}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to trigger GoalRelocationEvent {self.event_id}: {e}")
            return False
    
    def update(self, env, timestep: int):
        """No continuous updates needed for goal relocation."""
        pass
    
    def deactivate(self, env):
        """Restore original goal positions."""
        if self.original_goals and hasattr(env, 'goal_locations'):
            env.goal_locations = self.original_goals.copy()
            
            if hasattr(env, 'update_goal_visualization'):
                env.update_goal_visualization()
        
        self.is_active = False
        logger.info(f"GoalRelocationEvent {self.event_id} deactivated")


class EventScheduler:
    """Manages and schedules dynamic events during episodes."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.events: List[Event] = []
        self.event_log: List[EventLog] = []
        self.scheduled_events: Dict[int, List[Event]] = {}  # timestep -> events
        
        # Event replay support
        self.replay_mode = config.get("replay_mode", False)
        self.replay_log_path = config.get("replay_log_path", None)
        
        if self.replay_mode and self.replay_log_path:
            self.load_replay_log(self.replay_log_path)
    
    def add_event(self, event: Event, trigger_timestep: Optional[int] = None):
        """Add an event to the scheduler."""
        self.events.append(event)
        
        if trigger_timestep is not None:
            if trigger_timestep not in self.scheduled_events:
                self.scheduled_events[trigger_timestep] = []
            self.scheduled_events[trigger_timestep].append(event)
    
    def update(self, env, timestep: int):
        """Update all events and trigger scheduled ones."""
        # Trigger scheduled events
        if timestep in self.scheduled_events:
            for event in self.scheduled_events[timestep]:
                success = event.trigger(env, timestep)
                self._log_event(timestep, event, success)
        
        # Update all active events
        for event in self.events:
            if event.is_active:
                event.update(env, timestep)
    
    def reset(self):
        """Reset all events for new episode."""
        for event in self.events:
            if event.is_active:
                event.is_active = False
        
        self.event_log.clear()
    
    def get_event_log(self) -> List[Dict[str, Any]]:
        """Get the current event log."""
        return [log.to_dict() for log in self.event_log]
    
    def save_event_log(self, filepath: str):
        """Save event log to file for replay."""
        try:
            with open(filepath, 'w') as f:
                json.dump(self.get_event_log(), f, indent=2)
            logger.info(f"Event log saved to {filepath}")
        except Exception as e:
            logger.error(f"Failed to save event log: {e}")
    
    def load_replay_log(self, filepath: str):
        """Load event log for deterministic replay."""
        try:
            with open(filepath, 'r') as f:
                log_data = json.load(f)
            
            # Schedule events based on replay log
            for entry in log_data:
                timestep = entry["timestep"]
                # Create events based on log entries for replay
                # This would need specific implementation based on event types
                
            logger.info(f"Replay log loaded from {filepath}")
        except Exception as e:
            logger.error(f"Failed to load replay log: {e}")
    
    def _log_event(self, timestep: int, event: Event, success: bool):
        """Log an event occurrence."""
        log_entry = EventLog(
            timestep=timestep,
            event_type=type(event).__name__,
            event_id=event.event_id,
            parameters=event.get_parameters(),
            success=success
        )
        self.event_log.append(log_entry)
        
        logger.debug(f"Event logged: {log_entry.event_type} at step {timestep}")