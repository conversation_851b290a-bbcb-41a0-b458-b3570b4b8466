#!/usr/bin/env python3
"""
Comprehensive test suite for SD-HRL utility modules
Tests all major functionality of attention_viz, metrics, and visualization modules
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_attention_viz():
    """Test attention visualization utilities"""
    print("🔍 Testing Attention Visualization...")
    
    try:
        from src.utils.attention_viz import AttentionVisualizer
        
        # Create test data
        n_agents = 4
        attention_matrix = np.random.rand(n_agents, n_agents)
        attention_matrix = attention_matrix / attention_matrix.sum(axis=1, keepdims=True)  # Normalize
        
        agent_positions = [(1, 1), (3, 3), (5, 2), (2, 4)]
        
        visualizer = AttentionVisualizer()
        
        # Test 1: Attention heatmap
        print("  ✓ Testing attention heatmap...")
        fig = visualizer.plot_attention_heatmap(attention_matrix)
        plt.savefig('test_attention_heatmap.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test 2: Communication graph (included in heatmap)
        print("  ✓ Testing communication graph...")
        fig = visualizer.plot_attention_heatmap(attention_matrix, agent_positions)
        plt.savefig('test_communication_graph.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test 3: Attention timeline with multiple timesteps
        print("  ✓ Testing attention timeline...")
        attention_history = [np.random.rand(n_agents, n_agents) for _ in range(5)]
        for i, att in enumerate(attention_history):
            attention_history[i] = att / att.sum(axis=1, keepdims=True)
        
        gif_path = visualizer.create_attention_timeline(attention_history, save_path='test_attention_timeline.gif')
        print(f"    - Timeline saved to: {gif_path}")
        
        # Test 4: Communication pattern analysis
        print("  ✓ Testing communication analysis...")
        stats = visualizer.analyze_communication_patterns(attention_history)
        print(f"    - Communication efficiency: {stats['communication_efficiency']:.3f}")
        print(f"    - Network density: {stats['network_density']:.3f}")
        print(f"    - Clustering coefficient: {stats['clustering_coefficient']:.3f}")
        
        print("  ✅ Attention visualization tests PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Attention visualization test FAILED: {e}")
        return False

def test_metrics():
    """Test metrics collection utilities"""
    print("📊 Testing Metrics Collection...")
    
    try:
        from src.utils.metrics import MetricsCollector
        
        collector = MetricsCollector()
        
        # Test episode tracking
        print("  ✓ Testing episode tracking...")
        agent_ids = ['agent_0', 'agent_1', 'agent_2']
        collector.start_episode(0, agent_ids)
        
        # Simulate episode steps
        for step in range(10):
            obs = {f'agent_{i}': np.random.rand(4) for i in range(3)}
            actions = {f'agent_{i}': np.random.randint(0, 4) for i in range(3)}
            rewards = {f'agent_{i}': np.random.rand() for i in range(3)}
            dones = {f'agent_{i}': False for i in range(3)}
            info = {'step_info': f'step_{step}'}
            
            collector.record_step(step, obs, actions, rewards, dones, info)
        
        # End episode
        episode_metrics = collector.end_episode(success=True)
        print(f"    - Episode length: {episode_metrics.episode_length}")
        print(f"    - Total reward: {episode_metrics.total_reward:.3f}")
        print(f"    - Success: {episode_metrics.success}")
        
        # Test multiple episodes
        print("  ✓ Testing multiple episodes...")
        for ep in range(1, 5):
            collector.start_episode(ep, agent_ids)
            for step in range(np.random.randint(5, 15)):
                obs = {f'agent_{i}': np.random.rand(4) for i in range(3)}
                actions = {f'agent_{i}': np.random.randint(0, 4) for i in range(3)}
                rewards = {f'agent_{i}': np.random.rand() for i in range(3)}
                dones = {f'agent_{i}': False for i in range(3)}
                info = {}
                collector.record_step(step, obs, actions, rewards, dones, info)
            collector.end_episode(success=np.random.rand() > 0.3)
        
        # Test analytics
        print("  ✓ Testing analytics...")
        summary = collector.get_summary_statistics()
        print(f"    - Episodes completed: {summary['total_episodes']}")
        print(f"    - Average reward: {summary['reward_stats']['mean']:.3f}")
        print(f"    - Success rate: {summary['success_rate']:.3f}")
        
        # Test plotting
        print("  ✓ Testing training curves...")
        fig = collector.plot_training_curves()
        plt.savefig('test_training_curves.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test data export
        print("  ✓ Testing data export...")
        collector.save_metrics('test_metrics_export.json')
        
        print("  ✅ Metrics collection tests PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Metrics collection test FAILED: {e}")
        return False

def test_visualization():
    """Test environment visualization utilities"""
    print("🎨 Testing Environment Visualization...")
    
    try:
        from src.utils.visualization import GridWorldVisualizer
        
        # Create test environment
        grid_size = (8, 8)
        visualizer = GridWorldVisualizer(grid_size)
        
        # Test 1: Basic grid rendering
        print("  ✓ Testing basic grid rendering...")
        grid = np.zeros(grid_size)
        grid[2:4, 2:4] = 1  # Add some obstacles
        
        agent_positions = [(1, 1), (6, 6), (3, 5)]
        goals = [(7, 7), (0, 0), (4, 4)]
        
        frame = visualizer.render_frame(grid, agent_positions, goals)
        plt.figure(figsize=(8, 8))
        plt.imshow(frame)
        plt.title('Test Grid World Rendering')
        plt.axis('off')
        plt.savefig('test_grid_rendering.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test 2: Agent trajectories
        print("  ✓ Testing trajectory visualization...")
        trajectories = {
            'agent_0': [(1, 1), (2, 1), (3, 1), (4, 2), (5, 3), (6, 4), (7, 5)],
            'agent_1': [(6, 6), (5, 5), (4, 4), (3, 3), (2, 2), (1, 1), (0, 0)],
            'agent_2': [(3, 5), (3, 4), (4, 4), (4, 3), (4, 4), (4, 4), (4, 4)]
        }
        
        fig = visualizer.plot_trajectories(trajectories, grid)
        plt.savefig('test_trajectories.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test 3: Communication visualization
        print("  ✓ Testing communication visualization...")
        attention_matrix = np.array([
            [0.1, 0.3, 0.6],
            [0.4, 0.2, 0.4],
            [0.2, 0.5, 0.3]
        ])
        
        frame_with_comm = visualizer.render_frame_with_communication(
            grid, agent_positions, goals, attention_matrix
        )
        plt.figure(figsize=(8, 8))
        plt.imshow(frame_with_comm)
        plt.title('Test Communication Visualization')
        plt.axis('off')
        plt.savefig('test_communication_viz.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test 4: Exploration heatmap
        print("  ✓ Testing exploration heatmap...")
        visit_counts = np.random.poisson(2, grid_size)
        fig = visualizer.plot_exploration_heatmap(visit_counts)
        plt.savefig('test_exploration_heatmap.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        # Test 5: Performance comparison
        print("  ✓ Testing performance comparison...")
        algorithms = ['SD-HRL', 'MADDPG', 'QMIX', 'Independent']
        rewards = [
            [100, 120, 140, 160, 180, 200, 220, 240, 260, 280],
            [80, 90, 100, 110, 120, 130, 140, 150, 160, 170],
            [70, 85, 95, 105, 115, 125, 135, 145, 155, 165],
            [60, 65, 70, 75, 80, 85, 90, 95, 100, 105]
        ]
        
        fig = visualizer.plot_algorithm_comparison(algorithms, rewards)
        plt.savefig('test_algorithm_comparison.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("  ✅ Environment visualization tests PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Environment visualization test FAILED: {e}")
        return False

def test_integration():
    """Test integration between different utility modules"""
    print("🔗 Testing Module Integration...")
    
    try:
        from src.utils.attention_viz import AttentionVisualizer
        from src.utils.metrics import MetricsCollector
        from src.utils.visualization import GridWorldVisualizer
        
        # Create integrated test scenario
        print("  ✓ Testing integrated workflow...")
        
        # Setup
        grid_size = (6, 6)
        n_agents = 3
        n_episodes = 3
        
        metrics_collector = MetricsCollector()
        attention_viz = AttentionVisualizer()
        env_viz = GridWorldVisualizer(grid_size)
        
        all_attention_data = []
        
        # Simulate training episodes
        for episode in range(n_episodes):
            agent_ids = [f'agent_{i}' for i in range(n_agents)]
            metrics_collector.start_episode(episode, agent_ids)
            
            episode_attention = []
            
            for step in range(10):
                # Generate synthetic data
                obs = {f'agent_{i}': np.random.rand(4) for i in range(n_agents)}
                actions = {f'agent_{i}': np.random.randint(0, 4) for i in range(n_agents)}
                rewards = {f'agent_{i}': np.random.rand() for i in range(n_agents)}
                dones = {f'agent_{i}': False for i in range(n_agents)}
                
                # Generate attention matrix
                attention = np.random.rand(n_agents, n_agents)
                attention = attention / attention.sum(axis=1, keepdims=True)
                episode_attention.append(attention)
                
                info = {'attention_matrix': attention}
                metrics_collector.record_step(step, obs, actions, rewards, dones, info)
            
            all_attention_data.extend(episode_attention)
            metrics_collector.end_episode(success=True)
        
        # Generate integrated visualizations
        print("  ✓ Creating integrated visualizations...")
        
        # Combined metrics and attention analysis
        summary = metrics_collector.get_summary_statistics()
        attention_stats = attention_viz.analyze_communication_patterns(all_attention_data)
        
        print(f"    - Training episodes: {summary['total_episodes']}")
        print(f"    - Average reward: {summary['reward_stats']['mean']:.3f}")
        print(f"    - Communication efficiency: {attention_stats['communication_efficiency']:.3f}")
        
        # Create comprehensive report
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        
        # Training curves
        metrics_collector.plot_training_curves(ax=axes[0, 0])
        axes[0, 0].set_title('Training Progress')
        
        # Attention heatmap
        avg_attention = np.mean(all_attention_data, axis=0)
        im = axes[0, 1].imshow(avg_attention, cmap='Blues')
        axes[0, 1].set_title('Average Attention Matrix')
        plt.colorbar(im, ax=axes[0, 1])
        
        # Communication timeline
        comm_efficiency = [attention_viz.analyze_communication_patterns([att])['communication_efficiency'] 
                          for att in all_attention_data]
        axes[1, 0].plot(comm_efficiency)
        axes[1, 0].set_title('Communication Efficiency Over Time')
        axes[1, 0].set_xlabel('Step')
        axes[1, 0].set_ylabel('Efficiency')
        
        # Network density
        network_density = [attention_viz.analyze_communication_patterns([att])['network_density'] 
                          for att in all_attention_data]
        axes[1, 1].plot(network_density)
        axes[1, 1].set_title('Network Density Over Time')
        axes[1, 1].set_xlabel('Step')
        axes[1, 1].set_ylabel('Density')
        
        plt.tight_layout()
        plt.savefig('test_integrated_report.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("  ✅ Integration tests PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Integration test FAILED: {e}")
        return False

def main():
    """Run all utility tests"""
    print("🚀 Starting SD-HRL Utility Tests")
    print("=" * 50)
    
    test_results = []
    
    # Run individual tests
    test_results.append(test_attention_viz())
    test_results.append(test_metrics())
    test_results.append(test_visualization())
    test_results.append(test_integration())
    
    # Summary
    print("\n" + "=" * 50)
    print("🏁 Test Results Summary")
    print("=" * 50)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED! ({passed}/{total})")
        print("\n🎉 SD-HRL utility modules are working perfectly!")
        print("\n📁 Generated test files:")
        test_files = [
            'test_attention_heatmap.png',
            'test_communication_graph.png', 
            'test_attention_timeline.png',
            'test_training_curves.png',
            'test_metrics_export.json',
            'test_grid_rendering.png',
            'test_trajectories.png',
            'test_communication_viz.png',
            'test_exploration_heatmap.png',
            'test_algorithm_comparison.png',
            'test_integrated_report.png'
        ]
        for file in test_files:
            if os.path.exists(file):
                print(f"  ✓ {file}")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)