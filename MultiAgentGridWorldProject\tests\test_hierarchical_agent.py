"""
Tests for Hierarchical Agent Controller

Tests the core HRL controller that integrates all components.
"""

import pytest
import torch
import numpy as np
from unittest.mock import Mock, patch

from src.controllers.hierarchical_agent import HierarchicalAgent, OptionTracker, ExperienceBuffer
from src.controllers.base_agent import BaseAgent


class TestOptionTracker:
    """Test option tracking functionality."""
    
    @pytest.fixture
    def tracker(self):
        return OptionTracker(max_option_duration=5)
    
    def test_initial_state(self, tracker):
        """Test initial tracker state."""
        assert tracker.current_option is None
        assert tracker.option_duration == 0
        assert len(tracker.option_counts) == 0
    
    def test_set_option(self, tracker):
        """Test option setting and tracking."""
        tracker.set_option(0, current_step=10)
        
        assert tracker.current_option == 0
        assert tracker.option_start_step == 10
        assert tracker.option_duration == 0
        assert tracker.option_counts[0] == 1
    
    def test_option_duration_tracking(self, tracker):
        """Test option duration tracking."""
        tracker.set_option(1, current_step=0)
        
        for i in range(3):
            tracker.step()
            assert tracker.option_duration == i + 1
    
    def test_max_duration_termination(self, tracker):
        """Test termination due to max duration."""
        tracker.set_option(0, current_step=0)
        
        # Should not terminate before max duration
        for i in range(4):
            tracker.step()
            assert not tracker.should_terminate()
        
        # Should terminate at max duration
        tracker.step()
        assert tracker.should_terminate()
    
    def test_option_history(self, tracker):
        """Test option history tracking."""
        # First option
        tracker.set_option(0, current_step=0)
        for _ in range(3):
            tracker.step()
        
        # Second option (should record first option's duration)
        tracker.set_option(1, current_step=3)
        
        assert len(tracker.option_history) == 1
        assert tracker.option_history[0] == (0, 3)  # (option, duration)
        assert tracker.option_durations[0] == [3]
    
    def test_metrics(self, tracker):
        """Test metrics generation."""
        tracker.set_option(0, current_step=0)
        tracker.step()
        tracker.step()
        
        metrics = tracker.get_metrics()
        
        assert metrics["current_option"] == 0
        assert metrics["option_duration"] == 2
        assert metrics["option_counts"][0] == 1
    
    def test_reset(self, tracker):
        """Test tracker reset."""
        tracker.set_option(0, current_step=0)
        tracker.step()
        
        tracker.reset()
        
        assert tracker.current_option is None
        assert tracker.option_duration == 0
        assert tracker.option_start_step == 0


class TestExperienceBuffer:
    """Test experience buffer functionality."""
    
    @pytest.fixture
    def buffer(self):
        return ExperienceBuffer(capacity=100)
    
    def test_initial_state(self, buffer):
        """Test initial buffer state."""
        assert len(buffer) == 0
        assert len(buffer.buffer) == 0
        assert len(buffer.option_buffer) == 0
    
    def test_add_transition(self, buffer):
        """Test adding transitions."""
        transition = {
            "observation": torch.randn(4),
            "action": 1,
            "reward": 0.5
        }
        
        buffer.add_transition(transition)
        
        assert len(buffer) == 1
        assert buffer.buffer[0] == transition
    
    def test_add_option_transition(self, buffer):
        """Test adding option transitions."""
        option_transition = {
            "state": torch.randn(4),
            "option": 2,
            "reward": 1.0
        }
        
        buffer.add_option_transition(option_transition)
        
        assert len(buffer.option_buffer) == 1
        assert buffer.option_buffer[0] == option_transition
    
    def test_sampling(self, buffer):
        """Test transition sampling."""
        # Add multiple transitions
        for i in range(10):
            transition = {"step": i, "data": torch.randn(2)}
            buffer.add_transition(transition)
        
        # Sample batch
        batch = buffer.sample(5)
        
        assert len(batch) == 5
        assert all("step" in t for t in batch)
        assert all("data" in t for t in batch)
    
    def test_sampling_insufficient_data(self, buffer):
        """Test sampling when buffer has insufficient data."""
        # Add only 2 transitions
        for i in range(2):
            buffer.add_transition({"step": i})
        
        # Request 5 transitions
        batch = buffer.sample(5)
        
        # Should return all available transitions
        assert len(batch) == 2
    
    def test_capacity_limit(self):
        """Test buffer capacity limits."""
        buffer = ExperienceBuffer(capacity=3)
        
        # Add more transitions than capacity
        for i in range(5):
            buffer.add_transition({"step": i})
        
        # Should only keep last 3
        assert len(buffer) == 3
        steps = [t["step"] for t in buffer.buffer]
        assert steps == [2, 3, 4]
    
    def test_clear(self, buffer):
        """Test buffer clearing."""
        buffer.add_transition({"data": 1})
        buffer.add_option_transition({"option": 0})
        
        buffer.clear()
        
        assert len(buffer) == 0
        assert len(buffer.option_buffer) == 0


class TestHierarchicalAgent:
    """Test hierarchical agent controller."""
    
    @pytest.fixture
    def config(self):
        return {
            "policy": {
                "num_options": 4,
                "obs_dim": 10,  # 8 (state) + 2 (position) = 10
                "action_dim": 4,
                "hidden_dim": 64
            },
            "use_communication": False,
            "max_option_duration": 10,
            "option_frequency": 4,
            "buffer_capacity": 1000,
            "batch_size": 32,
            "deadlock_threshold": 20
        }
    
    @pytest.fixture
    def agent(self, config):
        return HierarchicalAgent("test_agent", config)
    
    def test_agent_initialization(self, agent, config):
        """Test agent initialization."""
        assert agent.agent_id == "test_agent"
        assert agent.config == config
        assert isinstance(agent.option_tracker, OptionTracker)
        assert isinstance(agent.buffer, ExperienceBuffer)
        assert agent.comm_module is None  # Communication disabled
    
    def test_agent_with_communication(self, config):
        """Test agent initialization with communication."""
        config["use_communication"] = True
        config["communication"] = {
            "num_agents": 4,
            "communication_dim": 64
        }
        
        agent = HierarchicalAgent("comm_agent", config)
        
        assert agent.use_communication is True
        assert agent.comm_module is not None
    
    def test_initial_option_sampled(self, agent):
        """Test that option is sampled at t=0."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # First action should sample option
        action = agent.act(observation)
        
        assert isinstance(action, dict)
        assert "action" in action
        assert agent.option_tracker.current_option is not None
        assert 0 <= agent.option_tracker.current_option < 4  # num_options
    
    def test_worker_action_valid(self, agent):
        """Test that worker returns valid actions."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        action = agent.act(observation)
        
        assert isinstance(action["action"], int)
        assert 0 <= action["action"] < 4  # action_dim
    
    def test_option_duration(self, agent):
        """Test that option is held for at least K steps."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # First action
        agent.act(observation)
        first_option = agent.option_tracker.current_option
        
        # Next few actions should keep same option
        for i in range(3):  # Less than option_frequency (4)
            agent.step()
            agent.act(observation)
            assert agent.option_tracker.current_option == first_option
    
    def test_option_frequency_resampling(self, agent):
        """Test option resampling at specified frequency."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # First action
        agent.act(observation)
        first_option = agent.option_tracker.current_option
        
        # Advance to option_frequency steps
        for i in range(agent.option_frequency):
            agent.step()
            agent.act(observation)
        
        # Option might have changed (stochastic, but duration should reset)
        assert agent.option_tracker.option_duration <= 1  # Should be reset
    
    def test_buffer_logging(self, agent):
        """Test that transitions are stored correctly."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        initial_buffer_size = len(agent.buffer)
        
        # Take action
        agent.act(observation)
        
        # Buffer should have new transition
        assert len(agent.buffer) == initial_buffer_size + 1
        
        # Check transition content
        transition = agent.buffer.buffer[-1]
        assert "observation" in transition
        assert "option" in transition
        assert "action" in transition
        assert "termination_prob" in transition
    
    def test_deadlock_detection(self, agent):
        """Test deadlock detection and recovery."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # Take initial action
        agent.act(observation)
        initial_option = agent.option_tracker.current_option
        
        # Manually set no_reward_steps to trigger deadlock detection
        agent.no_reward_steps = agent.deadlock_threshold - 1
        
        # One step with no reward should trigger deadlock detection
        agent.step(reward=0.0)
        
        # The counter should be reset after deadlock detection
        assert agent.no_reward_steps == 0
    
    def test_learning_integration(self, agent):
        """Test learning integration."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # Fill buffer with enough transitions
        for i in range(agent.batch_size + 5):
            agent.act(observation)
            agent.step(reward=np.random.random())
        
        # Learning should be triggered
        metrics = agent.learn()
        
        assert isinstance(metrics, dict)
        # Should have some learning metrics (exact keys depend on policy implementation)
        assert len(metrics) > 0
    
    def test_metrics_collection(self, agent):
        """Test comprehensive metrics collection."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # Take some actions
        for i in range(5):
            agent.act(observation)
            agent.step(reward=0.1)
        
        metrics = agent.get_metrics()
        
        # Check base metrics
        assert "episode_step" in metrics
        assert "total_steps" in metrics
        assert "episode_reward" in metrics
        
        # Check hierarchical metrics
        assert "current_option" in metrics
        assert "option_duration" in metrics
        assert "buffer_size" in metrics
        assert "no_reward_steps" in metrics
    
    def test_state_save_load(self, agent):
        """Test state saving and loading."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # Take some actions to create state
        for i in range(3):
            agent.act(observation)
            agent.step(reward=0.1)
        
        # Save state
        state = agent.save_state()
        
        # Create new agent and load state
        new_agent = HierarchicalAgent("test_agent_2", agent.config)
        new_agent.load_state(state)
        
        # Check that key state was transferred
        assert new_agent.total_steps == agent.total_steps
        assert new_agent.episode_reward == agent.episode_reward
    
    def test_reset_functionality(self, agent):
        """Test agent reset."""
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # Take actions and accumulate state
        for i in range(5):
            agent.act(observation)
            agent.step(reward=0.1)
        
        # Reset
        agent.reset()
        
        # Check reset state
        assert agent.episode_step == 0
        assert agent.episode_reward == 0.0
        assert agent.option_tracker.current_option is None
        assert agent.no_reward_steps == 0
    
    def test_communication_features(self, config):
        """Test communication feature extraction."""
        config["use_communication"] = True
        config["communication"] = {"communication_dim": 64}
        
        agent = HierarchicalAgent("comm_agent", config)
        
        observation = {
            "state": torch.randn(8),
            "position": torch.tensor([1.0, 2.0])
        }
        
        # Take action (should extract communication features)
        agent.act(observation)
        
        assert agent.last_communication_features is not None
        assert isinstance(agent.last_communication_features, torch.Tensor)


def test_communication_integration():
    """Test integration with communication module."""
    config = {
        "policy": {
            "num_options": 4,
            "observation_dim": 8,
            "action_dim": 4
        },
        "use_communication": True,
        "communication": {
            "num_agents": 2,
            "communication_dim": 64
        }
    }
    
    agent1 = HierarchicalAgent("agent_1", config)
    agent2 = HierarchicalAgent("agent_2", config)
    
    # Create agent features for communication
    agent_features = {
        "agent_1": torch.randn(64),
        "agent_2": torch.randn(64)
    }
    
    # Test communication
    updated_features = agent1.communicate(agent_features)
    
    assert isinstance(updated_features, dict)
    assert "agent_1" in updated_features
    assert "agent_2" in updated_features
    assert updated_features["agent_1"].shape == agent_features["agent_1"].shape


if __name__ == "__main__":
    pytest.main([__file__, "-v"])