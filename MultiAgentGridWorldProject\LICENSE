MIT License

Copyright (c) 2025 SD-HRL Research Team

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.

---

## Additional Terms for Research Use

This software is provided for research and educational purposes. If you use this
software in your research, please cite our work:

```bibtex
@misc{sdhrl2025,
  title={SD-HRL: Spatially-Distributed Hierarchical Reinforcement Learning for Multi-Agent Coordination},
  author={SD-HRL Research Team},
  year={2025},
  url={https://github.com/your-username/MultiAgentGridWorldProject},
  note={Complete framework with benchmarks and real-world applications}
}
```

## Third-Party Licenses

This project includes or depends on several third-party libraries:

- PyTorch: BSD-style license
- NumPy: BSD license  
- Matplotlib: PSF-based license
- Hydra: MIT license
- PettingZoo: MIT license
- SMAC: MIT license

Please refer to the individual libraries for their specific license terms.

## Research Data and Models

Any pretrained models, datasets, or experimental results included in this
repository are provided under the same MIT license terms, unless otherwise
specified in the individual files or directories.

## Commercial Use

While this software is provided under the MIT license allowing commercial use,
we encourage commercial users to:

1. Contribute improvements back to the open source community
2. Cite our research in any publications or products
3. Consider collaboration opportunities with the research team

## Contact

For questions about licensing or commercial use, please contact:
- GitHub Issues: https://github.com/your-username/MultiAgentGridWorldProject/issues
- Email: <EMAIL>

---

Last Updated: January 26, 2025