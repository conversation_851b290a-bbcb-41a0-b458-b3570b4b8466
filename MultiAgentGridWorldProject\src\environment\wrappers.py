"""
Environment Wrappers

Modular and composable wrappers for multi-agent environments.
Provides failure injection, normalization, time limits, and other
research-oriented modifications.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Union
from gymnasium import spaces
import logging
from abc import ABC, abstractmethod

from .base_env import BaseMultiAgentEnv

logger = logging.getLogger(__name__)


class BaseWrapper(ABC):
    """Base class for environment wrappers."""
    
    def __init__(self, env: BaseMultiAgentEnv):
        self.env = env
        self.unwrapped = env.unwrapped
        
        # Copy important attributes
        self.num_agents = env.num_agents
        self.agent_ids = env.agent_ids
        self.active_agents = env.active_agents.copy()
        self.action_space = env.action_space
        self.observation_space = env.observation_space
        self.episode_limit = env.episode_limit
    
    def __getattr__(self, name):
        """Delegate attribute access to wrapped environment."""
        return getattr(self.env, name)
    
    def reset(self) -> <PERSON><PERSON>[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset the environment."""
        return self.env.reset()
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Step the environment."""
        return self.env.step(actions)
    
    def close(self):
        """Close the environment."""
        return self.env.close()
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment."""
        return self.env.render(mode)


class TimeLimitWrapper(BaseWrapper):
    """
    Wrapper that enforces episode time limits.
    
    Adds time-based termination and time information to observations.
    """
    
    def __init__(self, env: BaseMultiAgentEnv, max_episode_steps: Optional[int] = None):
        super().__init__(env)
        
        if max_episode_steps is None:
            max_episode_steps = getattr(env, 'episode_limit', 200)
        
        self.max_episode_steps = max_episode_steps
        self.current_step = 0
        
        # Modify observation space to include time information
        if hasattr(env, 'observation_space'):
            if isinstance(env.observation_space, spaces.Box):
                # Add one dimension for time information
                old_shape = env.observation_space.shape
                new_shape = (old_shape[0] + 1,)
                self.observation_space = spaces.Box(
                    low=0, high=1, shape=new_shape, dtype=env.observation_space.dtype
                )
        
        logger.info(f"TimeLimitWrapper applied with max_steps={max_episode_steps}")
    
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset environment and step counter."""
        self.current_step = 0
        observations, info = self.env.reset()
        
        # Add time information to observations
        observations = self._add_time_info(observations)
        info['time_limit'] = self.max_episode_steps
        
        return observations, info
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Step environment with time limit enforcement."""
        self.current_step += 1
        
        observations, rewards, dones, info = self.env.step(actions)
        
        # Add time information to observations
        observations = self._add_time_info(observations)
        
        # Check time limit
        if self.current_step >= self.max_episode_steps:
            for agent_id in self.get_active_agents():
                dones[agent_id] = True
            info['time_limit_reached'] = True
        
        info['steps_remaining'] = self.max_episode_steps - self.current_step
        
        return observations, rewards, dones, info
    
    def _add_time_info(self, observations: Dict[str, np.ndarray]) -> Dict[str, np.ndarray]:
        """Add time information to observations."""
        time_ratio = self.current_step / self.max_episode_steps
        
        enhanced_obs = {}
        for agent_id, obs in observations.items():
            # Append time information
            time_obs = np.append(obs, time_ratio)
            enhanced_obs[agent_id] = time_obs.astype(obs.dtype)
        
        return enhanced_obs


class FailureInjectionWrapper(BaseWrapper):
    """
    Wrapper that injects agent failures during episodes.
    
    Simulates realistic scenarios where agents may fail or become
    temporarily unavailable.
    """
    
    def __init__(self, env: BaseMultiAgentEnv, failure_probability: float = 0.01,
                 recovery_probability: float = 0.1, max_simultaneous_failures: int = 1):
        super().__init__(env)
        
        self.failure_probability = failure_probability
        self.recovery_probability = recovery_probability
        self.max_simultaneous_failures = max_simultaneous_failures
        
        self.failed_agents = set()
        self.failure_history = []
        
        logger.info(f"FailureInjectionWrapper applied with failure_prob={failure_probability}")
    
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset environment and failure state."""
        self.failed_agents.clear()
        self.failure_history.clear()
        
        observations, info = self.env.reset()
        info['failed_agents'] = list(self.failed_agents)
        
        return observations, info
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Step environment with failure injection."""
        # Process failures and recoveries
        self._update_failures()
        
        # Filter actions for non-failed agents
        filtered_actions = {}
        for agent_id, action in actions.items():
            if agent_id not in self.failed_agents:
                filtered_actions[agent_id] = action
            # Failed agents take no action (or default action)
        
        # Step environment
        observations, rewards, dones, info = self.env.step(filtered_actions)
        
        # Zero out observations and rewards for failed agents
        for agent_id in self.failed_agents:
            if agent_id in observations:
                observations[agent_id] = np.zeros_like(observations[agent_id])
            if agent_id in rewards:
                rewards[agent_id] = 0.0
        
        # Add failure information to info
        info['failed_agents'] = list(self.failed_agents)
        info['failure_count'] = len(self.failed_agents)
        info['failure_history'] = self.failure_history.copy()
        
        return observations, rewards, dones, info
    
    def _update_failures(self):
        """Update agent failure states."""
        # Recovery for failed agents
        recovered_agents = []
        for agent_id in self.failed_agents.copy():
            if np.random.random() < self.recovery_probability:
                self.failed_agents.remove(agent_id)
                recovered_agents.append(agent_id)
                self.failure_history.append({
                    'step': getattr(self.env, 'current_step', 0),
                    'agent': agent_id,
                    'event': 'recovery'
                })
                logger.info(f"Agent {agent_id} recovered from failure")
        
        # New failures for active agents
        if len(self.failed_agents) < self.max_simultaneous_failures:
            for agent_id in self.get_active_agents():
                if (agent_id not in self.failed_agents and 
                    np.random.random() < self.failure_probability):
                    self.failed_agents.add(agent_id)
                    self.failure_history.append({
                        'step': getattr(self.env, 'current_step', 0),
                        'agent': agent_id,
                        'event': 'failure'
                    })
                    logger.info(f"Agent {agent_id} failed")
                    break  # Only one failure per step
    
    def get_active_agents(self) -> List[str]:
        """Get list of currently active (non-failed) agents."""
        return [agent_id for agent_id in self.env.get_active_agents() 
                if agent_id not in self.failed_agents]


class RewardClipWrapper(BaseWrapper):
    """
    Wrapper that clips rewards to a specified range.
    
    Useful for stabilizing training and handling extreme reward values.
    """
    
    def __init__(self, env: BaseMultiAgentEnv, min_reward: float = -10.0, 
                 max_reward: float = 10.0):
        super().__init__(env)
        
        self.min_reward = min_reward
        self.max_reward = max_reward
        
        logger.info(f"RewardClipWrapper applied with range=[{min_reward}, {max_reward}]")
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Step environment with reward clipping."""
        observations, rewards, dones, info = self.env.step(actions)
        
        # Clip rewards
        clipped_rewards = {}
        for agent_id, reward in rewards.items():
            original_reward = reward
            clipped_reward = np.clip(reward, self.min_reward, self.max_reward)
            clipped_rewards[agent_id] = clipped_reward
            
            # Log if clipping occurred
            if abs(original_reward - clipped_reward) > 1e-6:
                logger.debug(f"Reward clipped for {agent_id}: {original_reward} -> {clipped_reward}")
        
        return observations, clipped_rewards, dones, info


class ObsNormalizationWrapper(BaseWrapper):
    """
    Wrapper that normalizes observations using running statistics.
    
    Maintains running mean and standard deviation for observation normalization.
    """
    
    def __init__(self, env: BaseMultiAgentEnv, epsilon: float = 1e-8, 
                 clip_obs: float = 10.0):
        super().__init__(env)
        
        self.epsilon = epsilon
        self.clip_obs = clip_obs
        
        # Initialize running statistics for each agent
        self.obs_rms = {}
        self.initialized = False
        
        logger.info(f"ObsNormalizationWrapper applied with epsilon={epsilon}")
    
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset environment and initialize normalization if needed."""
        observations, info = self.env.reset()
        
        # Initialize running statistics on first reset
        if not self.initialized:
            self._initialize_rms(observations)
            self.initialized = True
        
        # Normalize observations
        normalized_obs = self._normalize_observations(observations, update_stats=False)
        
        return normalized_obs, info
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Step environment with observation normalization."""
        observations, rewards, dones, info = self.env.step(actions)
        
        # Normalize observations and update statistics
        normalized_obs = self._normalize_observations(observations, update_stats=True)
        
        return normalized_obs, rewards, dones, info
    
    def _initialize_rms(self, observations: Dict[str, np.ndarray]):
        """Initialize running mean and std for each agent."""
        for agent_id, obs in observations.items():
            self.obs_rms[agent_id] = RunningMeanStd(shape=obs.shape)
    
    def _normalize_observations(self, observations: Dict[str, np.ndarray], 
                              update_stats: bool = True) -> Dict[str, np.ndarray]:
        """Normalize observations using running statistics."""
        normalized = {}
        
        for agent_id, obs in observations.items():
            if agent_id not in self.obs_rms:
                self.obs_rms[agent_id] = RunningMeanStd(shape=obs.shape)
            
            rms = self.obs_rms[agent_id]
            
            if update_stats:
                rms.update(obs)
            
            # Normalize
            normalized_obs = (obs - rms.mean) / np.sqrt(rms.var + self.epsilon)
            
            # Clip to prevent extreme values
            if self.clip_obs > 0:
                normalized_obs = np.clip(normalized_obs, -self.clip_obs, self.clip_obs)
            
            normalized[agent_id] = normalized_obs.astype(obs.dtype)
        
        return normalized


class MaskInvalidActionsWrapper(BaseWrapper):
    """
    Wrapper that masks invalid actions for environments with variable action spaces.
    
    Useful for environments where not all actions are valid at all times.
    """
    
    def __init__(self, env: BaseMultiAgentEnv):
        super().__init__(env)
        
        # Check if environment supports action masking
        self.supports_action_masking = hasattr(env, 'get_avail_actions')
        
        if self.supports_action_masking:
            logger.info("MaskInvalidActionsWrapper applied")
        else:
            logger.warning("Environment doesn't support action masking")
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Step environment with action masking."""
        if self.supports_action_masking:
            # Get available actions
            avail_actions = self.env.get_avail_actions()
            
            # Mask invalid actions
            masked_actions = {}
            for agent_id, action in actions.items():
                if agent_id in avail_actions:
                    avail = avail_actions[agent_id]
                    if isinstance(action, int) and action < len(avail):
                        # For discrete actions, check if action is available
                        if avail[action] == 1:
                            masked_actions[agent_id] = action
                        else:
                            # Find first available action as fallback
                            masked_actions[agent_id] = np.argmax(avail)
                    else:
                        masked_actions[agent_id] = action
                else:
                    masked_actions[agent_id] = action
            
            observations, rewards, dones, info = self.env.step(masked_actions)
            
            # Add action mask information to info
            info['action_masks'] = avail_actions
            
        else:
            observations, rewards, dones, info = self.env.step(actions)
        
        return observations, rewards, dones, info
    
    def get_action_masks(self) -> Dict[str, np.ndarray]:
        """Get action masks for all agents."""
        if self.supports_action_masking:
            return self.env.get_avail_actions()
        else:
            # Return all actions as valid
            masks = {}
            for agent_id in self.get_active_agents():
                if hasattr(self.action_space, 'n'):
                    masks[agent_id] = np.ones(self.action_space.n, dtype=np.int32)
                else:
                    masks[agent_id] = np.array([1], dtype=np.int32)
            return masks


class RunningMeanStd:
    """
    Tracks the mean, std and count of values using Welford's algorithm.
    """
    
    def __init__(self, epsilon: float = 1e-4, shape: Tuple[int, ...] = ()):
        self.mean = np.zeros(shape, dtype=np.float64)
        self.var = np.ones(shape, dtype=np.float64)
        self.count = epsilon
    
    def update(self, x: np.ndarray):
        """Update running statistics with new data."""
        batch_mean = np.mean(x, axis=0)
        batch_var = np.var(x, axis=0)
        batch_count = x.shape[0] if x.ndim > 0 else 1
        self.update_from_moments(batch_mean, batch_var, batch_count)
    
    def update_from_moments(self, batch_mean: np.ndarray, batch_var: np.ndarray, 
                           batch_count: int):
        """Update from batch moments."""
        delta = batch_mean - self.mean
        tot_count = self.count + batch_count
        
        new_mean = self.mean + delta * batch_count / tot_count
        m_a = self.var * self.count
        m_b = batch_var * batch_count
        M2 = m_a + m_b + np.square(delta) * self.count * batch_count / tot_count
        new_var = M2 / tot_count
        
        self.mean = new_mean
        self.var = new_var
        self.count = tot_count


def make_wrapped_env(env: BaseMultiAgentEnv, wrapper_configs: List[Dict[str, Any]]) -> BaseMultiAgentEnv:
    """
    Create a wrapped environment with multiple wrappers.
    
    Args:
        env: Base environment to wrap
        wrapper_configs: List of wrapper configurations
        
    Returns:
        Wrapped environment
    """
    wrapped_env = env
    
    for wrapper_config in wrapper_configs:
        wrapper_type = wrapper_config.pop('type')
        
        if wrapper_type == 'time_limit':
            wrapped_env = TimeLimitWrapper(wrapped_env, **wrapper_config)
        elif wrapper_type == 'failure_injection':
            wrapped_env = FailureInjectionWrapper(wrapped_env, **wrapper_config)
        elif wrapper_type == 'reward_clip':
            wrapped_env = RewardClipWrapper(wrapped_env, **wrapper_config)
        elif wrapper_type == 'obs_normalization':
            wrapped_env = ObsNormalizationWrapper(wrapped_env, **wrapper_config)
        elif wrapper_type == 'mask_invalid_actions':
            wrapped_env = MaskInvalidActionsWrapper(wrapped_env, **wrapper_config)
        else:
            logger.warning(f"Unknown wrapper type: {wrapper_type}")
    
    return wrapped_env