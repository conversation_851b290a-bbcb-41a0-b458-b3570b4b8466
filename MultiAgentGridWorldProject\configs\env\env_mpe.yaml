# Multi-Particle Environment Configuration
env_name: "MPE"
scenario: "simple_spread"

# Agent configuration
num_agents: 4  # Match GridWorld for consistency
agent_radius: 0.04
agent_mass: 1.0
max_speed: 0.1

# Environment physics
world_size: 2.0
landmark_size: 0.08
num_landmarks: 4
landmark_movable: false

# Rewards and penalties
shared_reward: true
collision_penalty: -0.8
distance_reward_scale: 1.0
occupancy_reward: 0.5  # Reward for covering landmarks

# Episode configuration
episode_limit: 200
max_cycles: 200
continuous_actions: false

# Observation configuration
observation_type: "partial"
observe_landmarks: true
observe_other_agents: true
communication_range: 0.5

# PettingZoo settings
use_pettingzoo: true
render_mode: "rgb_array"

# Environment seed
seed: 0