defaults:
  - env: env_gridworld
  - agent: agent_hrl_icp
  - training: training_defaults
  - _self_

# Experiment identification
experiment_name: "gridworld_hrl"
experiment_group: "baseline"
tags: ["hierarchical", "gridworld", "single_agent"]

# Environment settings
multi_agent: false
num_agents: 1
seed: 42

# Results and logging
results_dir: "results"
log_level: "INFO"

# Hardware configuration
device: "auto"  # auto, cpu, cuda
mixed_precision: false
num_workers: 4

# Reproducibility
deterministic: false  # Set true for exact reproducibility (slower)

# Hydra configuration
hydra:
  run:
    dir: ./outputs/${experiment_name}/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: false  # Keep working directory
  sweep:
    dir: ./multirun/${experiment_name}
    subdir: ${hydra:job.num}