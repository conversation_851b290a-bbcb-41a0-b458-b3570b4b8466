"""
Option Selector Model

Computes logits over options for hierarchical policies.
Architecture: MLP(obs) → logits (num_options)
Supports Gumbel-softmax and categorical sampling.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, Any
import numpy as np
import logging

logger = logging.getLogger(__name__)


class OptionSelector(nn.Module):
    """
    Neural network for option selection in hierarchical RL.
    
    Computes logits over discrete options given observations,
    with support for Gumbel-softmax for differentiable sampling.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.obs_dim = config.get("obs_dim", 128)
        self.num_options = config.get("num_options", 6)
        self.hidden_dim = config.get("hidden_dim", 256)
        self.num_layers = config.get("num_layers", 3)
        self.activation = config.get("activation", "relu")
        
        # Gumbel-softmax parameters
        self.use_gumbel_softmax = config.get("use_gumbel_softmax", True)
        self.gumbel_temperature = config.get("gumbel_temperature", 1.0)
        self.temperature_decay = config.get("temperature_decay", 0.999)
        self.min_temperature = config.get("min_temperature", 0.1)
        
        # Regularization
        self.dropout = config.get("dropout", 0.1)
        self.use_layer_norm = config.get("use_layer_norm", True)
        self.use_residual = config.get("use_residual", False)
        
        # Build network
        self._build_network()
        
        # Initialize weights
        self._initialize_weights()
        
        logger.info(f"OptionSelector initialized: {self.obs_dim}→{self.num_options}, "
                   f"{self.num_layers} layers, Gumbel: {self.use_gumbel_softmax}")
    
    def _build_network(self):
        """Build the option selection network."""
        
        # Activation function
        if self.activation == "relu":
            activation_fn = nn.ReLU
        elif self.activation == "tanh":
            activation_fn = nn.Tanh
        elif self.activation == "gelu":
            activation_fn = nn.GELU
        elif self.activation == "swish":
            activation_fn = nn.SiLU
        else:
            activation_fn = nn.ReLU
        
        # Build layers
        layers = []
        input_dim = self.obs_dim
        
        for i in range(self.num_layers):
            # Linear layer
            layers.append(nn.Linear(input_dim, self.hidden_dim))
            
            # Activation
            layers.append(activation_fn())
            
            # Layer normalization
            if self.use_layer_norm:
                layers.append(nn.LayerNorm(self.hidden_dim))
            
            # Dropout
            if self.dropout > 0:
                layers.append(nn.Dropout(self.dropout))
            
            input_dim = self.hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Output head
        self.option_head = nn.Linear(self.hidden_dim, self.num_options)
        
        # Residual connection (if input/output dims match)
        if self.use_residual and self.obs_dim == self.num_options:
            self.residual_proj = nn.Identity()
        elif self.use_residual:
            self.residual_proj = nn.Linear(self.obs_dim, self.num_options)
        else:
            self.residual_proj = None
    
    def _initialize_weights(self):
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                if self.config.get("use_orthogonal_init", True):
                    nn.init.orthogonal_(module.weight)
                else:
                    nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0.0)
    
    def forward(self, observations: torch.Tensor,
                deterministic: bool = False,
                temperature: Optional[float] = None) -> Dict[str, torch.Tensor]:
        """Forward pass for option selection.
        
        Computes option logits from observations and samples discrete options
        using either categorical sampling or Gumbel-softmax for differentiability.
        
        Args:
            observations (torch.Tensor): Batch of observations with shape 
                ``[batch_size, obs_dim]``.
            deterministic (bool, optional): If ``True``, uses deterministic 
                selection (argmax). If ``False``, uses stochastic sampling.
                Default: ``False``.
            temperature (float, optional): Temperature parameter for Gumbel-softmax.
                If ``None``, uses the current temperature from the model.
                Default: ``None``.
                
        Returns:
            Dict[str, torch.Tensor]: Dictionary containing:
                - **option_logits** (torch.Tensor): Raw option logits with shape 
                  ``[batch_size, num_options]``.
                - **option_probs** (torch.Tensor): Softmax probabilities with shape 
                  ``[batch_size, num_options]``.
                - **option_samples** (torch.Tensor): Sampled options (soft if Gumbel-softmax, 
                  hard otherwise) with shape ``[batch_size, num_options]``.
                - **selected_options** (torch.Tensor): Hard option indices with shape 
                  ``[batch_size]``.
                - **option_log_probs** (torch.Tensor): Log probabilities of selected 
                  options with shape ``[batch_size]``.
                  
        Note:
            When using Gumbel-softmax, ``option_samples`` contains soft samples for
            gradient flow, while ``selected_options`` contains the corresponding
            hard discrete choices.
        """
        batch_size = observations.shape[0]
        
        # Extract features
        features = self.feature_extractor(observations)
        
        # Compute option logits
        option_logits = self.option_head(features)
        
        # Add residual connection
        if self.residual_proj is not None:
            residual = self.residual_proj(observations)
            option_logits = option_logits + residual
        
        # Compute probabilities
        option_probs = F.softmax(option_logits, dim=-1)
        
        # Sample options
        if deterministic:
            # Deterministic selection (argmax)
            selected_options = torch.argmax(option_logits, dim=-1)
            option_samples = F.one_hot(selected_options, self.num_options).float()
            option_log_probs = F.log_softmax(option_logits, dim=-1).gather(1, selected_options.unsqueeze(1)).squeeze(1)
        else:
            if self.use_gumbel_softmax:
                # Gumbel-softmax sampling
                temp = temperature if temperature is not None else self.gumbel_temperature
                option_samples = F.gumbel_softmax(option_logits, tau=temp, hard=False)
                
                # Also get hard samples for discrete actions
                hard_samples = F.gumbel_softmax(option_logits, tau=temp, hard=True)
                selected_options = torch.argmax(hard_samples, dim=-1)
                option_log_probs = F.log_softmax(option_logits, dim=-1).gather(1, selected_options.unsqueeze(1)).squeeze(1)
            else:
                # Categorical sampling
                dist = torch.distributions.Categorical(logits=option_logits)
                selected_options = dist.sample()
                option_samples = F.one_hot(selected_options, self.num_options).float()
                option_log_probs = dist.log_prob(selected_options)
        
        return {
            "option_logits": option_logits,
            "option_probs": option_probs,
            "option_samples": option_samples,
            "selected_options": selected_options,
            "option_log_probs": option_log_probs
        }
    
    def update_temperature(self):
        """Update Gumbel-softmax temperature with decay."""
        if self.use_gumbel_softmax:
            self.gumbel_temperature = max(
                self.min_temperature,
                self.gumbel_temperature * self.temperature_decay
            )
    
    def get_option_distribution(self, observations: torch.Tensor) -> torch.distributions.Categorical:
        """Get categorical distribution over options."""
        with torch.no_grad():
            features = self.feature_extractor(observations)
            option_logits = self.option_head(features)
            
            if self.residual_proj is not None:
                residual = self.residual_proj(observations)
                option_logits = option_logits + residual
            
            return torch.distributions.Categorical(logits=option_logits)
    
    def compute_entropy(self, observations: torch.Tensor) -> torch.Tensor:
        """Compute entropy of option distribution."""
        dist = self.get_option_distribution(observations)
        return dist.entropy()
    
    def compute_kl_divergence(self, observations: torch.Tensor,
                            target_logits: torch.Tensor) -> torch.Tensor:
        """Compute KL divergence between current and target distributions."""
        features = self.feature_extractor(observations)
        current_logits = self.option_head(features)
        
        if self.residual_proj is not None:
            residual = self.residual_proj(observations)
            current_logits = current_logits + residual
        
        current_probs = F.softmax(current_logits, dim=-1)
        target_probs = F.softmax(target_logits, dim=-1)
        
        return F.kl_div(
            F.log_softmax(current_logits, dim=-1),
            target_probs,
            reduction='batchmean'
        )
    
    def get_metrics(self) -> Dict[str, float]:
        """Get model metrics for monitoring."""
        metrics = {
            "gumbel_temperature": self.gumbel_temperature,
            "num_parameters": sum(p.numel() for p in self.parameters()),
            "num_trainable_parameters": sum(p.numel() for p in self.parameters() if p.requires_grad)
        }
        
        # Add gradient norms
        total_norm = 0.0
        for p in self.parameters():
            if p.grad is not None:
                total_norm += p.grad.data.norm(2).item() ** 2
        metrics["gradient_norm"] = total_norm ** 0.5
        
        return metrics


class MultiAgentOptionSelector(nn.Module):
    """
    Multi-agent version of option selector with shared or separate networks.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.num_agents = config.get("num_agents", 4)
        self.shared_selector = config.get("shared_selector", True)
        
        if self.shared_selector:
            # Single shared selector
            self.selector = OptionSelector(config)
        else:
            # Separate selector per agent
            self.selectors = nn.ModuleDict({
                f"agent_{i}": OptionSelector(config)
                for i in range(self.num_agents)
            })
        
        logger.info(f"MultiAgentOptionSelector initialized: {self.num_agents} agents, "
                   f"shared={'Yes' if self.shared_selector else 'No'}")
    
    def forward(self, observations: Dict[str, torch.Tensor],
                deterministic: bool = False,
                temperature: Optional[float] = None) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Forward pass for multi-agent option selection.
        
        Args:
            observations: Dict mapping agent_id to observations
            deterministic: Whether to use deterministic selection
            temperature: Override temperature for Gumbel-softmax
            
        Returns:
            Dict mapping agent_id to option selection results
        """
        results = {}
        
        if self.shared_selector:
            # Use shared selector for all agents
            for agent_id, obs in observations.items():
                results[agent_id] = self.selector(obs, deterministic, temperature)
        else:
            # Use separate selector per agent
            for i, (agent_id, obs) in enumerate(observations.items()):
                selector_key = f"agent_{i}"
                if selector_key in self.selectors:
                    results[agent_id] = self.selectors[selector_key](obs, deterministic, temperature)
                else:
                    # Fallback to first selector
                    results[agent_id] = self.selectors["agent_0"](obs, deterministic, temperature)
        
        return results
    
    def update_temperature(self):
        """Update temperature for all selectors."""
        if self.shared_selector:
            self.selector.update_temperature()
        else:
            for selector in self.selectors.values():
                selector.update_temperature()
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get metrics from all selectors."""
        if self.shared_selector:
            return {"shared": self.selector.get_metrics()}
        else:
            return {
                agent_id: selector.get_metrics()
                for agent_id, selector in self.selectors.items()
            }