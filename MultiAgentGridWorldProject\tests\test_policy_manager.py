"""
Policy Manager Tests

Advanced tests for policy management, coordination, and research-grade features
including KL regularization, curriculum learning, and interpretability metrics.
"""

import pytest
import torch
import numpy as np
from typing import Dict, Any
from collections import defaultdict
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.policies import (
    OptionPolicy,
    WorkerPolicy,
    TerminationFunction,
    HierarchicalPolicy,
    MultiAgentHierarchicalPolicy
)


class PolicyManager:
    """Advanced policy management with research-grade features."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.policies = {}
        self.metrics_history = []
        
        # KL regularization
        self.kl_weight = config.get("kl_sparsity_weight", 0.01)
        
        # Curriculum parameters
        self.curriculum_enabled = config.get("curriculum_enabled", False)
        self.curriculum_stage = 0
        
    def create_policy(self, policy_type: str, agent_id: str = None):
        """Create and register a policy."""
        if policy_type == "hierarchical":
            policy = HierarchicalPolicy(self.config)
        elif policy_type == "multi_agent":
            num_agents = self.config.get("num_agents", 3)
            policy = MultiAgentHierarchicalPolicy(self.config, num_agents)
        else:
            raise ValueError(f"Unknown policy type: {policy_type}")
        
        key = agent_id if agent_id else policy_type
        self.policies[key] = policy
        return policy
    
    def compute_kl_regularization(self, option_probs: torch.Tensor) -> torch.Tensor:
        """Compute KL regularization between option distributions."""
        # Encourage diversity between options
        uniform_dist = torch.ones_like(option_probs) / option_probs.shape[-1]
        kl_div = torch.nn.functional.kl_div(
            torch.log(option_probs + 1e-8),
            uniform_dist,
            reduction='batchmean'
        )
        return self.kl_weight * kl_div
    
    def compute_option_usage_entropy(self, option_counts: Dict[int, int]) -> float:
        """Compute entropy of option usage distribution."""
        total_count = sum(option_counts.values())
        if total_count == 0:
            return 0.0
        
        probs = np.array([option_counts.get(i, 0) for i in range(self.config["num_options"])])
        probs = probs / total_count
        
        # Compute entropy
        entropy = -np.sum(probs * np.log(probs + 1e-8))
        max_entropy = np.log(self.config["num_options"])
        
        return entropy / max_entropy  # Normalized entropy
    
    def update_curriculum(self, performance_metrics: Dict[str, float]):
        """Update curriculum based on performance."""
        if not self.curriculum_enabled:
            return
        
        success_rate = performance_metrics.get("success_rate", 0.0)
        
        # Simple curriculum progression
        if success_rate > 0.8 and self.curriculum_stage < 3:
            self.curriculum_stage += 1
            print(f"Curriculum advanced to stage {self.curriculum_stage}")
    
    def get_advanced_metrics(self, policy) -> Dict[str, float]:
        """Get advanced interpretability metrics."""
        metrics = {}
        
        if hasattr(policy, 'get_metrics'):
            base_metrics = policy.get_metrics()
            metrics.update(base_metrics)
        
        # Add advanced metrics
        if hasattr(policy, 'option_usage'):
            option_entropy = self.compute_option_usage_entropy(policy.option_usage)
            metrics["option_usage_entropy"] = option_entropy
        
        metrics["curriculum_stage"] = self.curriculum_stage
        
        return metrics


class TestPolicyManager:
    """Test advanced policy management features."""
    
    @pytest.fixture
    def config(self):
        """Configuration for policy manager tests."""
        return {
            "obs_dim": 64,
            "action_dim": 5,
            "num_options": 6,
            "option_freq": 8,
            "hidden_dim": 128,
            "kl_sparsity_weight": 0.01,
            "curriculum_enabled": True,
            "num_agents": 3
        }
    
    @pytest.fixture
    def manager(self, config):
        """Create policy manager."""
        return PolicyManager(config)
    
    def test_policy_manager_creation(self, manager, config):
        """Test policy manager instantiation."""
        assert manager is not None
        assert manager.config == config
        assert manager.kl_weight == config["kl_sparsity_weight"]
        print("✅ PolicyManager creation test passed")
    
    def test_policy_creation_and_registration(self, manager):
        """Test policy creation and registration."""
        # Create hierarchical policy
        policy = manager.create_policy("hierarchical", "test_agent")
        
        assert "test_agent" in manager.policies
        assert isinstance(manager.policies["test_agent"], HierarchicalPolicy)
        
        # Create multi-agent policy
        multi_policy = manager.create_policy("multi_agent")
        
        assert "multi_agent" in manager.policies
        assert isinstance(manager.policies["multi_agent"], MultiAgentHierarchicalPolicy)
        
        print("✅ Policy creation and registration test passed")
    
    def test_kl_regularization(self, manager):
        """Test KL regularization computation."""
        batch_size = 4
        num_options = 6
        
        # Test with uniform distribution (should have low KL)
        uniform_probs = torch.ones(batch_size, num_options) / num_options
        kl_uniform = manager.compute_kl_regularization(uniform_probs)
        
        # Test with peaked distribution (should have high KL)
        peaked_probs = torch.zeros(batch_size, num_options)
        peaked_probs[:, 0] = 1.0  # All probability on first option
        kl_peaked = manager.compute_kl_regularization(peaked_probs)
        
        assert kl_uniform < kl_peaked
        assert kl_uniform.item() >= -1e-6  # Allow for small numerical errors
        assert kl_peaked.item() >= 0
        
        print("✅ KL regularization test passed")
    
    def test_option_usage_entropy(self, manager):
        """Test option usage entropy computation."""
        # Test uniform usage (high entropy)
        uniform_counts = {i: 10 for i in range(6)}
        uniform_entropy = manager.compute_option_usage_entropy(uniform_counts)
        
        # Test peaked usage (low entropy)
        peaked_counts = {0: 50, 1: 1, 2: 1, 3: 1, 4: 1, 5: 1}
        peaked_entropy = manager.compute_option_usage_entropy(peaked_counts)
        
        assert uniform_entropy > peaked_entropy
        assert 0 <= uniform_entropy <= 1
        assert 0 <= peaked_entropy <= 1
        
        print("✅ Option usage entropy test passed")
    
    def test_curriculum_learning(self, manager):
        """Test curriculum learning functionality."""
        initial_stage = manager.curriculum_stage
        
        # Low performance should not advance curriculum
        low_performance = {"success_rate": 0.3}
        manager.update_curriculum(low_performance)
        assert manager.curriculum_stage == initial_stage
        
        # High performance should advance curriculum
        high_performance = {"success_rate": 0.9}
        manager.update_curriculum(high_performance)
        assert manager.curriculum_stage > initial_stage
        
        print("✅ Curriculum learning test passed")
    
    def test_advanced_metrics(self, manager):
        """Test advanced metrics computation."""
        # Create a policy
        policy = manager.create_policy("hierarchical", "test_agent")
        
        # Run some steps to generate metrics
        observations = torch.randn(1, 64)
        for _ in range(10):
            policy(observations)
        
        # Get advanced metrics
        metrics = manager.get_advanced_metrics(policy)
        
        assert isinstance(metrics, dict)
        assert "curriculum_stage" in metrics
        
        print("✅ Advanced metrics test passed")


class TestResearchFeatures:
    """Test research-grade features and enhancements."""
    
    @pytest.fixture
    def config(self):
        """Configuration for research features."""
        return {
            "obs_dim": 64,
            "action_dim": 5,
            "num_options": 6,
            "option_freq": 8,
            "hidden_dim": 128,
            "use_option_embeddings": True,
            "option_embed_dim": 32
        }
    
    def test_option_specialization(self, config):
        """Test that options develop distinct specializations."""
        policy = HierarchicalPolicy(config)
        
        # Run multiple episodes to develop specialization
        observations = torch.randn(1, 64)
        option_action_pairs = defaultdict(list)
        
        for episode in range(5):
            policy.reset()
            for step in range(20):
                result = policy(observations)
                option = result["current_options"].item()
                action = result["actions"].item()
                option_action_pairs[option].append(action)
        
        # Check if options show preference for certain actions
        option_preferences = {}
        for option, actions in option_action_pairs.items():
            if actions:
                action_counts = np.bincount(actions, minlength=config["action_dim"])
                most_common_action = np.argmax(action_counts)
                preference_strength = action_counts[most_common_action] / len(actions)
                option_preferences[option] = (most_common_action, preference_strength)
        
        # At least some options should show clear preferences
        strong_preferences = sum(1 for _, strength in option_preferences.values() if strength > 0.4)
        assert strong_preferences > 0, "Options should develop action preferences"
        
        print("✅ Option specialization test passed")
    
    def test_termination_curriculum(self, config):
        """Test curriculum-driven termination behavior."""
        # Early training: more frequent termination
        early_config = config.copy()
        early_config["termination_strategy"] = "entropy"
        early_config["entropy_threshold"] = 0.7  # High threshold = more termination
        
        early_termination = TerminationFunction(early_config)
        
        # Late training: less frequent termination
        late_config = config.copy()
        late_config["termination_strategy"] = "entropy"
        late_config["entropy_threshold"] = 0.3  # Low threshold = less termination
        
        late_termination = TerminationFunction(late_config)
        
        # Test with medium entropy
        observations = torch.randn(4, 64)
        options = torch.randint(0, 6, (4,))
        medium_entropy = torch.tensor([0.5, 0.5, 0.5, 0.5])
        
        early_result = early_termination(observations, options, worker_entropy=medium_entropy)
        late_result = late_termination(observations, options, worker_entropy=medium_entropy)
        
        # Early training should terminate more often
        early_terminations = early_result["should_terminate"].sum().item()
        late_terminations = late_result["should_terminate"].sum().item()
        
        assert early_terminations >= late_terminations, "Early training should terminate more frequently"
        
        print("✅ Termination curriculum test passed")
    
    def test_option_embedding_diversity(self, config):
        """Test that option embeddings maintain diversity."""
        policy = OptionPolicy(config)
        
        # Get all option embeddings
        option_indices = torch.arange(config["num_options"])
        embeddings = policy.get_option_embedding(option_indices)
        
        # Compute pairwise similarities
        normalized_embeddings = embeddings / embeddings.norm(dim=1, keepdim=True)
        similarities = torch.mm(normalized_embeddings, normalized_embeddings.t())
        
        # Check off-diagonal similarities (should be low for diversity)
        off_diagonal_mask = ~torch.eye(similarities.shape[0], dtype=torch.bool)
        off_diagonal_similarities = similarities[off_diagonal_mask]
        
        avg_similarity = off_diagonal_similarities.mean().item()
        
        # Embeddings should be reasonably diverse
        assert avg_similarity < 0.8, f"Option embeddings too similar (avg: {avg_similarity:.3f})"
        
        print("✅ Option embedding diversity test passed")


def test_integration_with_advanced_features():
    """Integration test with all advanced features."""
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 4,
        "option_freq": 5,
        "hidden_dim": 64,
        "kl_sparsity_weight": 0.01,
        "curriculum_enabled": True,
        "num_agents": 2
    }
    
    # Create policy manager
    manager = PolicyManager(config)
    
    # Create policies
    single_policy = manager.create_policy("hierarchical", "agent_0")
    multi_policy = manager.create_policy("multi_agent")
    
    # Simulate training with advanced features
    observations = torch.randn(1, 64)
    multi_observations = {
        "agent_0": torch.randn(1, 64),
        "agent_1": torch.randn(1, 64)
    }
    
    # Run episodes
    for episode in range(3):
        # Single agent episode
        single_policy.reset()
        for step in range(10):
            result = single_policy(observations)
            
            # Compute KL regularization if needed
            if "option_probs" in result:
                kl_loss = manager.compute_kl_regularization(result["option_probs"])
        
        # Multi-agent episode
        multi_policy.reset()
        for step in range(10):
            results = multi_policy(multi_observations)
        
        # Update curriculum
        performance = {"success_rate": 0.6 + episode * 0.2}
        manager.update_curriculum(performance)
    
    # Get final metrics
    single_metrics = manager.get_advanced_metrics(single_policy)
    multi_metrics = manager.get_advanced_metrics(multi_policy)
    
    assert isinstance(single_metrics, dict)
    assert isinstance(multi_metrics, dict)
    assert manager.curriculum_stage > 0
    
    print("✅ Integration with advanced features test passed")


if __name__ == "__main__":
    # Run tests manually if not using pytest
    print("Running Policy Manager Tests...")
    
    from collections import defaultdict
    
    # Basic manager tests
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 6,
        "option_freq": 8,
        "hidden_dim": 128,
        "kl_sparsity_weight": 0.01,
        "curriculum_enabled": True,
        "num_agents": 3
    }
    
    manager_test = TestPolicyManager()
    manager = PolicyManager(config)
    
    manager_test.test_policy_manager_creation(manager, config)
    manager_test.test_policy_creation_and_registration(manager)
    manager_test.test_kl_regularization(manager)
    manager_test.test_option_usage_entropy(manager)
    manager_test.test_curriculum_learning(manager)
    manager_test.test_advanced_metrics(manager)
    
    # Research features tests
    research_test = TestResearchFeatures()
    research_test.test_option_specialization(config)
    research_test.test_termination_curriculum(config)
    research_test.test_option_embedding_diversity(config)
    
    # Integration test
    test_integration_with_advanced_features()
    
    print("\n🎉 All Policy Manager Tests Passed!")
    print("✅ Advanced features and research enhancements validated")