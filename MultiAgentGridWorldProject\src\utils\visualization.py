"""
Visualization Tools for Multi-Agent Environments

This module provides comprehensive visualization tools for multi-agent
reinforcement learning environments, including real-time rendering,
training curve plotting, and trajectory analysis.
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import matplotlib.animation as animation
from matplotlib.colors import ListedColormap
import seaborn as sns
import cv2
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
from pathlib import Path
import pandas as pd
from PIL import Image, ImageDraw, ImageFont
import io
import base64

logger = logging.getLogger(__name__)


class GridWorldVisualizer:
    """
    Comprehensive visualization tool for GridWorld environments.
    
    Provides methods for rendering environments, creating animations,
    and analyzing agent trajectories.
    """
    
    def __init__(self, grid_size: Tuple[int, int] = (12, 12), cell_size: int = 40):
        self.grid_size = grid_size
        self.cell_size = cell_size
        self.fig_size = (
            grid_size[1] * cell_size / 100,
            grid_size[0] * cell_size / 100
        )
        
        # Color scheme
        self.colors = {
            'empty': '#FFFFFF',
            'wall': '#2C3E50',
            'obstacle': '#7F8C8D',
            'goal': '#F39C12',
            'agent': '#E74C3C',
            'visited': '#ECF0F1',
            'communication': '#3498DB',
            'attention': '#9B59B6'
        }
        
        # Agent colors for multi-agent scenarios
        self.agent_colors = [
            '#E74C3C', '#3498DB', '#2ECC71', '#F39C12',
            '#9B59B6', '#1ABC9C', '#E67E22', '#34495E'
        ]
        
        # Trajectory storage
        self.trajectories = {}
        self.attention_history = []
        self.communication_events = []
        
    def render_frame(
        self,
        grid: np.ndarray,
        agent_positions: Dict[str, Tuple[int, int]],
        goal_positions: List[Tuple[int, int]],
        attention_weights: Optional[np.ndarray] = None,
        timestep: int = 0,
        info: Optional[Dict[str, Any]] = None
    ) -> np.ndarray:
        """
        Render a single frame of the environment.
        
        Args:
            grid: Environment grid (obstacles, walls, etc.)
            agent_positions: Dictionary of agent positions
            goal_positions: List of goal positions
            attention_weights: Optional attention weight matrix
            timestep: Current timestep
            info: Additional information to display
            
        Returns:
            RGB image array
        """
        # Create figure
        fig, ax = plt.subplots(figsize=self.fig_size)
        ax.set_xlim(0, self.grid_size[1])
        ax.set_ylim(0, self.grid_size[0])
        ax.set_aspect('equal')
        
        # Draw grid background
        self._draw_grid_background(ax, grid)
        
        # Draw goals
        for goal_pos in goal_positions:
            self._draw_goal(ax, goal_pos)
        
        # Draw communication links (if attention weights provided)
        if attention_weights is not None:
            self._draw_communication_links(ax, agent_positions, attention_weights)
        
        # Draw agents
        agent_ids = list(agent_positions.keys())
        for i, (agent_id, pos) in enumerate(agent_positions.items()):
            color = self.agent_colors[i % len(self.agent_colors)]
            self._draw_agent(ax, pos, agent_id, color)
        
        # Add title and info
        title = f"GridWorld - Step {timestep}"
        if info:
            if 'episode_reward' in info:
                title += f" | Reward: {info['episode_reward']:.2f}"
            if 'success' in info:
                title += f" | Success: {info['success']}"
        ax.set_title(title)
        
        # Remove axes
        ax.set_xticks([])
        ax.set_yticks([])
        
        # Convert to image array
        fig.canvas.draw()
        buf = np.frombuffer(fig.canvas.tostring_rgb(), dtype=np.uint8)
        buf = buf.reshape(fig.canvas.get_width_height()[::-1] + (3,))
        
        plt.close(fig)
        return buf
    
    def _draw_grid_background(self, ax: plt.Axes, grid: np.ndarray):
        """Draw the grid background with obstacles and walls."""
        for i in range(self.grid_size[0]):
            for j in range(self.grid_size[1]):
                cell_value = grid[i, j]
                
                if cell_value == 1:  # Wall/Obstacle
                    color = self.colors['wall']
                elif cell_value == 2:  # Goal (will be overdrawn)
                    color = self.colors['goal']
                else:  # Empty
                    color = self.colors['empty']
                
                rect = patches.Rectangle(
                    (j, self.grid_size[0] - i - 1), 1, 1,
                    linewidth=0.5, edgecolor='gray', facecolor=color
                )
                ax.add_patch(rect)
    
    def _draw_goal(self, ax: plt.Axes, goal_pos: Tuple[int, int]):
        """Draw a goal position."""
        x, y = goal_pos
        y = self.grid_size[0] - y - 1  # Flip y-coordinate
        
        # Draw goal as a star
        star = patches.RegularPolygon(
            (x + 0.5, y + 0.5), 5, radius=0.3,
            facecolor=self.colors['goal'], edgecolor='black', linewidth=2
        )
        ax.add_patch(star)
    
    def _draw_agent(self, ax: plt.Axes, pos: Tuple[int, int], agent_id: str, color: str):
        """Draw an agent at the specified position."""
        x, y = pos
        y = self.grid_size[0] - y - 1  # Flip y-coordinate
        
        # Draw agent as a circle
        circle = patches.Circle(
            (x + 0.5, y + 0.5), 0.25,
            facecolor=color, edgecolor='black', linewidth=2
        )
        ax.add_patch(circle)
        
        # Add agent ID label
        ax.text(x + 0.5, y + 0.5, agent_id.split('_')[-1],
               ha='center', va='center', fontsize=8, fontweight='bold', color='white')
    
    def _draw_communication_links(
        self,
        ax: plt.Axes,
        agent_positions: Dict[str, Tuple[int, int]],
        attention_weights: np.ndarray
    ):
        """Draw communication links between agents based on attention weights."""
        agent_ids = list(agent_positions.keys())
        
        for i, agent_i in enumerate(agent_ids):
            for j, agent_j in enumerate(agent_ids):
                if i != j and attention_weights[i, j] > 0.1:  # Threshold for visualization
                    pos_i = agent_positions[agent_i]
                    pos_j = agent_positions[agent_j]
                    
                    # Flip y-coordinates
                    y_i = self.grid_size[0] - pos_i[1] - 1
                    y_j = self.grid_size[0] - pos_j[1] - 1
                    
                    # Draw line with thickness proportional to attention weight
                    line_width = attention_weights[i, j] * 3
                    alpha = min(1.0, attention_weights[i, j] * 2)
                    
                    ax.plot([pos_i[0] + 0.5, pos_j[0] + 0.5],
                           [y_i + 0.5, y_j + 0.5],
                           color=self.colors['communication'],
                           linewidth=line_width, alpha=alpha)
    
    def create_training_gif(
        self,
        episode_data: List[Dict[str, Any]],
        save_path: str,
        fps: int = 5,
        show_attention: bool = True
    ) -> str:
        """
        Create an animated GIF from episode data.
        
        Args:
            episode_data: List of episode step data
            save_path: Path to save the GIF
            fps: Frames per second
            show_attention: Whether to show attention weights
            
        Returns:
            Path to saved GIF
        """
        if not episode_data:
            raise ValueError("No episode data provided")
        
        frames = []
        
        for step_data in episode_data:
            # Extract data
            grid = step_data.get('grid', np.zeros(self.grid_size))
            agent_positions = step_data.get('agent_positions', {})
            goal_positions = step_data.get('goal_positions', [])
            attention_weights = step_data.get('attention_weights') if show_attention else None
            timestep = step_data.get('timestep', 0)
            info = step_data.get('info', {})
            
            # Render frame
            frame = self.render_frame(
                grid, agent_positions, goal_positions,
                attention_weights, timestep, info
            )
            frames.append(frame)
        
        # Save as GIF
        if frames:
            # Convert to PIL Images
            pil_frames = [Image.fromarray(frame) for frame in frames]
            
            # Save GIF
            pil_frames[0].save(
                save_path,
                save_all=True,
                append_images=pil_frames[1:],
                duration=int(1000 / fps),
                loop=0
            )
            
            logger.info(f"Training GIF saved to {save_path}")
        
        return save_path
    
    def plot_trajectory_analysis(
        self,
        trajectories: Dict[str, List[Tuple[int, int]]],
        grid: np.ndarray,
        save_path: Optional[str] = None
    ) -> plt.Figure:
        """
        Plot trajectory analysis for multiple agents.
        
        Args:
            trajectories: Dictionary of agent trajectories
            grid: Environment grid
            save_path: Optional path to save the figure
            
        Returns:
            matplotlib Figure object
        """
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # Plot 1: All trajectories on grid
        ax1 = axes[0]
        ax1.imshow(grid, cmap='gray', alpha=0.3)
        
        for i, (agent_id, trajectory) in enumerate(trajectories.items()):
            if trajectory:
                x_coords = [pos[0] for pos in trajectory]
                y_coords = [pos[1] for pos in trajectory]
                
                color = self.agent_colors[i % len(self.agent_colors)]
                ax1.plot(x_coords, y_coords, color=color, linewidth=2, 
                        alpha=0.7, label=agent_id)
                
                # Mark start and end
                ax1.scatter(x_coords[0], y_coords[0], color=color, 
                           s=100, marker='o', edgecolor='black', linewidth=2)
                ax1.scatter(x_coords[-1], y_coords[-1], color=color, 
                           s=100, marker='s', edgecolor='black', linewidth=2)
        
        ax1.set_title('Agent Trajectories')
        ax1.legend()
        ax1.set_aspect('equal')
        
        # Plot 2: Exploration heatmap
        ax2 = axes[1]
        exploration_map = np.zeros(self.grid_size)
        
        for trajectory in trajectories.values():
            for pos in trajectory:
                if 0 <= pos[0] < self.grid_size[1] and 0 <= pos[1] < self.grid_size[0]:
                    exploration_map[pos[1], pos[0]] += 1
        
        im = ax2.imshow(exploration_map, cmap='hot', interpolation='nearest')
        ax2.set_title('Exploration Heatmap')
        plt.colorbar(im, ax=ax2, label='Visit Count')
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Trajectory analysis saved to {save_path}")
        
        return fig


def create_training_gif(
    episode_data: List[Dict[str, Any]],
    save_path: str,
    grid_size: Tuple[int, int] = (12, 12),
    **kwargs
) -> str:
    """
    Convenience function to create training GIF.
    
    Args:
        episode_data: List of episode step data
        save_path: Path to save the GIF
        grid_size: Size of the grid
        **kwargs: Additional arguments for GridWorldVisualizer
        
    Returns:
        Path to saved GIF
    """
    visualizer = GridWorldVisualizer(grid_size)
    return visualizer.create_training_gif(episode_data, save_path, **kwargs)


def plot_training_curves(
    metrics_data: Dict[str, List[float]],
    save_path: Optional[str] = None,
    title: str = "Training Curves"
) -> plt.Figure:
    """
    Plot comprehensive training curves.
    
    Args:
        metrics_data: Dictionary of metric names to value lists
        save_path: Optional path to save the figure
        title: Plot title
        
    Returns:
        matplotlib Figure object
    """
    # Determine subplot layout
    num_metrics = len(metrics_data)
    if num_metrics <= 2:
        rows, cols = 1, num_metrics
    elif num_metrics <= 4:
        rows, cols = 2, 2
    elif num_metrics <= 6:
        rows, cols = 2, 3
    else:
        rows, cols = 3, 3
    
    fig, axes = plt.subplots(rows, cols, figsize=(5*cols, 4*rows))
    if num_metrics == 1:
        axes = [axes]
    elif rows == 1 or cols == 1:
        axes = axes.flatten()
    else:
        axes = axes.flatten()
    
    # Plot each metric
    for i, (metric_name, values) in enumerate(metrics_data.items()):
        if i >= len(axes):
            break
            
        ax = axes[i]
        
        # Plot raw values
        ax.plot(values, alpha=0.7, label='Raw')
        
        # Add moving average if enough data
        if len(values) > 10:
            window = min(50, len(values) // 10)
            moving_avg = pd.Series(values).rolling(window).mean()
            ax.plot(moving_avg, color='red', linewidth=2, label=f'MA({window})')
            ax.legend()
        
        ax.set_title(metric_name.replace('_', ' ').title())
        ax.set_xlabel('Episode')
        ax.set_ylabel('Value')
        ax.grid(True, alpha=0.3)
    
    # Hide unused subplots
    for i in range(num_metrics, len(axes)):
        axes[i].set_visible(False)
    
    plt.suptitle(title, fontsize=16)
    plt.tight_layout()
    
    if save_path:
        fig.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Training curves saved to {save_path}")
    
    return fig


def create_comparison_plot(
    data_dict: Dict[str, Dict[str, List[float]]],
    metric_name: str,
    save_path: Optional[str] = None
) -> plt.Figure:
    """
    Create comparison plot for multiple algorithms/runs.
    
    Args:
        data_dict: Dictionary of algorithm names to metrics data
        metric_name: Name of the metric to plot
        save_path: Optional path to save the figure
        
    Returns:
        matplotlib Figure object
    """
    fig, ax = plt.subplots(figsize=(12, 8))
    
    colors = plt.cm.Set1(np.linspace(0, 1, len(data_dict)))
    
    for i, (algo_name, metrics_data) in enumerate(data_dict.items()):
        if metric_name in metrics_data:
            values = metrics_data[metric_name]
            color = colors[i]
            
            # Plot raw values
            ax.plot(values, alpha=0.3, color=color)
            
            # Plot moving average
            if len(values) > 10:
                window = min(50, len(values) // 10)
                moving_avg = pd.Series(values).rolling(window).mean()
                ax.plot(moving_avg, color=color, linewidth=2, label=algo_name)
    
    ax.set_title(f'{metric_name.replace("_", " ").title()} Comparison')
    ax.set_xlabel('Episode')
    ax.set_ylabel(metric_name.replace('_', ' ').title())
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        fig.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Comparison plot saved to {save_path}")
    
    return fig


def create_performance_heatmap(
    performance_matrix: np.ndarray,
    algorithm_names: List[str],
    metric_names: List[str],
    save_path: Optional[str] = None
) -> plt.Figure:
    """
    Create performance heatmap for algorithm comparison.
    
    Args:
        performance_matrix: Matrix of performance values
        algorithm_names: List of algorithm names
        metric_names: List of metric names
        save_path: Optional path to save the figure
        
    Returns:
        matplotlib Figure object
    """
    fig, ax = plt.subplots(figsize=(10, 8))
    
    # Create heatmap
    im = ax.imshow(performance_matrix, cmap='RdYlGn', aspect='auto')
    
    # Set ticks and labels
    ax.set_xticks(np.arange(len(metric_names)))
    ax.set_yticks(np.arange(len(algorithm_names)))
    ax.set_xticklabels(metric_names)
    ax.set_yticklabels(algorithm_names)
    
    # Rotate the tick labels and set their alignment
    plt.setp(ax.get_xticklabels(), rotation=45, ha="right", rotation_mode="anchor")
    
    # Add colorbar
    cbar = ax.figure.colorbar(im, ax=ax)
    cbar.ax.set_ylabel('Performance Score', rotation=-90, va="bottom")
    
    # Add text annotations
    for i in range(len(algorithm_names)):
        for j in range(len(metric_names)):
            text = ax.text(j, i, f'{performance_matrix[i, j]:.3f}',
                          ha="center", va="center", color="black", fontweight='bold')
    
    ax.set_title("Algorithm Performance Comparison")
    plt.tight_layout()
    
    if save_path:
        fig.savefig(save_path, dpi=300, bbox_inches='tight')
        logger.info(f"Performance heatmap saved to {save_path}")
    
    return fig


if __name__ == "__main__":
    # Example usage and testing
    import argparse
    
    parser = argparse.ArgumentParser(description="Visualization Tools")
    parser.add_argument("--mode", choices=["render", "gif", "curves", "heatmap"],
                       default="render", help="Visualization mode")
    parser.add_argument("--output", type=str, default="visualization.png",
                       help="Output file path")
    
    args = parser.parse_args()
    
    if args.mode == "render":
        # Test single frame rendering
        visualizer = GridWorldVisualizer((8, 8))
        
        grid = np.zeros((8, 8))
        grid[0, :] = 1  # Top wall
        grid[-1, :] = 1  # Bottom wall
        grid[:, 0] = 1  # Left wall
        grid[:, -1] = 1  # Right wall
        grid[3:5, 3:5] = 1  # Obstacle
        
        agent_positions = {'agent_0': (2, 2), 'agent_1': (5, 5)}
        goal_positions = [(1, 1), (6, 6)]
        
        frame = visualizer.render_frame(grid, agent_positions, goal_positions)
        
        # Save frame
        Image.fromarray(frame).save(args.output)
        print(f"Frame saved to {args.output}")
    
    elif args.mode == "curves":
        # Test training curves
        metrics_data = {
            'episode_reward': np.cumsum(np.random.randn(1000) * 0.1) + np.arange(1000) * 0.01,
            'success_rate': np.clip(np.random.randn(1000) * 0.1 + np.arange(1000) * 0.001, 0, 1),
            'episode_length': 100 + np.random.randn(1000) * 10
        }
        
        fig = plot_training_curves(metrics_data, args.output)
        print(f"Training curves saved to {args.output}")
    
    elif args.mode == "heatmap":
        # Test performance heatmap
        algorithms = ['SD-HRL', 'QMIX', 'MADDPG', 'IPPO']
        metrics = ['Reward', 'Success Rate', 'Communication Efficiency']
        
        # Generate sample performance matrix
        performance_matrix = np.random.rand(len(algorithms), len(metrics))
        performance_matrix[0, :] *= 1.2  # Make SD-HRL slightly better
        
        fig = create_performance_heatmap(
            performance_matrix, algorithms, metrics, args.output
        )
        print(f"Performance heatmap saved to {args.output}")
    
    print("Visualization test completed successfully!")