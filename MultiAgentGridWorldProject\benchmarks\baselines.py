#!/usr/bin/env python3
"""
Baseline Algorithm Benchmarks for SD-HRL Comparison

This script runs baseline multi-agent reinforcement learning algorithms
to compare against our SD-HRL (Spatially-Distributed Hierarchical RL) approach.

Supported baselines:
- QMIX: Value-based method with centralized training
- MADDPG: Multi-agent actor-critic with centralized critics  
- IPPO: Independent Proximal Policy Optimization
- MAPPO: Multi-agent PPO with centralized value function
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, List
import numpy as np
import torch
import yaml
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.environment.grid_world import GridWorldEnv
from src.environment.mpe_adapter import MPEAdapter
from src.environment.smac_adapter import SMACAdapter
from src.training.runner import TrainingRunner
from src.utils.logger import setup_logger

logger = logging.getLogger(__name__)


class BaselineAlgorithm:
    """Base class for baseline algorithms."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.results = {}
    
    def train(self, env, num_episodes: int = 1000) -> Dict[str, Any]:
        """Train the baseline algorithm."""
        raise NotImplementedError
    
    def evaluate(self, env, num_episodes: int = 100) -> Dict[str, Any]:
        """Evaluate the trained algorithm."""
        raise NotImplementedError


class QMIXBaseline(BaselineAlgorithm):
    """QMIX baseline implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("QMIX", config)
        self.q_networks = {}
        self.mixer_network = None
        self.target_networks = {}
        
    def train(self, env, num_episodes: int = 1000) -> Dict[str, Any]:
        """Train QMIX algorithm."""
        logger.info(f"Training {self.name} for {num_episodes} episodes")
        
        episode_rewards = []
        episode_lengths = []
        communication_overhead = []
        
        for episode in range(num_episodes):
            obs = env.reset()
            episode_reward = 0
            episode_length = 0
            comm_count = 0
            done = False
            
            while not done:
                # QMIX action selection (simplified)
                actions = {}
                for agent_id in env.agent_ids:
                    # Epsilon-greedy action selection
                    if np.random.random() < self.config.get('epsilon', 0.1):
                        actions[agent_id] = env.action_space.sample()
                    else:
                        # Use Q-network (simplified - would need actual implementation)
                        actions[agent_id] = np.random.randint(env.action_space.n)
                
                # Step environment
                next_obs, rewards, dones, info = env.step(actions)
                
                # Update metrics
                episode_reward += sum(rewards.values())
                episode_length += 1
                comm_count += len(actions)  # Simplified communication count
                
                done = all(dones.values()) or episode_length >= env.episode_limit
                obs = next_obs
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            communication_overhead.append(comm_count)
            
            if episode % 100 == 0:
                avg_reward = np.mean(episode_rewards[-100:])
                logger.info(f"Episode {episode}: Avg Reward = {avg_reward:.2f}")
        
        return {
            'episode_rewards': episode_rewards,
            'episode_lengths': episode_lengths,
            'communication_overhead': communication_overhead,
            'final_avg_reward': np.mean(episode_rewards[-100:]),
            'final_success_rate': np.mean([r > 0 for r in episode_rewards[-100:]])
        }


class MADDPGBaseline(BaselineAlgorithm):
    """MADDPG baseline implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("MADDPG", config)
        self.actors = {}
        self.critics = {}
        
    def train(self, env, num_episodes: int = 1000) -> Dict[str, Any]:
        """Train MADDPG algorithm."""
        logger.info(f"Training {self.name} for {num_episodes} episodes")
        
        episode_rewards = []
        episode_lengths = []
        communication_overhead = []
        
        for episode in range(num_episodes):
            obs = env.reset()
            episode_reward = 0
            episode_length = 0
            comm_count = 0
            done = False
            
            while not done:
                # MADDPG action selection (simplified)
                actions = {}
                for agent_id in env.agent_ids:
                    # Add noise for exploration
                    noise = np.random.normal(0, self.config.get('noise_std', 0.1))
                    actions[agent_id] = np.clip(
                        np.random.random() + noise, 0, env.action_space.n - 1
                    ).astype(int)
                
                # Step environment
                next_obs, rewards, dones, info = env.step(actions)
                
                # Update metrics
                episode_reward += sum(rewards.values())
                episode_length += 1
                comm_count += len(actions) * 2  # Actor + Critic communication
                
                done = all(dones.values()) or episode_length >= env.episode_limit
                obs = next_obs
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            communication_overhead.append(comm_count)
            
            if episode % 100 == 0:
                avg_reward = np.mean(episode_rewards[-100:])
                logger.info(f"Episode {episode}: Avg Reward = {avg_reward:.2f}")
        
        return {
            'episode_rewards': episode_rewards,
            'episode_lengths': episode_lengths,
            'communication_overhead': communication_overhead,
            'final_avg_reward': np.mean(episode_rewards[-100:]),
            'final_success_rate': np.mean([r > 0 for r in episode_rewards[-100:]])
        }


class IPPOBaseline(BaselineAlgorithm):
    """Independent PPO baseline implementation."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("IPPO", config)
        self.policies = {}
        
    def train(self, env, num_episodes: int = 1000) -> Dict[str, Any]:
        """Train IPPO algorithm."""
        logger.info(f"Training {self.name} for {num_episodes} episodes")
        
        episode_rewards = []
        episode_lengths = []
        communication_overhead = []
        
        for episode in range(num_episodes):
            obs = env.reset()
            episode_reward = 0
            episode_length = 0
            comm_count = 0
            done = False
            
            while not done:
                # IPPO action selection (independent)
                actions = {}
                for agent_id in env.agent_ids:
                    # Simple policy (would use actual PPO policy)
                    actions[agent_id] = np.random.randint(env.action_space.n)
                
                # Step environment
                next_obs, rewards, dones, info = env.step(actions)
                
                # Update metrics
                episode_reward += sum(rewards.values())
                episode_length += 1
                comm_count += 0  # No communication in IPPO
                
                done = all(dones.values()) or episode_length >= env.episode_limit
                obs = next_obs
            
            episode_rewards.append(episode_reward)
            episode_lengths.append(episode_length)
            communication_overhead.append(comm_count)
            
            if episode % 100 == 0:
                avg_reward = np.mean(episode_rewards[-100:])
                logger.info(f"Episode {episode}: Avg Reward = {avg_reward:.2f}")
        
        return {
            'episode_rewards': episode_rewards,
            'episode_lengths': episode_lengths,
            'communication_overhead': communication_overhead,
            'final_avg_reward': np.mean(episode_rewards[-100:]),
            'final_success_rate': np.mean([r > 0 for r in episode_rewards[-100:]])
        }


def create_environment(env_name: str, config: Dict[str, Any]):
    """Create environment based on name."""
    if env_name == "gridworld":
        return GridWorldEnv(config)
    elif env_name == "mpe":
        return MPEAdapter(config)
    elif env_name == "smac":
        return SMACAdapter(config)
    else:
        raise ValueError(f"Unknown environment: {env_name}")


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        # Return default config
        return {
            'env': {
                'map_size': [8, 8],
                'num_agents': 4,
                'episode_limit': 200
            },
            'training': {
                'num_episodes': 1000,
                'eval_episodes': 100
            }
        }
    
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def save_results(results: Dict[str, Any], output_dir: str, algorithm: str, env_name: str):
    """Save benchmark results."""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{algorithm}_{env_name}_{timestamp}.yaml"
    filepath = os.path.join(output_dir, filename)
    
    # Convert numpy types to Python native types
    def convert_numpy(obj):
        if isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, dict):
            return {k: convert_numpy(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [convert_numpy(v) for v in obj]
        else:
            return obj
    
    clean_results = convert_numpy(results)
    
    with open(filepath, 'w') as f:
        yaml.dump(clean_results, f, default_flow_style=False)
    
    logger.info(f"Results saved to {filepath}")


def main():
    parser = argparse.ArgumentParser(description="Run baseline algorithm benchmarks")
    parser.add_argument("--algo", required=True, choices=["qmix", "maddpg", "ippo"],
                       help="Baseline algorithm to run")
    parser.add_argument("--env", required=True, choices=["gridworld", "mpe", "smac"],
                       help="Environment to test on")
    parser.add_argument("--config", default=None,
                       help="Configuration file path")
    parser.add_argument("--seed", type=int, default=42,
                       help="Random seed")
    parser.add_argument("--num_runs", type=int, default=1,
                       help="Number of independent runs")
    parser.add_argument("--output_dir", default="results/baselines",
                       help="Output directory for results")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logger(level=logging.INFO)
    
    # Set random seeds
    np.random.seed(args.seed)
    torch.manual_seed(args.seed)
    
    # Load configuration
    if args.config:
        config = load_config(args.config)
    else:
        config = load_config("")  # Use default config
    
    logger.info(f"Running {args.algo.upper()} baseline on {args.env} environment")
    logger.info(f"Configuration: {config}")
    
    # Create algorithm
    if args.algo == "qmix":
        algorithm = QMIXBaseline(config)
    elif args.algo == "maddpg":
        algorithm = MADDPGBaseline(config)
    elif args.algo == "ippo":
        algorithm = IPPOBaseline(config)
    else:
        raise ValueError(f"Unknown algorithm: {args.algo}")
    
    # Run multiple independent runs
    all_results = []
    
    for run in range(args.num_runs):
        logger.info(f"Starting run {run + 1}/{args.num_runs}")
        
        # Create environment
        env = create_environment(args.env, config.get('env', {}))
        
        # Train algorithm
        start_time = time.time()
        results = algorithm.train(env, config.get('training', {}).get('num_episodes', 1000))
        training_time = time.time() - start_time
        
        # Add metadata
        results.update({
            'algorithm': args.algo,
            'environment': args.env,
            'run_id': run,
            'seed': args.seed + run,
            'training_time': training_time,
            'config': config
        })
        
        all_results.append(results)
        
        # Save individual run results
        save_results(results, args.output_dir, args.algo, args.env)
    
    # Compute aggregate statistics
    if args.num_runs > 1:
        aggregate_results = {
            'algorithm': args.algo,
            'environment': args.env,
            'num_runs': args.num_runs,
            'aggregate_stats': {
                'mean_final_reward': np.mean([r['final_avg_reward'] for r in all_results]),
                'std_final_reward': np.std([r['final_avg_reward'] for r in all_results]),
                'mean_success_rate': np.mean([r['final_success_rate'] for r in all_results]),
                'std_success_rate': np.std([r['final_success_rate'] for r in all_results]),
                'mean_training_time': np.mean([r['training_time'] for r in all_results]),
            },
            'individual_runs': all_results
        }
        
        # Save aggregate results
        save_results(aggregate_results, args.output_dir, f"{args.algo}_aggregate", args.env)
    
    logger.info("Benchmark completed successfully!")


if __name__ == "__main__":
    main()