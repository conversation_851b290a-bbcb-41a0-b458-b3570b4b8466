#!/usr/bin/env python3
"""
Real-World Case Studies for SD-HRL

This script demonstrates SD-HRL application to real-world scenarios:
1. CityFlow: Traffic signal control in urban environments
2. IEEE-33: Smart grid voltage control and power management
3. Warehouse: Multi-robot coordination for logistics

These case studies show the practical applicability of SD-HRL beyond
academic benchmarks.
"""

import argparse
import logging
import os
import sys
import time
from pathlib import Path
from typing import Dict, Any, List, Tuple
import numpy as np
import yaml
from datetime import datetime

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.utils.logger import setup_logger

logger = logging.getLogger(__name__)


class RealWorldEnvironment:
    """Base class for real-world environment adapters."""
    
    def __init__(self, name: str, config: Dict[str, Any]):
        self.name = name
        self.config = config
        self.agents = []
        self.state = None
        self.episode_step = 0
        self.episode_limit = config.get('episode_limit', 1000)
    
    def reset(self) -> Dict[str, Any]:
        """Reset environment to initial state."""
        raise NotImplementedError
    
    def step(self, actions: Dict[str, int]) -> Tuple[Dict, Dict, Dict, Dict]:
        """Execute actions and return next state."""
        raise NotImplementedError
    
    def get_metrics(self) -> Dict[str, float]:
        """Get environment-specific performance metrics."""
        raise NotImplementedError


class CityFlowEnvironment(RealWorldEnvironment):
    """
    CityFlow Traffic Signal Control Environment
    
    Simulates urban traffic management where SD-HRL agents control
    traffic lights to optimize traffic flow and minimize delays.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("CityFlow", config)
        
        # Traffic network parameters
        self.num_intersections = config.get('num_intersections', 16)
        self.num_lanes = config.get('num_lanes', 4)
        self.traffic_density = config.get('traffic_density', 0.3)
        
        # Initialize traffic state
        self.traffic_lights = {}
        self.vehicle_queues = {}
        self.waiting_times = {}
        
        # Create agents (one per intersection)
        self.agents = [f"intersection_{i}" for i in range(self.num_intersections)]
        
        # Performance tracking
        self.total_waiting_time = 0
        self.total_throughput = 0
        self.congestion_events = 0
        
        logger.info(f"CityFlow environment initialized: {self.num_intersections} intersections")
    
    def reset(self) -> Dict[str, Any]:
        """Reset traffic simulation."""
        self.episode_step = 0
        self.total_waiting_time = 0
        self.total_throughput = 0
        self.congestion_events = 0
        
        # Initialize traffic lights (0=Red, 1=Green)
        for agent in self.agents:
            self.traffic_lights[agent] = np.random.randint(0, 2, self.num_lanes)
            self.vehicle_queues[agent] = np.random.poisson(5, self.num_lanes)  # Initial queue lengths
            self.waiting_times[agent] = np.zeros(self.num_lanes)
        
        return self._get_observations()
    
    def step(self, actions: Dict[str, int]) -> Tuple[Dict, Dict, Dict, Dict]:
        """Execute traffic light control actions."""
        self.episode_step += 1
        
        # Update traffic lights based on actions
        for agent, action in actions.items():
            if agent in self.traffic_lights:
                # Action encoding: 0-3 for different light phases
                if action == 0:  # North-South Green
                    self.traffic_lights[agent] = [1, 1, 0, 0]
                elif action == 1:  # East-West Green
                    self.traffic_lights[agent] = [0, 0, 1, 1]
                elif action == 2:  # All Red (safety phase)
                    self.traffic_lights[agent] = [0, 0, 0, 0]
                else:  # Adaptive phase
                    # Choose phase based on queue lengths
                    queues = self.vehicle_queues[agent]
                    if queues[0] + queues[1] > queues[2] + queues[3]:
                        self.traffic_lights[agent] = [1, 1, 0, 0]
                    else:
                        self.traffic_lights[agent] = [0, 0, 1, 1]
        
        # Simulate traffic dynamics
        self._simulate_traffic()
        
        # Calculate rewards
        rewards = self._calculate_rewards()
        
        # Check if episode is done
        done = self.episode_step >= self.episode_limit
        dones = {agent: done for agent in self.agents}
        
        # Get next observations
        observations = self._get_observations()
        
        # Info
        info = {
            'total_waiting_time': self.total_waiting_time,
            'throughput': self.total_throughput,
            'congestion_events': self.congestion_events
        }
        
        return observations, rewards, dones, info
    
    def _simulate_traffic(self):
        """Simulate traffic flow dynamics."""
        for agent in self.agents:
            lights = self.traffic_lights[agent]
            queues = self.vehicle_queues[agent]
            
            for lane in range(self.num_lanes):
                # Vehicles arrive (Poisson process)
                arrivals = np.random.poisson(self.traffic_density)
                queues[lane] += arrivals
                
                # Vehicles depart if light is green
                if lights[lane] == 1:
                    # Service rate depends on queue length (congestion effect)
                    service_rate = max(1, min(5, queues[lane] // 2))
                    departures = min(queues[lane], service_rate)
                    queues[lane] -= departures
                    self.total_throughput += departures
                
                # Update waiting times
                self.waiting_times[agent][lane] += queues[lane]
                self.total_waiting_time += queues[lane]
                
                # Count congestion events
                if queues[lane] > 20:  # Congestion threshold
                    self.congestion_events += 1
    
    def _calculate_rewards(self) -> Dict[str, float]:
        """Calculate rewards for traffic optimization."""
        rewards = {}
        
        for agent in self.agents:
            queues = self.vehicle_queues[agent]
            waiting = self.waiting_times[agent]
            
            # Reward components
            throughput_reward = np.sum([1 if light == 1 else 0 for light in self.traffic_lights[agent]])
            queue_penalty = -np.sum(queues) * 0.1
            waiting_penalty = -np.sum(waiting) * 0.01
            congestion_penalty = -5 if np.any(queues > 20) else 0
            
            rewards[agent] = throughput_reward + queue_penalty + waiting_penalty + congestion_penalty
        
        return rewards
    
    def _get_observations(self) -> Dict[str, np.ndarray]:
        """Get traffic state observations."""
        observations = {}
        
        for agent in self.agents:
            # Local observation: own intersection state
            local_obs = np.concatenate([
                self.traffic_lights[agent],  # Current light phases
                self.vehicle_queues[agent],  # Queue lengths
                self.waiting_times[agent] / 100.0  # Normalized waiting times
            ])
            
            # Neighbor observations (simplified)
            neighbor_obs = np.zeros(8)  # Placeholder for neighbor states
            
            observations[agent] = np.concatenate([local_obs, neighbor_obs])
        
        return observations
    
    def get_metrics(self) -> Dict[str, float]:
        """Get traffic performance metrics."""
        if self.episode_step == 0:
            return {}
        
        avg_waiting_time = self.total_waiting_time / (self.episode_step * len(self.agents))
        throughput_rate = self.total_throughput / self.episode_step
        congestion_rate = self.congestion_events / (self.episode_step * len(self.agents))
        
        return {
            'average_waiting_time': avg_waiting_time,
            'throughput_rate': throughput_rate,
            'congestion_rate': congestion_rate,
            'total_throughput': self.total_throughput
        }


class IEEE33Environment(RealWorldEnvironment):
    """
    IEEE-33 Bus Smart Grid Environment
    
    Simulates distributed voltage control in a power distribution network
    where SD-HRL agents manage voltage regulators and reactive power sources.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("IEEE33", config)
        
        # Power system parameters
        self.num_buses = config.get('num_buses', 33)
        self.num_agents = config.get('num_agents', 8)  # Distributed control points
        self.voltage_limits = config.get('voltage_limits', [0.95, 1.05])
        self.load_variation = config.get('load_variation', 0.2)
        
        # Initialize power system state
        self.bus_voltages = np.ones(self.num_buses)  # Per-unit voltages
        self.bus_loads = np.random.uniform(0.5, 1.5, self.num_buses)  # MW loads
        self.reactive_power = np.zeros(self.num_buses)  # MVAR
        
        # Create agents (distributed controllers)
        self.agents = [f"controller_{i}" for i in range(self.num_agents)]
        self.agent_bus_mapping = self._create_agent_mapping()
        
        # Performance tracking
        self.voltage_violations = 0
        self.power_losses = 0
        self.total_cost = 0
        
        logger.info(f"IEEE-33 environment initialized: {self.num_buses} buses, {self.num_agents} controllers")
    
    def _create_agent_mapping(self) -> Dict[str, List[int]]:
        """Map agents to controlled buses."""
        buses_per_agent = self.num_buses // self.num_agents
        mapping = {}
        
        for i, agent in enumerate(self.agents):
            start_bus = i * buses_per_agent
            end_bus = min((i + 1) * buses_per_agent, self.num_buses)
            mapping[agent] = list(range(start_bus, end_bus))
        
        return mapping
    
    def reset(self) -> Dict[str, Any]:
        """Reset power system simulation."""
        self.episode_step = 0
        self.voltage_violations = 0
        self.power_losses = 0
        self.total_cost = 0
        
        # Initialize system state
        self.bus_voltages = np.ones(self.num_buses) + np.random.normal(0, 0.02, self.num_buses)
        self.bus_loads = np.random.uniform(0.5, 1.5, self.num_buses)
        self.reactive_power = np.zeros(self.num_buses)
        
        return self._get_observations()
    
    def step(self, actions: Dict[str, int]) -> Tuple[Dict, Dict, Dict, Dict]:
        """Execute voltage control actions."""
        self.episode_step += 1
        
        # Apply control actions
        for agent, action in actions.items():
            controlled_buses = self.agent_bus_mapping[agent]
            
            for bus in controlled_buses:
                # Action encoding: 0=decrease, 1=maintain, 2=increase reactive power
                if action == 0:  # Decrease reactive power
                    self.reactive_power[bus] -= 0.1
                elif action == 2:  # Increase reactive power
                    self.reactive_power[bus] += 0.1
                # action == 1: maintain current level
                
                # Clamp reactive power limits
                self.reactive_power[bus] = np.clip(self.reactive_power[bus], -2.0, 2.0)
        
        # Simulate power system dynamics
        self._simulate_power_flow()
        
        # Calculate rewards
        rewards = self._calculate_rewards()
        
        # Check if episode is done
        done = self.episode_step >= self.episode_limit
        dones = {agent: done for agent in self.agents}
        
        # Get next observations
        observations = self._get_observations()
        
        # Info
        info = {
            'voltage_violations': self.voltage_violations,
            'power_losses': self.power_losses,
            'total_cost': self.total_cost
        }
        
        return observations, rewards, dones, info
    
    def _simulate_power_flow(self):
        """Simulate power flow and voltage dynamics."""
        # Simplified power flow calculation
        for i in range(self.num_buses):
            # Load variation
            load_change = np.random.normal(0, self.load_variation * 0.1)
            self.bus_loads[i] += load_change
            self.bus_loads[i] = max(0.1, self.bus_loads[i])  # Minimum load
            
            # Voltage calculation (simplified)
            # V = V0 - (P*R + Q*X) / V0
            resistance = 0.01  # Simplified line resistance
            reactance = 0.02   # Simplified line reactance
            
            voltage_drop = (self.bus_loads[i] * resistance + 
                          self.reactive_power[i] * reactance) / self.bus_voltages[i]
            
            self.bus_voltages[i] = 1.0 - voltage_drop + np.random.normal(0, 0.005)
            
            # Count voltage violations
            if (self.bus_voltages[i] < self.voltage_limits[0] or 
                self.bus_voltages[i] > self.voltage_limits[1]):
                self.voltage_violations += 1
        
        # Calculate power losses
        self.power_losses += np.sum(self.bus_loads) * 0.05  # Simplified loss calculation
        
        # Calculate operational cost
        self.total_cost += np.sum(np.abs(self.reactive_power)) * 0.1
    
    def _calculate_rewards(self) -> Dict[str, float]:
        """Calculate rewards for voltage control."""
        rewards = {}
        
        for agent in self.agents:
            controlled_buses = self.agent_bus_mapping[agent]
            
            # Voltage regulation reward
            voltage_reward = 0
            for bus in controlled_buses:
                if self.voltage_limits[0] <= self.bus_voltages[bus] <= self.voltage_limits[1]:
                    voltage_reward += 1.0
                else:
                    voltage_reward -= abs(self.bus_voltages[bus] - 1.0) * 10
            
            # Power loss penalty
            loss_penalty = -np.sum([self.bus_loads[bus] for bus in controlled_buses]) * 0.01
            
            # Control effort penalty
            control_penalty = -np.sum([abs(self.reactive_power[bus]) for bus in controlled_buses]) * 0.1
            
            rewards[agent] = voltage_reward + loss_penalty + control_penalty
        
        return rewards
    
    def _get_observations(self) -> Dict[str, np.ndarray]:
        """Get power system state observations."""
        observations = {}
        
        for agent in self.agents:
            controlled_buses = self.agent_bus_mapping[agent]
            
            # Local observations
            local_voltages = [self.bus_voltages[bus] for bus in controlled_buses]
            local_loads = [self.bus_loads[bus] for bus in controlled_buses]
            local_reactive = [self.reactive_power[bus] for bus in controlled_buses]
            
            # System-wide observations (limited)
            system_avg_voltage = np.mean(self.bus_voltages)
            system_total_load = np.sum(self.bus_loads)
            
            observations[agent] = np.array(
                local_voltages + local_loads + local_reactive + 
                [system_avg_voltage, system_total_load]
            )
        
        return observations
    
    def get_metrics(self) -> Dict[str, float]:
        """Get power system performance metrics."""
        if self.episode_step == 0:
            return {}
        
        voltage_deviation = np.mean(np.abs(self.bus_voltages - 1.0))
        violation_rate = self.voltage_violations / (self.episode_step * self.num_buses)
        avg_losses = self.power_losses / self.episode_step
        avg_cost = self.total_cost / self.episode_step
        
        return {
            'voltage_deviation': voltage_deviation,
            'violation_rate': violation_rate,
            'average_losses': avg_losses,
            'average_cost': avg_cost,
            'min_voltage': np.min(self.bus_voltages),
            'max_voltage': np.max(self.bus_voltages)
        }


class WarehouseEnvironment(RealWorldEnvironment):
    """
    Multi-Robot Warehouse Environment
    
    Simulates coordinated robot navigation and task allocation in a warehouse
    setting where SD-HRL agents manage robot fleets for order fulfillment.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("Warehouse", config)
        
        # Warehouse parameters
        self.warehouse_size = config.get('warehouse_size', [20, 20])
        self.num_robots = config.get('num_robots', 6)
        self.num_shelves = config.get('num_shelves', 50)
        self.num_orders = config.get('num_orders', 10)
        
        # Initialize warehouse layout
        self.robot_positions = {}
        self.shelf_positions = {}
        self.order_queue = []
        self.completed_orders = 0
        
        # Create agents (robot controllers)
        self.agents = [f"robot_{i}" for i in range(self.num_robots)]
        
        # Performance tracking
        self.total_distance = 0
        self.collision_count = 0
        self.order_completion_time = []
        
        logger.info(f"Warehouse environment initialized: {self.num_robots} robots, {self.num_shelves} shelves")
    
    def reset(self) -> Dict[str, Any]:
        """Reset warehouse simulation."""
        self.episode_step = 0
        self.completed_orders = 0
        self.total_distance = 0
        self.collision_count = 0
        self.order_completion_time = []
        
        # Initialize robot positions
        for i, agent in enumerate(self.agents):
            self.robot_positions[agent] = [
                np.random.randint(0, self.warehouse_size[0]),
                np.random.randint(0, self.warehouse_size[1])
            ]
        
        # Initialize shelf positions
        for i in range(self.num_shelves):
            self.shelf_positions[f"shelf_{i}"] = [
                np.random.randint(0, self.warehouse_size[0]),
                np.random.randint(0, self.warehouse_size[1])
            ]
        
        # Generate initial orders
        self.order_queue = self._generate_orders(self.num_orders)
        
        return self._get_observations()
    
    def _generate_orders(self, num_orders: int) -> List[Dict]:
        """Generate random orders."""
        orders = []
        for i in range(num_orders):
            order = {
                'id': f"order_{i}",
                'items': [f"shelf_{np.random.randint(0, self.num_shelves)}" 
                         for _ in range(np.random.randint(1, 4))],
                'priority': np.random.uniform(0.1, 1.0),
                'creation_time': self.episode_step
            }
            orders.append(order)
        return orders
    
    def step(self, actions: Dict[str, int]) -> Tuple[Dict, Dict, Dict, Dict]:
        """Execute robot movement and task actions."""
        self.episode_step += 1
        
        # Execute movement actions
        for agent, action in actions.items():
            if agent in self.robot_positions:
                old_pos = self.robot_positions[agent].copy()
                
                # Action encoding: 0=up, 1=down, 2=left, 3=right, 4=stay, 5=pickup, 6=dropoff
                if action == 0:  # Up
                    self.robot_positions[agent][1] = min(
                        self.warehouse_size[1] - 1, 
                        self.robot_positions[agent][1] + 1
                    )
                elif action == 1:  # Down
                    self.robot_positions[agent][1] = max(0, self.robot_positions[agent][1] - 1)
                elif action == 2:  # Left
                    self.robot_positions[agent][0] = max(0, self.robot_positions[agent][0] - 1)
                elif action == 3:  # Right
                    self.robot_positions[agent][0] = min(
                        self.warehouse_size[0] - 1, 
                        self.robot_positions[agent][0] + 1
                    )
                elif action == 4:  # Stay
                    pass
                elif action == 5:  # Pickup
                    self._handle_pickup(agent)
                elif action == 6:  # Dropoff
                    self._handle_dropoff(agent)
                
                # Calculate distance moved
                distance = abs(self.robot_positions[agent][0] - old_pos[0]) + \
                          abs(self.robot_positions[agent][1] - old_pos[1])
                self.total_distance += distance
        
        # Check for collisions
        self._check_collisions()
        
        # Process orders
        self._process_orders()
        
        # Generate new orders occasionally
        if np.random.random() < 0.1:
            new_orders = self._generate_orders(np.random.randint(1, 3))
            self.order_queue.extend(new_orders)
        
        # Calculate rewards
        rewards = self._calculate_rewards()
        
        # Check if episode is done
        done = self.episode_step >= self.episode_limit
        dones = {agent: done for agent in self.agents}
        
        # Get next observations
        observations = self._get_observations()
        
        # Info
        info = {
            'completed_orders': self.completed_orders,
            'total_distance': self.total_distance,
            'collision_count': self.collision_count,
            'pending_orders': len(self.order_queue)
        }
        
        return observations, rewards, dones, info
    
    def _handle_pickup(self, agent: str):
        """Handle item pickup action."""
        robot_pos = self.robot_positions[agent]
        
        # Check if robot is at a shelf location
        for shelf_id, shelf_pos in self.shelf_positions.items():
            if robot_pos == shelf_pos:
                # Simplified pickup logic
                return True
        return False
    
    def _handle_dropoff(self, agent: str):
        """Handle item dropoff action."""
        # Simplified dropoff logic - assume dropoff zone at (0,0)
        robot_pos = self.robot_positions[agent]
        if robot_pos == [0, 0]:
            self.completed_orders += 1
            return True
        return False
    
    def _check_collisions(self):
        """Check for robot collisions."""
        positions = list(self.robot_positions.values())
        for i in range(len(positions)):
            for j in range(i + 1, len(positions)):
                if positions[i] == positions[j]:
                    self.collision_count += 1
    
    def _process_orders(self):
        """Process pending orders."""
        # Simplified order processing
        completed = []
        for order in self.order_queue:
            if np.random.random() < 0.05:  # Random completion chance
                completion_time = self.episode_step - order['creation_time']
                self.order_completion_time.append(completion_time)
                completed.append(order)
        
        for order in completed:
            self.order_queue.remove(order)
    
    def _calculate_rewards(self) -> Dict[str, float]:
        """Calculate rewards for warehouse operations."""
        rewards = {}
        
        base_reward = 1.0  # Base reward for staying active
        
        for agent in self.agents:
            reward = base_reward
            
            # Reward for order completion (shared)
            if self.completed_orders > 0:
                reward += 10.0 / len(self.agents)
            
            # Penalty for collisions
            if self.collision_count > 0:
                reward -= 5.0
            
            # Penalty for excessive movement
            reward -= 0.1  # Movement cost
            
            # Bonus for being near shelves with pending orders
            robot_pos = self.robot_positions[agent]
            min_distance_to_shelf = float('inf')
            for shelf_pos in self.shelf_positions.values():
                distance = abs(robot_pos[0] - shelf_pos[0]) + abs(robot_pos[1] - shelf_pos[1])
                min_distance_to_shelf = min(min_distance_to_shelf, distance)
            
            if min_distance_to_shelf < 3:  # Close to shelf
                reward += 1.0
            
            rewards[agent] = reward
        
        return rewards
    
    def _get_observations(self) -> Dict[str, np.ndarray]:
        """Get warehouse state observations."""
        observations = {}
        
        for agent in self.agents:
            robot_pos = self.robot_positions[agent]
            
            # Local observations
            local_obs = np.array(robot_pos + [len(self.order_queue), self.completed_orders])
            
            # Nearby robot positions (within range)
            nearby_robots = []
            for other_agent, other_pos in self.robot_positions.items():
                if other_agent != agent:
                    distance = abs(robot_pos[0] - other_pos[0]) + abs(robot_pos[1] - other_pos[1])
                    if distance <= 5:  # Within communication range
                        nearby_robots.extend([other_pos[0], other_pos[1], distance])
            
            # Pad or truncate nearby robot observations
            nearby_obs = np.array(nearby_robots[:12])  # Max 4 nearby robots
            if len(nearby_obs) < 12:
                nearby_obs = np.pad(nearby_obs, (0, 12 - len(nearby_obs)))
            
            observations[agent] = np.concatenate([local_obs, nearby_obs])
        
        return observations
    
    def get_metrics(self) -> Dict[str, float]:
        """Get warehouse performance metrics."""
        if self.episode_step == 0:
            return {}
        
        avg_completion_time = np.mean(self.order_completion_time) if self.order_completion_time else 0
        throughput = self.completed_orders / self.episode_step
        efficiency = self.completed_orders / max(1, self.total_distance)
        collision_rate = self.collision_count / self.episode_step
        
        return {
            'average_completion_time': avg_completion_time,
            'throughput': throughput,
            'efficiency': efficiency,
            'collision_rate': collision_rate,
            'total_completed_orders': self.completed_orders,
            'pending_orders': len(self.order_queue)
        }


def create_environment(env_name: str, config: Dict[str, Any]) -> RealWorldEnvironment:
    """Create real-world environment based on name."""
    if env_name == "cityflow":
        return CityFlowEnvironment(config)
    elif env_name == "ieee33":
        return IEEE33Environment(config)
    elif env_name == "warehouse":
        return WarehouseEnvironment(config)
    else:
        raise ValueError(f"Unknown environment: {env_name}")


def load_config(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file."""
    if not os.path.exists(config_path):
        # Return default configs based on environment
        return {
            'cityflow': {
                'num_intersections': 16,
                'traffic_density': 0.3,
                'episode_limit': 1000
            },
            'ieee33': {
                'num_buses': 33,
                'num_agents': 8,
                'episode_limit': 500
            },
            'warehouse': {
                'warehouse_size': [20, 20],
                'num_robots': 6,
                'episode_limit': 800
            }
        }
    
    with open(config_path, 'r') as f:
        return yaml.safe_load(f)


def run_case_study(env_name: str, config: Dict[str, Any], num_episodes: int = 100):
    """Run a real-world case study."""
    logger.info(f"Starting {env_name.upper()} case study")
    
    # Create environment
    env = create_environment(env_name, config)
    
    # Run episodes
    episode_metrics = []
    
    for episode in range(num_episodes):
        obs = env.reset()
        episode_reward = 0
        done = False
        
        while not done:
            # Simple random policy for demonstration
            # In practice, this would use trained SD-HRL agents
            actions = {}
            for agent in env.agents:
                if env_name == "cityflow":
                    actions[agent] = np.random.randint(0, 4)  # Traffic light phases
                elif env_name == "ieee33":
                    actions[agent] = np.random.randint(0, 3)  # Voltage control
                elif env_name == "warehouse":
                    actions[agent] = np.random.randint(0, 7)  # Robot actions
            
            next_obs, rewards, dones, info = env.step(actions)
            episode_reward += sum(rewards.values())
            done = all(dones.values())
            obs = next_obs
        
        # Collect episode metrics
        metrics = env.get_metrics()
        metrics['episode_reward'] = episode_reward
        metrics['episode'] = episode
        episode_metrics.append(metrics)
        
        if episode % 20 == 0:
            logger.info(f"Episode {episode}: Reward = {episode_reward:.2f}")
            if metrics:
                logger.info(f"  Metrics: {metrics}")
    
    return episode_metrics


def save_case_study_results(results: List[Dict], env_name: str, output_dir: str):
    """Save case study results."""
    os.makedirs(output_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{env_name}_case_study_{timestamp}.yaml"
    filepath = os.path.join(output_dir, filename)
    
    # Compute summary statistics
    summary = {
        'environment': env_name,
        'num_episodes': len(results),
        'timestamp': timestamp,
        'summary_statistics': {},
        'episode_results': results
    }
    
    if results:
        # Calculate averages for numeric metrics
        numeric_keys = [k for k in results[0].keys() if isinstance(results[0][k], (int, float))]
        for key in numeric_keys:
            values = [r[key] for r in results if key in r]
            if values:
                summary['summary_statistics'][key] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values)
                }
    
    with open(filepath, 'w') as f:
        yaml.dump(summary, f, default_flow_style=False)
    
    logger.info(f"Case study results saved to {filepath}")
    return filepath


def main():
    parser = argparse.ArgumentParser(description="Run real-world case studies")
    parser.add_argument("--env", required=True, 
                       choices=["cityflow", "ieee33", "warehouse"],
                       help="Real-world environment to test")
    parser.add_argument("--config", default=None,
                       help="Configuration file path")
    parser.add_argument("--num_episodes", type=int, default=100,
                       help="Number of episodes to run")
    parser.add_argument("--output_dir", default="results/realworld",
                       help="Output directory for results")
    
    args = parser.parse_args()
    
    # Setup logging
    setup_logger(level=logging.INFO)
    
    # Load configuration
    if args.config:
        config = load_config(args.config)
        env_config = config.get(args.env, {})
    else:
        default_configs = load_config("")
        env_config = default_configs.get(args.env, {})
    
    logger.info(f"Running {args.env.upper()} case study with config: {env_config}")
    
    # Run case study
    results = run_case_study(args.env, env_config, args.num_episodes)
    
    # Save results
    output_file = save_case_study_results(results, args.env, args.output_dir)
    
    # Print summary
    if results:
        final_metrics = results[-1]
        logger.info(f"\nCase Study Summary for {args.env.upper()}:")
        logger.info(f"  Episodes completed: {len(results)}")
        logger.info(f"  Final episode reward: {final_metrics.get('episode_reward', 'N/A'):.2f}")
        
        # Environment-specific metrics
        if args.env == "cityflow":
            logger.info(f"  Average waiting time: {final_metrics.get('average_waiting_time', 'N/A'):.2f}")
            logger.info(f"  Throughput rate: {final_metrics.get('throughput_rate', 'N/A'):.2f}")
        elif args.env == "ieee33":
            logger.info(f"  Voltage deviation: {final_metrics.get('voltage_deviation', 'N/A'):.4f}")
            logger.info(f"  Violation rate: {final_metrics.get('violation_rate', 'N/A'):.4f}")
        elif args.env == "warehouse":
            logger.info(f"  Completed orders: {final_metrics.get('total_completed_orders', 'N/A')}")
            logger.info(f"  Efficiency: {final_metrics.get('efficiency', 'N/A'):.4f}")
    
    logger.info(f"\nResults saved to: {output_file}")
    logger.info("Case study completed successfully!")


if __name__ == "__main__":
    main()