"""
GridWorld Environment

Advanced grid-based multi-agent environment supporting static navigation,
multi-goal tasks, interactive elements, and dynamic events.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from gymnasium import spaces
import logging

from .base_env import BaseMultiAgentEnv
from .dynamic_events import EventScheduler

logger = logging.getLogger(__name__)


class GridWorldEnv(BaseMultiAgentEnv):
    """
    Multi-agent grid world environment with advanced features.
    
    Supports three modes:
    - static: Classic navigation with fixed obstacles
    - multi_goal: Task assignment and coordination
    - interactive: Agents can modify environment (switches, gates)
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Grid configuration
        self.map_size = config.get("map_size", [10, 10])
        self.grid_height, self.grid_width = self.map_size
        
        # Environment mode
        self.mode = config.get("mode", "multi_goal")  # static, multi_goal, interactive
        
        # Obstacles and walls
        self.num_obstacles = config.get("obstacles", 5)
        self.wall_density = config.get("wall_density", 0.1)
        self.moving_obstacles = config.get("moving_obstacles", False)
        
        # Goals and rewards
        self.goal_locations = config.get("goal_locations", [[1,1], [8,8]])
        self._original_goal_locations = self.goal_locations.copy()  # Store original for fixed obs size
        self.multi_goal = config.get("multi_goal", True)
        self.goal_reward = config.get("goal_reward", 10.0)
        self.step_penalty = config.get("step_penalty", -0.01)
        self.collision_penalty = config.get("collision_penalty", -0.5)
        self.cooperation_bonus = config.get("cooperation_bonus", 2.0)
        
        # Observation configuration
        self.observation_type = config.get("observation_type", "partial")
        self.observation_radius = config.get("observation_radius", 3)
        self.include_agent_positions = config.get("include_agent_positions", True)
        self.include_goal_info = config.get("include_goal_info", True)
        self.include_communication = config.get("include_communication", True)
        
        # Episode configuration
        self.max_steps_without_progress = config.get("max_steps_without_progress", 50)
        
        # State tracking
        self.agent_positions = {}
        self.agent_goals = {}  # For multi-goal mode
        self.goal_timers = {}  # Goal expiration timers
        self.steps_without_progress = 0
        self.last_progress_step = 0
        
        # Interactive elements (for interactive mode)
        self.switches = {}  # position -> state
        self.gates = {}     # position -> open/closed
        
        # Initialize environment
        self._setup_spaces()
        self._initialize_grid()
        
        # Dynamic events
        self.event_scheduler = EventScheduler(config.get("events", {}))
        
        logger.info(f"GridWorld initialized: {self.grid_height}x{self.grid_width}, "
                   f"{self.num_agents} agents, mode: {self.mode}")
    
    def _setup_spaces(self):
        """Setup action and observation spaces."""
        # Action space: [up, down, left, right, stay]
        self.action_space = spaces.Discrete(5)
        
        # Observation space depends on observation type
        if self.observation_type == "partial":
            obs_size = (2 * self.observation_radius + 1) ** 2
            if self.include_agent_positions:
                obs_size += self.num_agents * 2  # x, y for each agent
            if self.include_goal_info:
                obs_size += len(self.goal_locations) * 3  # x, y, distance for each goal
            if self.include_communication:
                obs_size += self.num_agents  # Communication channel per agent
        else:  # global
            obs_size = self.grid_height * self.grid_width + self.num_agents * 2
        
        self.observation_space = spaces.Box(
            low=0, high=1, shape=(obs_size,), dtype=np.float32
        )
    
    def _initialize_grid(self):
        """Initialize the grid with obstacles and goals."""
        # Create empty grid
        self.grid = np.zeros((self.grid_height, self.grid_width), dtype=np.int32)
        
        # Add walls around border
        self.grid[0, :] = 1  # Top wall
        self.grid[-1, :] = 1  # Bottom wall
        self.grid[:, 0] = 1  # Left wall
        self.grid[:, -1] = 1  # Right wall
        
        # Add random obstacles
        self._place_obstacles()
        
        # Mark goal locations
        for goal in self.goal_locations:
            if 0 <= goal[0] < self.grid_height and 0 <= goal[1] < self.grid_width:
                self.grid[goal[0], goal[1]] = 2  # Goal marker
        
        # Initialize interactive elements for interactive mode
        if self.mode == "interactive":
            self._initialize_interactive_elements()
    
    def _place_obstacles(self):
        """Place obstacles randomly in the grid."""
        obstacle_count = 0
        max_attempts = self.num_obstacles * 10
        attempts = 0
        
        while obstacle_count < self.num_obstacles and attempts < max_attempts:
            x = np.random.randint(1, self.grid_height - 1)
            y = np.random.randint(1, self.grid_width - 1)
            
            # Don't place obstacles on goals or existing obstacles
            if self.grid[x, y] == 0 and [x, y] not in self.goal_locations:
                self.grid[x, y] = 1
                obstacle_count += 1
            
            attempts += 1
    
    def _initialize_interactive_elements(self):
        """Initialize switches and gates for interactive mode."""
        # Place switches
        num_switches = max(1, self.num_obstacles // 3)
        for _ in range(num_switches):
            x = np.random.randint(1, self.grid_height - 1)
            y = np.random.randint(1, self.grid_width - 1)
            if self.grid[x, y] == 0:
                self.switches[(x, y)] = False  # Initially off
                self.grid[x, y] = 3  # Switch marker
        
        # Place gates
        num_gates = max(1, len(self.switches))
        for _ in range(num_gates):
            x = np.random.randint(1, self.grid_height - 1)
            y = np.random.randint(1, self.grid_width - 1)
            if self.grid[x, y] == 0:
                self.gates[(x, y)] = False  # Initially closed
                self.grid[x, y] = 4  # Gate marker
    
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """Reset environment to initial state."""
        self.current_step = 0
        self.episode_count += 1
        self.steps_without_progress = 0
        self.last_progress_step = 0
        
        # Reset metrics
        self.episode_metrics = {}
        
        # Reinitialize grid
        self._initialize_grid()
        
        # Reset agent positions
        self._reset_agent_positions()
        
        # Reset goal assignments for multi-goal mode
        if self.mode == "multi_goal":
            self._assign_goals()
        
        # Reset goal timers
        self.goal_timers = {i: np.random.randint(50, 150) for i in range(len(self.goal_locations))}
        
        # Reset event scheduler
        self.event_scheduler.reset()
        
        # Get initial observations
        observations = self.get_obs()
        info = self._get_info()
        
        logger.debug(f"Environment reset, episode {self.episode_count}")
        return observations, info
    
    def _reset_agent_positions(self):
        """Reset agent positions to valid starting locations."""
        self.agent_positions = {}
        
        for agent_id in self.agent_ids:
            placed = False
            attempts = 0
            max_attempts = 100
            
            while not placed and attempts < max_attempts:
                x = np.random.randint(1, self.grid_height - 1)
                y = np.random.randint(1, self.grid_width - 1)
                
                # Check if position is valid (empty and not occupied)
                if (self.grid[x, y] == 0 and 
                    (x, y) not in self.agent_positions.values()):
                    self.agent_positions[agent_id] = (x, y)
                    placed = True
                
                attempts += 1
            
            if not placed:
                logger.warning(f"Could not place {agent_id}, using fallback position")
                self.agent_positions[agent_id] = (1, 1)
    
    def _assign_goals(self):
        """Assign goals to agents for multi-goal mode."""
        self.agent_goals = {}
        
        # If no goals left, use original goals
        if len(self.goal_locations) == 0:
            self.goal_locations = self._original_goal_locations.copy()
        
        if len(self.goal_locations) >= self.num_agents:
            # Assign unique goals
            goals = self.goal_locations.copy()
            np.random.shuffle(goals)
            for i, agent_id in enumerate(self.agent_ids):
                self.agent_goals[agent_id] = goals[i]
        else:
            # Multiple agents per goal
            for i, agent_id in enumerate(self.agent_ids):
                goal_idx = i % len(self.goal_locations)
                self.agent_goals[agent_id] = self.goal_locations[goal_idx]
    
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray], Dict[str, float], Dict[str, bool], Dict[str, Any]
    ]:
        """Execute one environment step."""
        self.current_step += 1
        self.total_steps += 1
        
        # Process actions
        rewards = {}
        dones = {}
        step_info = {"collisions": 0, "cooperation_events": 0}
        
        # Move agents
        new_positions = {}
        for agent_id in self.get_active_agents():
            if agent_id in actions:
                new_pos = self._get_new_position(agent_id, actions[agent_id])
                new_positions[agent_id] = new_pos
        
        # Check for collisions and resolve conflicts
        resolved_positions = self._resolve_collisions(new_positions, step_info)
        
        # Update agent positions
        for agent_id, new_pos in resolved_positions.items():
            self.agent_positions[agent_id] = new_pos
        
        # Process interactive elements
        if self.mode == "interactive":
            self._process_interactive_elements()
        
        # Calculate rewards
        for agent_id in self.get_active_agents():
            rewards[agent_id] = self._calculate_reward(agent_id, step_info)
            dones[agent_id] = False
        
        # Check episode termination
        episode_done = self._check_episode_done()
        if episode_done:
            for agent_id in self.get_active_agents():
                dones[agent_id] = True
            self._finalize_episode_metrics()
        
        # Update dynamic events
        self.event_scheduler.update(self, self.current_step)
        
        # Update goal timers
        self._update_goal_timers()
        
        # Update metrics
        self._update_metrics(step_info)
        
        # Get observations and info
        observations = self.get_obs()
        info = self._get_info()
        info.update(step_info)
        
        return observations, rewards, dones, info
    
    def _get_new_position(self, agent_id: str, action: int) -> Tuple[int, int]:
        """Get new position based on action."""
        current_pos = self.agent_positions[agent_id]
        x, y = current_pos
        
        # Action mapping: 0=up, 1=down, 2=left, 3=right, 4=stay
        if action == 0:  # up
            new_pos = (max(0, x - 1), y)
        elif action == 1:  # down
            new_pos = (min(self.grid_height - 1, x + 1), y)
        elif action == 2:  # left
            new_pos = (x, max(0, y - 1))
        elif action == 3:  # right
            new_pos = (x, min(self.grid_width - 1, y + 1))
        else:  # stay
            new_pos = current_pos
        
        # Check if new position is valid
        if self._is_valid_position(new_pos):
            return new_pos
        else:
            return current_pos  # Stay in place if invalid move
    
    def _is_valid_position(self, pos: Tuple[int, int]) -> bool:
        """Check if position is valid (not wall or obstacle)."""
        x, y = pos
        if (0 <= x < self.grid_height and 0 <= y < self.grid_width):
            cell_value = self.grid[x, y]
            # 0=empty, 2=goal, 3=switch are valid; 1=wall, 4=closed_gate are invalid
            if cell_value in [0, 2, 3]:
                return True
            elif cell_value == 4:  # Gate
                return self.gates.get((x, y), False)  # Open gates are valid
        return False
    
    def _resolve_collisions(self, new_positions: Dict[str, Tuple[int, int]], 
                          step_info: Dict[str, Any]) -> Dict[str, Tuple[int, int]]:
        """Resolve position conflicts between agents."""
        resolved = {}
        position_counts = {}
        
        # Count how many agents want each position
        for agent_id, pos in new_positions.items():
            if pos not in position_counts:
                position_counts[pos] = []
            position_counts[pos].append(agent_id)
        
        # Resolve conflicts
        for pos, agents in position_counts.items():
            if len(agents) == 1:
                # No conflict
                resolved[agents[0]] = pos
            else:
                # Collision - agents stay in original positions
                step_info["collisions"] += len(agents)
                for agent_id in agents:
                    resolved[agent_id] = self.agent_positions[agent_id]
        
        return resolved
    
    def _process_interactive_elements(self):
        """Process switch activations and gate states."""
        # Check if agents are on switches
        for agent_id, pos in self.agent_positions.items():
            if pos in self.switches:
                self.switches[pos] = True  # Activate switch
        
        # Update gate states based on switches
        for gate_pos in self.gates:
            # Gate opens if any switch is activated
            self.gates[gate_pos] = any(self.switches.values())
            
            # Update grid visualization
            if self.gates[gate_pos]:
                self.grid[gate_pos[0], gate_pos[1]] = 0  # Open gate (passable)
            else:
                self.grid[gate_pos[0], gate_pos[1]] = 4  # Closed gate
    
    def _calculate_reward(self, agent_id: str, step_info: Dict[str, Any]) -> float:
        """Calculate reward for an agent."""
        reward = self.step_penalty  # Base step penalty
        
        agent_pos = self.agent_positions[agent_id]
        
        # Goal rewards
        if self.mode == "multi_goal" and agent_id in self.agent_goals:
            goal_pos = tuple(self.agent_goals[agent_id])
            if agent_pos == goal_pos:
                reward += self.goal_reward
                step_info["success"] = step_info.get("success", 0) + 1
        else:
            # Any goal mode
            for goal_pos in self.goal_locations:
                if agent_pos == tuple(goal_pos):
                    reward += self.goal_reward
                    step_info["success"] = step_info.get("success", 0) + 1
                    break
        
        # Cooperation bonus (agents near each other)
        cooperation_count = 0
        for other_id, other_pos in self.agent_positions.items():
            if other_id != agent_id:
                distance = abs(agent_pos[0] - other_pos[0]) + abs(agent_pos[1] - other_pos[1])
                if distance <= 2:  # Manhattan distance
                    cooperation_count += 1
        
        if cooperation_count > 0:
            reward += self.cooperation_bonus * cooperation_count / self.num_agents
            step_info["cooperation_events"] += cooperation_count
        
        return reward
    
    def _check_episode_done(self) -> bool:
        """Check if episode should terminate."""
        # Standard termination conditions
        if self.is_episode_done():
            return True
        
        # Check if all goals are reached
        if self.mode == "multi_goal":
            all_goals_reached = True
            for agent_id in self.get_active_agents():
                if agent_id in self.agent_goals:
                    goal_pos = tuple(self.agent_goals[agent_id])
                    agent_pos = self.agent_positions[agent_id]
                    if agent_pos != goal_pos:
                        all_goals_reached = False
                        break
            if all_goals_reached:
                return True
        
        # Check for deadlock (no progress)
        if self.current_step - self.last_progress_step > self.max_steps_without_progress:
            self.episode_metrics["deadlock"] = True
            return True
        
        return False
    
    def _update_goal_timers(self):
        """Update goal expiration timers."""
        for i, timer in self.goal_timers.items():
            self.goal_timers[i] = max(0, timer - 1)
            
            # Remove expired goals
            if self.goal_timers[i] == 0 and i < len(self.goal_locations):
                self.goal_locations.pop(i)
                logger.info(f"Goal {i} expired and removed")
    
    def get_obs(self) -> Dict[str, np.ndarray]:
        """Get observations for all agents."""
        observations = {}
        
        for agent_id in self.get_active_agents():
            if self.observation_type == "partial":
                obs = self._get_partial_observation(agent_id)
            else:
                obs = self._get_global_observation(agent_id)
            
            observations[agent_id] = obs.astype(np.float32)
        
        return observations
    
    def _get_partial_observation(self, agent_id: str) -> np.ndarray:
        """Get partial observation for an agent."""
        agent_pos = self.agent_positions[agent_id]
        x, y = agent_pos
        
        # Local grid observation
        obs_grid = []
        for dx in range(-self.observation_radius, self.observation_radius + 1):
            for dy in range(-self.observation_radius, self.observation_radius + 1):
                nx, ny = x + dx, y + dy
                if 0 <= nx < self.grid_height and 0 <= ny < self.grid_width:
                    obs_grid.append(self.grid[nx, ny] / 4.0)  # Normalize
                else:
                    obs_grid.append(1.0)  # Out of bounds = wall
        
        observation = np.array(obs_grid)
        
        # Add agent positions if enabled
        if self.include_agent_positions:
            agent_obs = []
            for other_id in self.agent_ids:
                if other_id in self.agent_positions:
                    other_pos = self.agent_positions[other_id]
                    # Relative position
                    rel_x = (other_pos[0] - x) / self.grid_height
                    rel_y = (other_pos[1] - y) / self.grid_width
                    agent_obs.extend([rel_x, rel_y])
                else:
                    agent_obs.extend([0.0, 0.0])
            observation = np.concatenate([observation, agent_obs])
        
        # Add goal information if enabled
        if self.include_goal_info:
            goal_obs = []
            # Use original goal locations to maintain fixed observation size
            original_goal_locations = getattr(self, '_original_goal_locations', self.goal_locations)
            for i, goal_pos in enumerate(original_goal_locations):
                if i < len(self.goal_locations) and goal_pos in self.goal_locations:
                    # Active goal - compute actual values
                    rel_x = (goal_pos[0] - x) / self.grid_height
                    rel_y = (goal_pos[1] - y) / self.grid_width
                    distance = np.sqrt((goal_pos[0] - x)**2 + (goal_pos[1] - y)**2) / np.sqrt(self.grid_height**2 + self.grid_width**2)
                    goal_obs.extend([rel_x, rel_y, distance])
                else:
                    # Expired/removed goal - use placeholder values
                    goal_obs.extend([0.0, 0.0, 1.0])  # Max distance for removed goals
            observation = np.concatenate([observation, goal_obs])
        
        # Add communication channel if enabled
        if self.include_communication:
            comm_obs = np.zeros(self.num_agents)
            # Simple communication: distance-based signal strength
            for i, other_id in enumerate(self.agent_ids):
                if other_id != agent_id and other_id in self.agent_positions:
                    other_pos = self.agent_positions[other_id]
                    distance = np.sqrt((other_pos[0] - x)**2 + (other_pos[1] - y)**2)
                    comm_obs[i] = max(0, 1 - distance / 10.0)  # Signal strength
            observation = np.concatenate([observation, comm_obs])
        
        return observation
    
    def _get_global_observation(self, agent_id: str) -> np.ndarray:
        """Get global observation for an agent."""
        # Flatten grid
        grid_obs = self.grid.flatten() / 4.0
        
        # Agent positions
        agent_obs = []
        for other_id in self.agent_ids:
            if other_id in self.agent_positions:
                pos = self.agent_positions[other_id]
                agent_obs.extend([pos[0] / self.grid_height, pos[1] / self.grid_width])
            else:
                agent_obs.extend([0.0, 0.0])
        
        return np.concatenate([grid_obs, agent_obs])
    
    def get_state(self) -> np.ndarray:
        """Get global state for centralized critic."""
        # Complete environment state
        state = []
        
        # Grid state
        state.extend(self.grid.flatten())
        
        # Agent positions
        for agent_id in self.agent_ids:
            if agent_id in self.agent_positions:
                pos = self.agent_positions[agent_id]
                state.extend([pos[0], pos[1]])
            else:
                state.extend([0, 0])
        
        # Goal positions
        for goal in self.goal_locations:
            state.extend(goal)
        
        # Interactive elements state
        if self.mode == "interactive":
            for switch_state in self.switches.values():
                state.append(int(switch_state))
            for gate_state in self.gates.values():
                state.append(int(gate_state))
        
        return np.array(state, dtype=np.float32)
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment."""
        if mode == "rgb_array":
            return self._render_rgb()
        elif mode == "human":
            self._render_console()
        
    def _render_rgb(self) -> np.ndarray:
        """Render environment as RGB array."""
        # Create RGB image
        img = np.zeros((self.grid_height, self.grid_width, 3), dtype=np.uint8)
        
        # Color mapping
        colors = {
            0: [255, 255, 255],  # Empty - white
            1: [0, 0, 0],        # Wall - black
            2: [0, 255, 0],      # Goal - green
            3: [255, 255, 0],    # Switch - yellow
            4: [128, 128, 128]   # Gate - gray
        }
        
        # Fill grid colors
        for x in range(self.grid_height):
            for y in range(self.grid_width):
                img[x, y] = colors[self.grid[x, y]]
        
        # Draw agents
        agent_colors = [[255, 0, 0], [0, 0, 255], [255, 0, 255], [0, 255, 255]]
        for i, (agent_id, pos) in enumerate(self.agent_positions.items()):
            color = agent_colors[i % len(agent_colors)]
            img[pos[0], pos[1]] = color
        
        return img
    
    def _render_console(self):
        """Render environment to console."""
        print(f"\nStep {self.current_step}")
        print("Grid:")
        
        for x in range(self.grid_height):
            row = ""
            for y in range(self.grid_width):
                # Check if agent is at this position
                agent_here = None
                for agent_id, pos in self.agent_positions.items():
                    if pos == (x, y):
                        agent_here = agent_id[-1]  # Last character of agent_id
                        break
                
                if agent_here:
                    row += agent_here + " "
                elif self.grid[x, y] == 0:
                    row += ". "
                elif self.grid[x, y] == 1:
                    row += "# "
                elif self.grid[x, y] == 2:
                    row += "G "
                elif self.grid[x, y] == 3:
                    row += "S "
                elif self.grid[x, y] == 4:
                    row += "D " if not self.gates.get((x, y), False) else "O "
            print(row)
    
    def _get_info(self) -> Dict[str, Any]:
        """Get environment info."""
        info = {
            "step": self.current_step,
            "episode": self.episode_count,
            "active_agents": len(self.active_agents),
            "agent_positions": self.agent_positions.copy(),
            "goal_locations": self.goal_locations.copy(),
            "mode": self.mode
        }
        
        if self.mode == "interactive":
            info["switches"] = self.switches.copy()
            info["gates"] = self.gates.copy()
        
        return info
    
    def apply_agent_penalty(self, agent_id: str, penalty: float):
        """Apply penalty to specific agent (for dynamic events)."""
        # This would be called by dynamic events to apply damage/penalties
        # Implementation depends on how rewards are accumulated
        pass
    
    def update_goal_visualization(self):
        """Update goal markers in grid after goal relocation."""
        # Clear old goal markers
        self.grid[self.grid == 2] = 0
        
        # Add new goal markers
        for goal in self.goal_locations:
            if 0 <= goal[0] < self.grid_height and 0 <= goal[1] < self.grid_width:
                self.grid[goal[0], goal[1]] = 2