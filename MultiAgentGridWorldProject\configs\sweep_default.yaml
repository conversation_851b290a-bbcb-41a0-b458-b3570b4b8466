# Default Hyperparameter Sweep Configuration

# Sweep settings
task: "gridworld"  # gridworld, mpe, smac
method: "optuna"   # optuna, ray
n_trials: 50
n_seeds: 3
sweep_epochs: 50  # Reduced epochs for faster sweeps

# Multi-agent settings
multi_agent: false
num_agents: 1

# Results
results_dir: "results"
experiment_name: "hparam_sweep"

# Optuna-specific settings
optuna:
  study_name: null  # Auto-generated if null
  storage: null     # SQLite database path
  sampler: "TPE"    # TPE, Random, CmaEs
  pruner: "Median"  # Median, Hyperband, None

# Ray Tune-specific settings
ray:
  scheduler: "ASHA"     # ASHA, HyperBand, PopulationBased
  search_alg: "Optuna"  # Optuna, HyperOpt, Ax
  num_cpus: 4
  num_gpus: 0

# Search space bounds (used by both methods)
search_space:
  learning_rate:
    min: 1e-5
    max: 1e-2
    log: true
  
  num_options:
    min: 4
    max: 12
  
  hidden_dim:
    choices: [64, 128, 256, 512]
  
  gamma:
    min: 0.95
    max: 0.999
  
  entropy_coef:
    min: 0.001
    max: 0.1
    log: true

# W&B integration (optional)
wandb:
  enabled: false
  project: "hrl_hyperparameter_sweep"
  entity: null
  tags: ["sweep", "hyperparameter_optimization"]

# Hydra configuration
hydra:
  run:
    dir: ./outputs/sweeps/${task}/${method}/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: false