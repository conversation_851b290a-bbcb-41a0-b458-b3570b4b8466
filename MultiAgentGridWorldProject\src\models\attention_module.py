"""
Attention Module

Non-GAT attention mechanisms useful for:
- Coordination (QKV-style agent interaction)
- Fusion of option + state
- Inter-agent modules (if not using GATConv)

Includes scaled dot-product attention with optional skip connections and FFN.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, Any, List
import math
import logging

logger = logging.getLogger(__name__)


class MultiHeadAttention(nn.Module):
    """
    Multi-head scaled dot-product attention.
    
    Supports both self-attention and cross-attention with optional masking.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.d_model = config.get("d_model", 256)
        self.num_heads = config.get("num_heads", 8)
        self.dropout = config.get("dropout", 0.1)
        
        # Ensure d_model is divisible by num_heads
        assert self.d_model % self.num_heads == 0, "d_model must be divisible by num_heads"
        self.d_k = self.d_model // self.num_heads
        
        # Linear projections
        self.w_q = nn.Linear(self.d_model, self.d_model)
        self.w_k = nn.Linear(self.d_model, self.d_model)
        self.w_v = nn.Linear(self.d_model, self.d_model)
        self.w_o = nn.Linear(self.d_model, self.d_model)
        
        # Dropout
        self.dropout_layer = nn.Dropout(self.dropout)
        
        # Initialize weights
        self._initialize_weights()
        
        logger.debug(f"MultiHeadAttention initialized: {self.d_model}D, {self.num_heads} heads")
    
    def _initialize_weights(self):
        """Initialize attention weights."""
        for module in [self.w_q, self.w_k, self.w_v, self.w_o]:
            nn.init.xavier_uniform_(module.weight)
            nn.init.constant_(module.bias, 0.0)
    
    def forward(self, query: torch.Tensor,
                key: Optional[torch.Tensor] = None,
                value: Optional[torch.Tensor] = None,
                mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """Forward pass for multi-head scaled dot-product attention.
        
        Computes attention between query, key, and value tensors using multiple
        attention heads. Supports both self-attention (when key/value are None)
        and cross-attention with optional masking.
        
        Args:
            query (torch.Tensor): Query tensor with shape 
                ``[batch_size, seq_len_q, d_model]``.
            key (torch.Tensor, optional): Key tensor with shape 
                ``[batch_size, seq_len_k, d_model]``. If ``None``, defaults to 
                ``query`` for self-attention. Default: ``None``.
            value (torch.Tensor, optional): Value tensor with shape 
                ``[batch_size, seq_len_v, d_model]``. If ``None``, defaults to 
                ``key``. Default: ``None``.
            mask (torch.Tensor, optional): Attention mask with shape 
                ``[batch_size, seq_len_q, seq_len_k]`` or 
                ``[batch_size, 1, seq_len_q, seq_len_k]``. Values of 0 indicate 
                positions to mask. Default: ``None``.
            return_attention (bool, optional): If ``True``, returns attention 
                weights along with output. Default: ``False``.
                
        Returns:
            Tuple[torch.Tensor, Optional[torch.Tensor]]: A tuple containing:
                - **output** (torch.Tensor): Attention output with shape 
                  ``[batch_size, seq_len_q, d_model]``.
                - **attention_weights** (torch.Tensor or None): Attention weights 
                  with shape ``[batch_size, num_heads, seq_len_q, seq_len_k]`` if 
                  ``return_attention`` is ``True``, otherwise ``None``.
                  
        Note:
            The attention weights sum to 1 along the last dimension (seq_len_k)
            after applying softmax and any masking.
        """
        batch_size, seq_len_q, _ = query.shape
        
        # Default key and value to query (self-attention)
        if key is None:
            key = query
        if value is None:
            value = key
        
        seq_len_k = key.shape[1]
        seq_len_v = value.shape[1]
        
        # Linear projections and reshape for multi-head
        Q = self.w_q(query).view(batch_size, seq_len_q, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len_k, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len_v, self.num_heads, self.d_k).transpose(1, 2)
        
        # Scaled dot-product attention
        output, attention_weights = self._scaled_dot_product_attention(Q, K, V, mask)
        
        # Concatenate heads and apply output projection
        output = output.transpose(1, 2).contiguous().view(batch_size, seq_len_q, self.d_model)
        output = self.w_o(output)
        
        if return_attention:
            return output, attention_weights
        else:
            return output, None
    
    def _scaled_dot_product_attention(self, Q: torch.Tensor, K: torch.Tensor, V: torch.Tensor,
                                    mask: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, torch.Tensor]:
        """Compute scaled dot-product attention."""
        # Compute attention scores
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        
        # Apply mask if provided
        if mask is not None:
            # Expand mask for multi-head
            if mask.dim() == 3:  # [batch_size, seq_len_q, seq_len_k]
                mask = mask.unsqueeze(1)  # [batch_size, 1, seq_len_q, seq_len_k]
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Apply softmax
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout_layer(attention_weights)
        
        # Apply attention to values
        output = torch.matmul(attention_weights, V)
        
        return output, attention_weights


class AttentionModule(nn.Module):
    """
    Complete attention module with optional skip connections and FFN.
    
    Useful for coordination, option-state fusion, and inter-agent communication.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.d_model = config.get("d_model", 256)
        self.num_heads = config.get("num_heads", 8)
        self.d_ff = config.get("d_ff", 1024)
        self.dropout = config.get("dropout", 0.1)
        
        # Module options
        self.use_residual = config.get("use_residual", True)
        self.use_layer_norm = config.get("use_layer_norm", True)
        self.use_ffn = config.get("use_ffn", True)
        self.pre_norm = config.get("pre_norm", False)  # Pre-norm vs post-norm
        
        # Multi-head attention
        self.attention = MultiHeadAttention(config)
        
        # Layer normalization
        if self.use_layer_norm:
            self.norm1 = nn.LayerNorm(self.d_model)
            if self.use_ffn:
                self.norm2 = nn.LayerNorm(self.d_model)
        
        # Feed-forward network
        if self.use_ffn:
            self.ffn = nn.Sequential(
                nn.Linear(self.d_model, self.d_ff),
                nn.ReLU(),
                nn.Dropout(self.dropout),
                nn.Linear(self.d_ff, self.d_model),
                nn.Dropout(self.dropout)
            )
        
        logger.info(f"AttentionModule initialized: {self.d_model}D, {self.num_heads} heads, "
                   f"FFN: {self.use_ffn}, Residual: {self.use_residual}")
    
    def forward(self, x: torch.Tensor,
                key: Optional[torch.Tensor] = None,
                value: Optional[torch.Tensor] = None,
                mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass through attention module.
        
        Args:
            x: Input tensor [batch_size, seq_len, d_model]
            key: Key tensor for cross-attention (optional)
            value: Value tensor for cross-attention (optional)
            mask: Attention mask
            return_attention: Whether to return attention weights
            
        Returns:
            Tuple of (output, attention_weights)
        """
        residual = x
        attention_weights = None
        
        # Pre-norm
        if self.use_layer_norm and self.pre_norm:
            x = self.norm1(x)
        
        # Multi-head attention
        attn_output, attention_weights = self.attention(
            x, key, value, mask, return_attention
        )
        
        # Residual connection
        if self.use_residual:
            x = residual + attn_output
        else:
            x = attn_output
        
        # Post-norm
        if self.use_layer_norm and not self.pre_norm:
            x = self.norm1(x)
        
        # Feed-forward network
        if self.use_ffn:
            residual = x
            
            # Pre-norm for FFN
            if self.use_layer_norm and self.pre_norm:
                x = self.norm2(x)
            
            # FFN
            ffn_output = self.ffn(x)
            
            # Residual connection
            if self.use_residual:
                x = residual + ffn_output
            else:
                x = ffn_output
            
            # Post-norm for FFN
            if self.use_layer_norm and not self.pre_norm:
                x = self.norm2(x)
        
        return x, attention_weights


class CoordinationAttention(nn.Module):
    """
    Specialized attention module for multi-agent coordination.
    
    Enables agents to attend to each other's states and actions
    for coordinated decision-making.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.num_agents = config.get("num_agents", 4)
        self.agent_dim = config.get("agent_dim", 128)
        self.coordination_dim = config.get("coordination_dim", 256)
        
        # Attention configuration
        attn_config = config.copy()
        attn_config.update({
            "d_model": self.coordination_dim,
            "num_heads": config.get("coordination_heads", 4)
        })
        
        # Input projection
        self.input_proj = nn.Linear(self.agent_dim, self.coordination_dim)
        
        # Attention module
        self.attention = AttentionModule(attn_config)
        
        # Output projection
        self.output_proj = nn.Linear(self.coordination_dim, self.agent_dim)
        
        logger.info(f"CoordinationAttention initialized: {self.num_agents} agents, "
                   f"{self.agent_dim}→{self.coordination_dim}D")
    
    def forward(self, agent_states: torch.Tensor,
                agent_mask: Optional[torch.Tensor] = None,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass for coordination attention.
        
        Args:
            agent_states: Agent states [batch_size, num_agents, agent_dim]
            agent_mask: Mask for active agents [batch_size, num_agents]
            return_attention: Whether to return attention weights
            
        Returns:
            Tuple of (coordinated_states, attention_weights)
        """
        batch_size, num_agents, _ = agent_states.shape
        
        # Project to coordination space
        x = self.input_proj(agent_states)
        
        # Create attention mask if agent mask provided
        attention_mask = None
        if agent_mask is not None:
            # Create pairwise mask [batch_size, num_agents, num_agents]
            attention_mask = agent_mask.unsqueeze(1) * agent_mask.unsqueeze(2)
        
        # Apply attention
        coordinated, attention_weights = self.attention(
            x, mask=attention_mask, return_attention=return_attention
        )
        
        # Project back to agent space
        output = self.output_proj(coordinated)
        
        return output, attention_weights


class OptionStateFusion(nn.Module):
    """
    Attention-based fusion of option embeddings and state representations.
    
    Allows options to modulate state processing through attention mechanisms.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.state_dim = config.get("state_dim", 128)
        self.option_dim = config.get("option_dim", 64)
        self.fusion_dim = config.get("fusion_dim", 256)
        
        # Projections
        self.state_proj = nn.Linear(self.state_dim, self.fusion_dim)
        self.option_proj = nn.Linear(self.option_dim, self.fusion_dim)
        
        # Attention configuration
        attn_config = config.copy()
        attn_config.update({
            "d_model": self.fusion_dim,
            "num_heads": config.get("fusion_heads", 4),
            "use_ffn": True
        })
        
        # Cross-attention: state attends to option
        self.cross_attention = AttentionModule(attn_config)
        
        # Output projection
        self.output_proj = nn.Linear(self.fusion_dim, self.state_dim)
        
        logger.info(f"OptionStateFusion initialized: state={self.state_dim}D, "
                   f"option={self.option_dim}D, fusion={self.fusion_dim}D")
    
    def forward(self, state: torch.Tensor,
                option_embedding: torch.Tensor,
                return_attention: bool = False) -> Tuple[torch.Tensor, Optional[torch.Tensor]]:
        """
        Forward pass for option-state fusion.
        
        Args:
            state: State representation [batch_size, seq_len, state_dim]
            option_embedding: Option embedding [batch_size, 1, option_dim] or [batch_size, option_dim]
            return_attention: Whether to return attention weights
            
        Returns:
            Tuple of (fused_state, attention_weights)
        """
        batch_size = state.shape[0]
        
        # Ensure option embedding has sequence dimension
        if option_embedding.dim() == 2:
            option_embedding = option_embedding.unsqueeze(1)  # [batch_size, 1, option_dim]
        
        # Project to fusion space
        state_proj = self.state_proj(state)  # [batch_size, seq_len, fusion_dim]
        option_proj = self.option_proj(option_embedding)  # [batch_size, 1, fusion_dim]
        
        # Cross-attention: state (query) attends to option (key, value)
        fused, attention_weights = self.cross_attention(
            state_proj, key=option_proj, value=option_proj, return_attention=return_attention
        )
        
        # Project back to state space
        output = self.output_proj(fused)
        
        return output, attention_weights


def create_causal_mask(seq_len: int, device: torch.device) -> torch.Tensor:
    """Create causal (lower triangular) attention mask."""
    mask = torch.tril(torch.ones(seq_len, seq_len, device=device))
    return mask


def create_padding_mask(lengths: torch.Tensor, max_len: int) -> torch.Tensor:
    """Create padding mask from sequence lengths."""
    batch_size = lengths.shape[0]
    mask = torch.arange(max_len, device=lengths.device).expand(
        batch_size, max_len
    ) < lengths.unsqueeze(1)
    return mask


def create_agent_mask(active_agents: List[bool]) -> torch.Tensor:
    """Create mask for active agents."""
    return torch.tensor(active_agents, dtype=torch.float32)