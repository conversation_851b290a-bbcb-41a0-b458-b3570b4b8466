"""
Hierarchical Agent Controller

Core HRL controller that ties together:
- Option Policy (π^H)
- Worker Policy (π^L_z) 
- Termination Function (β_z)
- Communication Module (GAT)
- Buffering and step tracking
"""

from typing import Dict, Any, Optional, List, Tuple
import torch
import torch.nn as nn
import numpy as np
from collections import deque, defaultdict
import logging

from .base_agent import BaseAgent
from ..policies.hierarchical_policy import HierarchicalPolicy
from ..comms.graph_attention import MultiAgentCommunicationModule

logger = logging.getLogger(__name__)


class OptionTracker:
    """Tracks option selection and duration for hierarchical policies."""
    
    def __init__(self, max_option_duration: int = 10):
        self.current_option = None
        self.option_start_step = 0
        self.option_duration = 0
        self.max_option_duration = max_option_duration
        
        # Statistics
        self.option_history = deque(maxlen=1000)
        self.option_durations = defaultdict(list)
        self.option_counts = defaultdict(int)
        
        # Success tracking
        self.option_rewards = defaultdict(list)
        self.option_success_count = defaultdict(int)
        self.option_failure_count = defaultdict(int)
        self.last_position = None
        self.position_history = deque(maxlen=10)
    
    def set_option(self, option: int, current_step: int, reward: float = 0.0) -> None:
        """Set new option and update tracking."""
        if self.current_option is not None:
            # Record completed option
            duration = current_step - self.option_start_step
            self.option_durations[self.current_option].append(duration)
            self.option_history.append((self.current_option, duration))
            
            # Evaluate success of previous option
            self._evaluate_option_success(self.current_option, duration, reward)
        
        self.current_option = option
        self.option_start_step = current_step
        self.option_duration = 0
        self.option_counts[option] += 1
        self.position_history.clear()  # Reset position tracking
    
    def _evaluate_option_success(self, option: int, duration: int, final_reward: float) -> None:
        """Evaluate if the completed option was successful."""
        # Success criteria
        was_successful = False
        
        # Criteria 1: Received positive reward
        if final_reward > 0.1:
            was_successful = True
        
        # Criteria 2: Reasonable duration (not too short, not too long)
        elif 3 <= duration <= self.max_option_duration:
            # Check if there was movement
            if len(self.position_history) >= 2:
                unique_positions = len(set(self.position_history))
                if unique_positions > len(self.position_history) * 0.3:  # 30% unique positions
                    was_successful = True
        
        # Criteria 3: Average reward for this option
        if self.option_rewards[option]:
            avg_reward = np.mean(self.option_rewards[option])
            if final_reward > avg_reward:
                was_successful = True
        
        # Update success tracking
        if was_successful:
            self.option_success_count[option] += 1
        else:
            self.option_failure_count[option] += 1
        
        # Store reward for future comparisons
        self.option_rewards[option].append(final_reward)
    
    def step(self) -> None:
        """Update option duration."""
        self.option_duration += 1
    
    def should_terminate(self) -> bool:
        """Check if option should be terminated due to max duration."""
        return self.option_duration >= self.max_option_duration
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get option tracking metrics."""
        metrics = {
            "current_option": self.current_option,
            "option_duration": self.option_duration,
            "option_counts": dict(self.option_counts)
        }
        
        # Average durations
        if self.option_durations:
            avg_durations = {
                opt: np.mean(durations) 
                for opt, durations in self.option_durations.items()
            }
            metrics["avg_option_durations"] = avg_durations
        
        # Success rates
        success_rates = {}
        for option in set(list(self.option_success_count.keys()) + list(self.option_failure_count.keys())):
            success = self.option_success_count[option]
            failure = self.option_failure_count[option]
            total = success + failure
            success_rates[option] = success / total if total > 0 else 0.0
        
        metrics["option_success_rates"] = success_rates
        
        return metrics
    
    def update_position(self, position: Optional[tuple]) -> None:
        """Update position tracking for movement detection."""
        if position is not None:
            self.position_history.append(position)
            self.last_position = position
    
    def reset(self) -> None:
        """Reset for new episode."""
        self.current_option = None
        self.option_start_step = 0
        self.option_duration = 0


class ExperienceBuffer:
    """Local experience buffer for hierarchical agent."""
    
    def __init__(self, capacity: int = 10000):
        self.capacity = capacity
        self.buffer = deque(maxlen=capacity)
        self.option_buffer = deque(maxlen=capacity)  # For option-level transitions
        
    def add_transition(self, transition: Dict[str, Any]) -> None:
        """Add low-level transition."""
        self.buffer.append(transition)
    
    def add_option_transition(self, transition: Dict[str, Any]) -> None:
        """Add option-level transition."""
        self.option_buffer.append(transition)
    
    def sample(self, batch_size: int) -> List[Dict[str, Any]]:
        """Sample batch of transitions."""
        if len(self.buffer) < batch_size:
            return list(self.buffer)
        
        indices = np.random.choice(len(self.buffer), batch_size, replace=False)
        return [self.buffer[i] for i in indices]
    
    def sample_options(self, batch_size: int) -> List[Dict[str, Any]]:
        """Sample batch of option transitions."""
        if len(self.option_buffer) < batch_size:
            return list(self.option_buffer)
        
        indices = np.random.choice(len(self.option_buffer), batch_size, replace=False)
        return [self.option_buffer[i] for i in indices]
    
    def clear(self) -> None:
        """Clear all buffers."""
        self.buffer.clear()
        self.option_buffer.clear()
    
    def __len__(self) -> int:
        return len(self.buffer)


class HierarchicalAgent(BaseAgent):
    """
    Core hierarchical reinforcement learning agent.
    
    Integrates option policy, worker policies, termination functions,
    and optional communication for coordinated multi-agent behavior.
    """
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        super().__init__(agent_id, config)
        
        # Core components
        policy_config = config.copy()  # Use full config for policy
        self.policy = HierarchicalPolicy(policy_config)
        
        # Communication (optional)
        self.use_communication = config.get("use_communication", False)
        if self.use_communication:
            comm_config = config.get("communication", {})
            self.comm_module = MultiAgentCommunicationModule(comm_config)
        else:
            self.comm_module = None
        
        # Option tracking
        max_option_duration = config.get("max_option_duration", 10)
        self.option_tracker = OptionTracker(max_option_duration)
        
        # Experience buffer
        buffer_capacity = config.get("buffer_capacity", 10000)
        self.buffer = ExperienceBuffer(buffer_capacity)
        
        # Learning parameters
        self.option_frequency = config.get("option_frequency", 4)  # Re-sample every K steps
        self.learning_frequency = config.get("learning_frequency", 1)
        self.batch_size = config.get("batch_size", 32)
        
        # Fail-safes
        self.deadlock_threshold = config.get("deadlock_threshold", 50)
        self.no_reward_steps = 0
        self.stuck_detection = config.get("stuck_detection", True)
        self.last_reward = 0.0
        
        # Training mode
        self.training = True  # Default to training mode
        
        # Communication state
        self.last_communication_features = None
        
        logger.info(f"HierarchicalAgent {agent_id} initialized with "
                   f"communication={'enabled' if self.use_communication else 'disabled'}")
    
    def act(self, observation: Dict[str, torch.Tensor]) -> Dict[str, int]:
        """Select action using hierarchical reinforcement learning policy.
        
        Implements the complete HRL decision-making pipeline with temporal abstraction
        through options. The agent maintains a current option and executes actions
        according to the option-conditioned worker policy until termination.
        
        Args:
            observation (Dict[str, torch.Tensor]): Environment observation containing
                state information as tensors. Keys depend on environment but typically
                include 'state', 'position', etc.
                
        Returns:
            Dict[str, int]: Action dictionary containing:
                - **action** (int): Selected discrete action index.
                
        Note:
            The hierarchical decision process follows these steps:
            
            1. **Option Selection**: At t=0 or when the current option terminates,
               sample a new option z ~ π^H(s) from the high-level policy.
            2. **Communication**: If enabled, extract features for multi-agent
               coordination through graph attention networks.
            3. **Action Selection**: Execute action a ~ π^L_z(s, z) using the
               option-conditioned worker policy.
            4. **Termination Check**: Evaluate termination probability β_z(s, z)
               to determine if the current option should end.
            5. **Experience Logging**: Store transition information for learning.
               
        The agent automatically handles option duration tracking, deadlock detection,
        and experience buffer management.
        """
        
        # Step 1: Option selection
        need_new_option = (
            self.option_tracker.current_option is None or
            self.episode_step % self.option_frequency == 0 or
            self.option_tracker.should_terminate()
        )
        
        # Convert observation dict to tensor
        obs_tensor = self._dict_to_tensor(observation)
        
        if need_new_option:
            # Sample new option from high-level policy with exploration
            option_output = self.policy.option_policy(obs_tensor.unsqueeze(0))  # Add batch dim
            option_logits = option_output["option_logits"].squeeze(0)  # Remove batch dim
            option_probs = torch.softmax(option_logits, dim=-1)
            
            # Add exploration bonus based on success rates
            if self.training:
                success_rates = self.option_tracker.get_metrics().get("option_success_rates", {})
                exploration_bonus = torch.zeros_like(option_probs)
                
                for i in range(len(option_probs)):
                    success_rate = success_rates.get(i, 0.5)  # Default to neutral
                    usage_count = self.option_tracker.option_counts.get(i, 0)
                    
                    # Bonus for less-used options and higher success rates
                    usage_bonus = 1.0 / (1.0 + usage_count * 0.1)
                    success_bonus = success_rate * 0.5
                    exploration_bonus[i] = usage_bonus * 0.2 + success_bonus
                
                # Apply exploration bonus
                adjusted_logits = option_logits + exploration_bonus
                adjusted_probs = torch.softmax(adjusted_logits, dim=-1)
                option = torch.multinomial(adjusted_probs, 1).item()
                
                logger.debug(f"Agent {self.agent_id} selected option {option} "
                           f"(orig_prob: {option_probs[option]:.3f}, "
                           f"adj_prob: {adjusted_probs[option]:.3f})")
            else:
                option = torch.multinomial(option_probs, 1).item()
            
            # Get current reward for option evaluation
            current_reward = getattr(self, 'last_reward', 0.0)
            self.option_tracker.set_option(option, self.episode_step, current_reward)
            
            logger.debug(f"Agent {self.agent_id} selected option {option} at step {self.episode_step}")
        
        current_option = self.option_tracker.current_option
        
        # Step 2: Communication (if enabled)
        if self.use_communication and self.comm_module is not None:
            # This would be called by a multi-agent coordinator
            # For now, we store features for external communication
            self.last_communication_features = self._extract_communication_features(observation)
        
        # Step 3: Worker policy action selection
        option_tensor = torch.tensor([current_option], dtype=torch.long)  # Convert to tensor
        worker_output = self.policy.worker_policy(obs_tensor.unsqueeze(0), option_tensor)  # Add batch dim
        actions = worker_output["actions"].squeeze(0)  # Remove batch dim
        action = actions.item() if actions.dim() == 0 else actions[0].item()
        
        # Step 4: Termination check
        termination_output = self.policy.termination_fn(obs_tensor.unsqueeze(0), option_tensor)  # Add batch dim
        termination_prob = termination_output["termination_probs"].squeeze(0)  # Remove batch dim
        should_terminate = torch.bernoulli(termination_prob).item() > 0.5
        
        if should_terminate:
            logger.debug(f"Agent {self.agent_id} terminating option {current_option}")
        
        # Store transition for learning
        transition = {
            "observation": observation,
            "option": current_option,
            "action": action,
            "termination_prob": termination_prob.item(),
            "step": self.episode_step
        }
        self.buffer.add_transition(transition)
        
        # Update tracking
        self.option_tracker.step()
        
        # Update position tracking if available
        position = observation.get('position') if isinstance(observation, dict) else None
        if position is not None:
            if isinstance(position, torch.Tensor):
                position = tuple(position.cpu().numpy())
            elif isinstance(position, np.ndarray):
                position = tuple(position)
            self.option_tracker.update_position(position)
        
        self.last_observation = observation
        self.last_action = {"action": action}
        
        return {"action": action}
    
    def step(self, reward: Optional[float] = None, done: Optional[bool] = None) -> None:
        """Update agent state and handle learning."""
        super().step(reward, done)
        
        # Store reward for option evaluation
        if reward is not None:
            self.last_reward = reward
        
        # Enhanced deadlock detection
        if self.stuck_detection:
            if reward is None or reward <= 0:
                self.no_reward_steps += 1
            else:
                self.no_reward_steps = 0
            
            # Force option change if stuck
            if self.no_reward_steps >= self.deadlock_threshold:
                current_option = self.option_tracker.current_option
                success_rates = self.option_tracker.get_metrics().get("option_success_rates", {})
                current_success_rate = success_rates.get(current_option, 0.0)
                
                logger.warning(f"Agent {self.agent_id} stuck for {self.no_reward_steps} steps, "
                             f"forcing option change from option {current_option} "
                             f"(success_rate: {current_success_rate:.3f})")
                
                # Choose a different option (not the current one)
                available_options = list(range(self.policy.num_options))
                if current_option is not None and current_option in available_options:
                    available_options.remove(current_option)
                
                if available_options:
                    new_option = np.random.choice(available_options)
                else:
                    new_option = np.random.randint(self.policy.num_options)
                
                self.option_tracker.set_option(new_option, self.episode_step, reward or 0.0)
                self.no_reward_steps = 0
                
                logger.debug(f"Agent {self.agent_id} switched from option {current_option} to {new_option}")
        
        # Periodic option statistics logging
        if self.total_steps % 1000 == 0:
            option_metrics = self.option_tracker.get_metrics()
            success_rates = option_metrics.get("option_success_rates", {})
            if success_rates:
                logger.info(f"Agent {self.agent_id} option statistics at step {self.total_steps}:")
                for option_id, success_rate in success_rates.items():
                    usage_count = self.option_tracker.option_counts.get(option_id, 0)
                    logger.info(f"  Option {option_id}: success_rate={success_rate:.3f}, uses={usage_count}")
        
        # Learning
        if self.total_steps % self.learning_frequency == 0 and len(self.buffer) >= self.batch_size:
            self._update_policy()
    
    def learn(self, buffer: Any = None) -> Dict[str, float]:
        """
        Update agent parameters using experience buffer.
        
        Args:
            buffer: External buffer (optional, uses internal buffer if None)
            
        Returns:
            Dictionary of learning metrics
        """
        if buffer is None:
            buffer = self.buffer
        
        if len(buffer) < self.batch_size:
            return {"learning_skipped": 1.0}
        
        return self._update_policy(buffer)
    
    def _update_policy(self, buffer: Optional[ExperienceBuffer] = None) -> Dict[str, float]:
        """Internal policy update method."""
        if buffer is None:
            buffer = self.buffer
        
        # Sample transitions
        transitions = buffer.sample(self.batch_size)
        option_transitions = buffer.sample_options(min(self.batch_size, len(buffer.option_buffer)))
        
        # Convert to tensors
        batch = self._collate_transitions(transitions)
        
        # Update hierarchical policy (placeholder implementation)
        metrics = {"policy_update": 1.0}  # Placeholder - would implement actual policy updates
        
        # Add communication metrics if available
        if self.comm_module is not None:
            comm_metrics = self.comm_module.get_communication_metrics()
            metrics.update({f"comm_{k}": v for k, v in comm_metrics.items()})
        
        # Add option tracking metrics
        option_metrics = self.option_tracker.get_metrics()
        metrics.update({f"option_{k}": v for k, v in option_metrics.items()})
        
        return metrics
    
    def _collate_transitions(self, transitions: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        """Convert list of transitions to batched tensors."""
        if not transitions:
            return {}
        
        # Group by key
        batch = defaultdict(list)
        for transition in transitions:
            for key, value in transition.items():
                if isinstance(value, torch.Tensor):
                    batch[key].append(value)
                elif isinstance(value, (int, float)):
                    batch[key].append(torch.tensor(value))
        
        # Stack tensors
        for key, values in batch.items():
            if values and isinstance(values[0], torch.Tensor):
                batch[key] = torch.stack(values)
        
        return dict(batch)
    
    def _dict_to_tensor(self, observation) -> torch.Tensor:
        """Convert observation to single tensor."""
        if isinstance(observation, dict):
            # Dictionary observation - concatenate all values
            features = []
            for key, value in observation.items():
                if isinstance(value, torch.Tensor):
                    features.append(value.flatten())
                elif isinstance(value, np.ndarray):
                    features.append(torch.from_numpy(value).flatten())
                else:
                    features.append(torch.tensor([value]).float())
            
            if features:
                result = torch.cat(features)
                return result
            else:
                # Fallback to zero tensor
                obs_dim = self.config.get("obs_dim", 128)
                return torch.zeros(obs_dim)
        elif isinstance(observation, torch.Tensor):
            return observation.flatten()
        elif isinstance(observation, np.ndarray):
            tensor = torch.from_numpy(observation).flatten()
            return tensor
        else:
            # Single value observation
            return torch.tensor([observation]).float()
    
    def _extract_communication_features(self, observation: Dict[str, torch.Tensor]) -> torch.Tensor:
        """Extract features for communication."""
        # Use the same tensor conversion for consistency
        return self._dict_to_tensor(observation)
    
    def communicate(self, agent_features: Dict[str, torch.Tensor],
                   agent_positions: Optional[Dict[str, torch.Tensor]] = None,
                   communication_mask: Optional[Dict[str, bool]] = None) -> Dict[str, torch.Tensor]:
        """
        Perform communication with other agents.
        
        This method is called by a multi-agent coordinator.
        """
        if self.comm_module is None:
            return agent_features
        
        return self.comm_module(agent_features, agent_positions, communication_mask)
    
    def train(self, mode: bool = True) -> None:
        """Set training mode."""
        self.training = mode
        if hasattr(self.policy, 'train'):
            self.policy.train(mode)
    
    def eval(self) -> None:
        """Set evaluation mode."""
        self.train(False)
    
    def reset(self) -> None:
        """Reset agent for new episode."""
        super().reset()
        self.option_tracker.reset()
        self.no_reward_steps = 0
        self.last_communication_features = None
        
        logger.debug(f"HierarchicalAgent {self.agent_id} reset for new episode")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get comprehensive agent metrics."""
        metrics = super().get_metrics()
        
        # Add hierarchical-specific metrics
        metrics.update(self.option_tracker.get_metrics())
        metrics.update({
            "buffer_size": len(self.buffer),
            "no_reward_steps": self.no_reward_steps,
            "use_communication": self.use_communication
        })
        
        # Add policy metrics if available
        if hasattr(self.policy, 'get_metrics'):
            policy_metrics = self.policy.get_metrics()
            metrics.update({f"policy_{k}": v for k, v in policy_metrics.items()})
        
        return metrics
    
    def save_state(self) -> Dict[str, Any]:
        """Save complete agent state."""
        state = super().save_state()
        
        # Add hierarchical-specific state
        state.update({
            "option_tracker": {
                "current_option": self.option_tracker.current_option,
                "option_duration": self.option_tracker.option_duration,
                "option_counts": dict(self.option_tracker.option_counts)
            },
            "no_reward_steps": self.no_reward_steps,
            "policy_state": self.policy.state_dict() if hasattr(self.policy, 'state_dict') else None
        })
        
        return state
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """Load complete agent state."""
        super().load_state(state)
        
        # Load hierarchical-specific state
        if "option_tracker" in state:
            tracker_state = state["option_tracker"]
            self.option_tracker.current_option = tracker_state.get("current_option")
            self.option_tracker.option_duration = tracker_state.get("option_duration", 0)
            self.option_tracker.option_counts.update(tracker_state.get("option_counts", {}))
        
        self.no_reward_steps = state.get("no_reward_steps", 0)
        
        # Load policy state
        if "policy_state" in state and state["policy_state"] is not None:
            if hasattr(self.policy, 'load_state_dict'):
                self.policy.load_state_dict(state["policy_state"])
        
        logger.info(f"HierarchicalAgent {self.agent_id} loaded complete state")