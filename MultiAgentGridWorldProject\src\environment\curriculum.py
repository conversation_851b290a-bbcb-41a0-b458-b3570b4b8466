"""
Curriculum Learning Scheduler

Provides progressive difficulty adjustment for multi-agent environments
to improve learning efficiency and generalization.
"""

import numpy as np
from typing import Dict, List, Tuple, Any, Optional, Callable
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class CurriculumMetric(Enum):
    """Metrics used for curriculum progression."""
    SUCCESS_RATE = "success_rate"
    EPISODE_LENGTH = "episode_length"
    AVERAGE_REWARD = "average_reward"
    COVERAGE = "coverage"
    COOPERATION = "cooperation"
    CUSTOM = "custom"


@dataclass
class CurriculumStage:
    """Represents a stage in the curriculum."""
    stage_id: int
    name: str
    parameters: Dict[str, Any]
    success_threshold: float
    min_episodes: int
    max_episodes: int
    description: str = ""


class CurriculumScheduler:
    """
    Manages curriculum learning progression for multi-agent environments.
    
    Automatically adjusts environment difficulty based on agent performance
    to facilitate efficient learning.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        
        # Curriculum configuration
        self.metric = CurriculumMetric(config.get("metric", "success_rate"))
        self.evaluation_window = config.get("evaluation_window", 100)
        self.progression_threshold = config.get("progression_threshold", 0.8)
        self.regression_threshold = config.get("regression_threshold", 0.3)
        self.patience = config.get("patience", 50)
        
        # Curriculum stages
        self.stages = self._create_stages(config.get("stages", []))
        self.current_stage = 0
        self.episodes_in_stage = 0
        self.performance_history = []
        self.stage_history = []
        
        # Custom metric function
        self.custom_metric_fn: Optional[Callable] = None
        
        # Tracking
        self.total_episodes = 0
        self.progression_count = 0
        self.regression_count = 0
        
        logger.info(f"CurriculumScheduler initialized with {len(self.stages)} stages")
        logger.info(f"Metric: {self.metric.value}, Threshold: {self.progression_threshold}")
    
    def _create_stages(self, stage_configs: List[Dict[str, Any]]) -> List[CurriculumStage]:
        """Create curriculum stages from configuration."""
        stages = []
        
        for i, stage_config in enumerate(stage_configs):
            stage = CurriculumStage(
                stage_id=i,
                name=stage_config.get("name", f"Stage_{i}"),
                parameters=stage_config.get("parameters", {}),
                success_threshold=stage_config.get("success_threshold", self.progression_threshold),
                min_episodes=stage_config.get("min_episodes", 50),
                max_episodes=stage_config.get("max_episodes", 500),
                description=stage_config.get("description", "")
            )
            stages.append(stage)
        
        # If no stages provided, create default progression
        if not stages:
            stages = self._create_default_stages()
        
        return stages
    
    def _create_default_stages(self) -> List[CurriculumStage]:
        """Create default curriculum stages."""
        return [
            CurriculumStage(
                stage_id=0,
                name="Easy",
                parameters={"map_size": [8, 8], "obstacles": 3, "num_agents": 2},
                success_threshold=0.7,
                min_episodes=50,
                max_episodes=200,
                description="Small map, few obstacles, 2 agents"
            ),
            CurriculumStage(
                stage_id=1,
                name="Medium",
                parameters={"map_size": [10, 10], "obstacles": 5, "num_agents": 3},
                success_threshold=0.8,
                min_episodes=100,
                max_episodes=300,
                description="Medium map, moderate obstacles, 3 agents"
            ),
            CurriculumStage(
                stage_id=2,
                name="Hard",
                parameters={"map_size": [12, 12], "obstacles": 8, "num_agents": 4},
                success_threshold=0.8,
                min_episodes=150,
                max_episodes=500,
                description="Large map, many obstacles, 4 agents"
            )
        ]
    
    def update(self, env, episode_metrics: Dict[str, Any]) -> bool:
        """
        Update curriculum based on episode performance.
        
        Args:
            env: Environment instance to update
            episode_metrics: Metrics from the completed episode
            
        Returns:
            bool: True if curriculum stage changed
        """
        self.total_episodes += 1
        self.episodes_in_stage += 1
        
        # Calculate performance metric
        performance = self._calculate_performance(episode_metrics)
        self.performance_history.append(performance)
        
        # Keep only recent history
        if len(self.performance_history) > self.evaluation_window:
            self.performance_history.pop(0)
        
        # Check for stage progression
        stage_changed = False
        current_stage_info = self.stages[self.current_stage]
        
        # Only evaluate after minimum episodes in stage
        if self.episodes_in_stage >= current_stage_info.min_episodes:
            recent_performance = np.mean(self.performance_history[-self.evaluation_window:])
            
            # Check for progression
            if (recent_performance >= current_stage_info.success_threshold and 
                self.current_stage < len(self.stages) - 1):
                self._progress_stage(env)
                stage_changed = True
            
            # Check for regression (if performance drops significantly)
            elif (recent_performance < self.regression_threshold and 
                  self.current_stage > 0 and 
                  self.episodes_in_stage > current_stage_info.max_episodes // 2):
                self._regress_stage(env)
                stage_changed = True
            
            # Force progression if max episodes reached
            elif (self.episodes_in_stage >= current_stage_info.max_episodes and 
                  self.current_stage < len(self.stages) - 1):
                logger.info(f"Forcing progression after {self.episodes_in_stage} episodes")
                self._progress_stage(env)
                stage_changed = True
        
        return stage_changed
    
    def _calculate_performance(self, episode_metrics: Dict[str, Any]) -> float:
        """Calculate performance metric for curriculum evaluation."""
        if self.metric == CurriculumMetric.SUCCESS_RATE:
            return episode_metrics.get("success_rate", 0.0)
        
        elif self.metric == CurriculumMetric.EPISODE_LENGTH:
            # Normalize episode length (longer episodes = better performance)
            max_length = episode_metrics.get("episode_limit", 200)
            actual_length = episode_metrics.get("episode_length", 0)
            return actual_length / max_length
        
        elif self.metric == CurriculumMetric.AVERAGE_REWARD:
            return episode_metrics.get("average_reward", 0.0)
        
        elif self.metric == CurriculumMetric.COVERAGE:
            return episode_metrics.get("coverage", 0.0)
        
        elif self.metric == CurriculumMetric.COOPERATION:
            return episode_metrics.get("cooperation", 0.0)
        
        elif self.metric == CurriculumMetric.CUSTOM and self.custom_metric_fn:
            return self.custom_metric_fn(episode_metrics)
        
        else:
            logger.warning(f"Unknown metric: {self.metric}")
            return 0.0
    
    def _progress_stage(self, env):
        """Progress to the next curriculum stage."""
        old_stage = self.current_stage
        self.current_stage = min(self.current_stage + 1, len(self.stages) - 1)
        
        if self.current_stage != old_stage:
            self.episodes_in_stage = 0
            self.progression_count += 1
            
            # Apply new stage parameters to environment
            self._apply_stage_parameters(env, self.stages[self.current_stage])
            
            logger.info(f"Curriculum progressed: Stage {old_stage} -> Stage {self.current_stage}")
            logger.info(f"New stage: {self.stages[self.current_stage].name}")
            
            # Record stage change
            self.stage_history.append({
                'episode': self.total_episodes,
                'old_stage': old_stage,
                'new_stage': self.current_stage,
                'type': 'progression'
            })
    
    def _regress_stage(self, env):
        """Regress to the previous curriculum stage."""
        old_stage = self.current_stage
        self.current_stage = max(self.current_stage - 1, 0)
        
        if self.current_stage != old_stage:
            self.episodes_in_stage = 0
            self.regression_count += 1
            
            # Apply previous stage parameters to environment
            self._apply_stage_parameters(env, self.stages[self.current_stage])
            
            logger.info(f"Curriculum regressed: Stage {old_stage} -> Stage {self.current_stage}")
            logger.info(f"Returning to stage: {self.stages[self.current_stage].name}")
            
            # Record stage change
            self.stage_history.append({
                'episode': self.total_episodes,
                'old_stage': old_stage,
                'new_stage': self.current_stage,
                'type': 'regression'
            })
    
    def _apply_stage_parameters(self, env, stage: CurriculumStage):
        """Apply stage parameters to the environment."""
        try:
            for param_name, param_value in stage.parameters.items():
                if hasattr(env, param_name):
                    setattr(env, param_name, param_value)
                    logger.debug(f"Set {param_name} = {param_value}")
                elif hasattr(env, 'config'):
                    env.config[param_name] = param_value
                    logger.debug(f"Set config[{param_name}] = {param_value}")
                else:
                    logger.warning(f"Cannot set parameter {param_name} on environment")
            
            # Reinitialize environment if needed
            if hasattr(env, 'reinitialize'):
                env.reinitialize()
            elif hasattr(env, '_initialize_grid'):
                env._initialize_grid()
                
        except Exception as e:
            logger.error(f"Failed to apply stage parameters: {e}")
    
    def set_custom_metric(self, metric_fn: Callable[[Dict[str, Any]], float]):
        """Set custom metric function for curriculum evaluation."""
        self.custom_metric_fn = metric_fn
        self.metric = CurriculumMetric.CUSTOM
        logger.info("Custom metric function set")
    
    def get_current_stage(self) -> CurriculumStage:
        """Get current curriculum stage."""
        return self.stages[self.current_stage]
    
    def get_stage_info(self) -> Dict[str, Any]:
        """Get information about current stage."""
        current_stage = self.stages[self.current_stage]
        recent_performance = (np.mean(self.performance_history[-self.evaluation_window:]) 
                            if self.performance_history else 0.0)
        
        return {
            'current_stage': self.current_stage,
            'stage_name': current_stage.name,
            'episodes_in_stage': self.episodes_in_stage,
            'stage_parameters': current_stage.parameters,
            'success_threshold': current_stage.success_threshold,
            'recent_performance': recent_performance,
            'progression_count': self.progression_count,
            'regression_count': self.regression_count,
            'total_episodes': self.total_episodes
        }
    
    def get_curriculum_history(self) -> List[Dict[str, Any]]:
        """Get history of curriculum changes."""
        return self.stage_history.copy()
    
    def reset_curriculum(self):
        """Reset curriculum to initial stage."""
        self.current_stage = 0
        self.episodes_in_stage = 0
        self.performance_history.clear()
        self.stage_history.clear()
        self.total_episodes = 0
        self.progression_count = 0
        self.regression_count = 0
        
        logger.info("Curriculum reset to initial stage")
    
    def force_stage(self, stage_id: int, env):
        """Force curriculum to specific stage."""
        if 0 <= stage_id < len(self.stages):
            old_stage = self.current_stage
            self.current_stage = stage_id
            self.episodes_in_stage = 0
            
            # Apply stage parameters
            self._apply_stage_parameters(env, self.stages[stage_id])
            
            logger.info(f"Curriculum forced to stage {stage_id}: {self.stages[stage_id].name}")
            
            # Record forced change
            self.stage_history.append({
                'episode': self.total_episodes,
                'old_stage': old_stage,
                'new_stage': stage_id,
                'type': 'forced'
            })
        else:
            logger.error(f"Invalid stage ID: {stage_id}")
    
    def is_curriculum_complete(self) -> bool:
        """Check if curriculum has reached the final stage."""
        return self.current_stage == len(self.stages) - 1
    
    def get_progress_ratio(self) -> float:
        """Get curriculum progress as ratio (0.0 to 1.0)."""
        if len(self.stages) <= 1:
            return 1.0
        return self.current_stage / (len(self.stages) - 1)


class AdaptiveCurriculumScheduler(CurriculumScheduler):
    """
    Advanced curriculum scheduler that adapts stage parameters dynamically.
    
    Instead of fixed stages, this scheduler continuously adjusts difficulty
    based on performance trends.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        
        # Adaptive parameters
        self.adaptation_rate = config.get("adaptation_rate", 0.1)
        self.parameter_ranges = config.get("parameter_ranges", {})
        self.current_parameters = config.get("initial_parameters", {})
        
        logger.info("AdaptiveCurriculumScheduler initialized")
    
    def update(self, env, episode_metrics: Dict[str, Any]) -> bool:
        """Update curriculum with adaptive parameter adjustment."""
        self.total_episodes += 1
        
        # Calculate performance
        performance = self._calculate_performance(episode_metrics)
        self.performance_history.append(performance)
        
        # Keep recent history
        if len(self.performance_history) > self.evaluation_window:
            self.performance_history.pop(0)
        
        # Adapt parameters if we have enough history
        if len(self.performance_history) >= self.evaluation_window:
            return self._adapt_parameters(env)
        
        return False
    
    def _adapt_parameters(self, env) -> bool:
        """Adapt environment parameters based on performance."""
        recent_performance = np.mean(self.performance_history[-self.evaluation_window:])
        performance_trend = self._calculate_performance_trend()
        
        parameters_changed = False
        
        for param_name, param_range in self.parameter_ranges.items():
            min_val, max_val = param_range
            current_val = self.current_parameters.get(param_name, min_val)
            
            # Increase difficulty if performance is good and trending up
            if recent_performance > self.progression_threshold and performance_trend > 0:
                new_val = min(current_val + self.adaptation_rate * (max_val - min_val), max_val)
            
            # Decrease difficulty if performance is poor
            elif recent_performance < self.regression_threshold:
                new_val = max(current_val - self.adaptation_rate * (max_val - min_val), min_val)
            
            else:
                new_val = current_val
            
            # Apply change if significant
            if abs(new_val - current_val) > 0.01:
                self.current_parameters[param_name] = new_val
                
                # Apply to environment
                if hasattr(env, param_name):
                    setattr(env, param_name, new_val)
                elif hasattr(env, 'config'):
                    env.config[param_name] = new_val
                
                parameters_changed = True
                logger.debug(f"Adapted {param_name}: {current_val:.3f} -> {new_val:.3f}")
        
        if parameters_changed:
            # Reinitialize environment if needed
            if hasattr(env, 'reinitialize'):
                env.reinitialize()
            elif hasattr(env, '_initialize_grid'):
                env._initialize_grid()
        
        return parameters_changed
    
    def _calculate_performance_trend(self) -> float:
        """Calculate performance trend over recent episodes."""
        if len(self.performance_history) < 20:
            return 0.0
        
        # Simple linear trend calculation
        recent = self.performance_history[-10:]
        older = self.performance_history[-20:-10]
        
        return np.mean(recent) - np.mean(older)