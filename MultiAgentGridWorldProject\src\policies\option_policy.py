"""
Option Policy (π^H)

High-level policy that selects discrete options/skills every K steps.
Implements differentiable option selection with Gumbel-Softmax for
gradient flow and research-grade flexibility.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Categorical
from typing import Dict, Tuple, Optional, Any
import numpy as np
import logging

logger = logging.getLogger(__name__)


class OptionPolicy(nn.Module):
    """
    High-level option selection policy π^H.
    
    Selects discrete options from current state every K steps,
    supporting both discrete sampling and differentiable Gumbel-Softmax.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.obs_dim = config.get("obs_dim", 128)
        self.num_options = config.get("num_options", 6)
        self.hidden_dim = config.get("hidden_dim", 256)
        self.num_layers = config.get("num_layers", 3)
        self.activation = config.get("activation", "relu")
        
        # Option selection parameters
        self.use_gumbel_softmax = config.get("use_gumbel_softmax", True)
        self.gumbel_temperature = config.get("gumbel_temperature", 1.0)
        self.temperature_decay = config.get("temperature_decay", 0.999)
        self.min_temperature = config.get("min_temperature", 0.1)
        
        # Exploration parameters
        self.epsilon_start = config.get("epsilon_start", 1.0)
        self.epsilon_end = config.get("epsilon_end", 0.05)
        self.epsilon_decay = config.get("epsilon_decay", 0.995)
        self.current_epsilon = self.epsilon_start
        
        # Option embeddings
        self.use_option_embeddings = config.get("use_option_embeddings", True)
        self.option_embed_dim = config.get("option_embed_dim", 32)
        
        # Network architecture
        self._build_network()
        
        # Training state
        self.training_step = 0
        
        logger.info(f"OptionPolicy initialized: {self.num_options} options, "
                   f"{self.hidden_dim}D hidden, Gumbel-Softmax: {self.use_gumbel_softmax}")
    
    def _build_network(self):
        """Build the option selection network."""
        
        # Activation function
        if self.activation == "relu":
            activation_fn = nn.ReLU
        elif self.activation == "tanh":
            activation_fn = nn.Tanh
        elif self.activation == "gelu":
            activation_fn = nn.GELU
        else:
            activation_fn = nn.ReLU
        
        # Input processing layers
        layers = []
        input_dim = self.obs_dim
        
        for i in range(self.num_layers):
            layers.extend([
                nn.Linear(input_dim, self.hidden_dim),
                activation_fn(),
                nn.LayerNorm(self.hidden_dim) if self.config.get("use_layer_norm", True) else nn.Identity()
            ])
            input_dim = self.hidden_dim
        
        self.feature_extractor = nn.Sequential(*layers)
        
        # Option selection head
        self.option_head = nn.Linear(self.hidden_dim, self.num_options)
        
        # Option embeddings for conditioning
        if self.use_option_embeddings:
            self.option_embeddings = nn.Embedding(self.num_options, self.option_embed_dim)
        
        # Value head for option-level value estimation
        self.option_value_head = nn.Linear(self.hidden_dim, self.num_options)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                if self.config.get("use_orthogonal_init", True):
                    nn.init.orthogonal_(module.weight)
                else:
                    nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0.0, 0.1)
    
    def forward(self, observations: torch.Tensor, 
                deterministic: bool = False,
                return_features: bool = False) -> Dict[str, torch.Tensor]:
        """
        Forward pass for option selection.
        
        Args:
            observations: Batch of observations [batch_size, obs_dim]
            deterministic: Whether to use deterministic selection
            return_features: Whether to return intermediate features
            
        Returns:
            Dictionary containing:
                - option_logits: Raw option logits [batch_size, num_options]
                - option_probs: Option probabilities [batch_size, num_options]
                - selected_options: Selected option indices [batch_size]
                - option_log_probs: Log probabilities of selected options [batch_size]
                - option_values: Value estimates for each option [batch_size, num_options]
                - features: Feature representations (if requested)
        """
        batch_size = observations.shape[0]
        
        # Extract features
        features = self.feature_extractor(observations)
        
        # Option selection logits
        option_logits = self.option_head(features)
        option_probs = F.softmax(option_logits, dim=-1)
        
        # Option values
        option_values = self.option_value_head(features)
        
        # Option selection
        if deterministic:
            # Deterministic selection (argmax)
            selected_options = torch.argmax(option_logits, dim=-1)
            option_log_probs = F.log_softmax(option_logits, dim=-1)
            selected_log_probs = option_log_probs.gather(1, selected_options.unsqueeze(1)).squeeze(1)
        else:
            if self.use_gumbel_softmax and self.training:
                # Gumbel-Softmax for differentiable sampling
                gumbel_logits = self._gumbel_softmax(option_logits, self.gumbel_temperature)
                selected_options = torch.argmax(gumbel_logits, dim=-1)
                option_log_probs = F.log_softmax(option_logits, dim=-1)
                selected_log_probs = option_log_probs.gather(1, selected_options.unsqueeze(1)).squeeze(1)
            else:
                # Standard categorical sampling
                if self.training and np.random.random() < self.current_epsilon:
                    # Epsilon-greedy exploration
                    selected_options = torch.randint(0, self.num_options, (batch_size,), 
                                                   device=observations.device)
                    option_log_probs = F.log_softmax(option_logits, dim=-1)
                    selected_log_probs = option_log_probs.gather(1, selected_options.unsqueeze(1)).squeeze(1)
                else:
                    # Sample from categorical distribution
                    dist = Categorical(logits=option_logits)
                    selected_options = dist.sample()
                    selected_log_probs = dist.log_prob(selected_options)
                    option_log_probs = F.log_softmax(option_logits, dim=-1)
        
        result = {
            "option_logits": option_logits,
            "option_probs": option_probs,
            "selected_options": selected_options,
            "option_log_probs": selected_log_probs,
            "option_values": option_values,
            "entropy": -torch.sum(option_probs * option_log_probs, dim=-1)
        }
        
        if return_features:
            result["features"] = features
        
        return result
    
    def _gumbel_softmax(self, logits: torch.Tensor, temperature: float) -> torch.Tensor:
        """Apply Gumbel-Softmax trick for differentiable sampling."""
        gumbel_noise = -torch.log(-torch.log(torch.rand_like(logits) + 1e-8) + 1e-8)
        gumbel_logits = (logits + gumbel_noise) / temperature
        return F.softmax(gumbel_logits, dim=-1)
    
    def get_option_embedding(self, option_indices: torch.Tensor) -> torch.Tensor:
        """Get option embeddings for conditioning."""
        if self.use_option_embeddings:
            return self.option_embeddings(option_indices)
        else:
            # One-hot encoding
            return F.one_hot(option_indices, self.num_options).float()
    
    def update_exploration(self):
        """Update exploration parameters."""
        self.current_epsilon = max(
            self.epsilon_end,
            self.current_epsilon * self.epsilon_decay
        )
        
        self.gumbel_temperature = max(
            self.min_temperature,
            self.gumbel_temperature * self.temperature_decay
        )
        
        self.training_step += 1
    
    def get_option_distribution(self, observations: torch.Tensor) -> Categorical:
        """Get categorical distribution over options."""
        with torch.no_grad():
            result = self.forward(observations, deterministic=False)
            return Categorical(logits=result["option_logits"])
    
    def compute_option_entropy(self, observations: torch.Tensor) -> torch.Tensor:
        """Compute entropy of option distribution."""
        with torch.no_grad():
            result = self.forward(observations, deterministic=False)
            return result["entropy"]
    
    def get_training_info(self) -> Dict[str, float]:
        """Get training information for logging."""
        return {
            "epsilon": self.current_epsilon,
            "gumbel_temperature": self.gumbel_temperature,
            "training_step": self.training_step
        }


class MultiAgentOptionPolicy(nn.Module):
    """
    Multi-agent wrapper for option policies.
    
    Supports both parameter sharing and individual policies per agent.
    """
    
    def __init__(self, config: Dict[str, Any], num_agents: int):
        super().__init__()
        
        self.config = config
        self.num_agents = num_agents
        self.parameter_sharing = config.get("parameter_sharing", False)
        
        if self.parameter_sharing:
            # Shared policy for all agents
            self.shared_policy = OptionPolicy(config)
            logger.info(f"MultiAgentOptionPolicy: Parameter sharing enabled")
        else:
            # Individual policies per agent
            self.agent_policies = nn.ModuleDict({
                f"agent_{i}": OptionPolicy(config) 
                for i in range(num_agents)
            })
            logger.info(f"MultiAgentOptionPolicy: Individual policies for {num_agents} agents")
    
    def forward(self, observations: Dict[str, torch.Tensor], 
                agent_ids: Optional[list] = None,
                deterministic: bool = False) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Forward pass for multiple agents.
        
        Args:
            observations: Dict mapping agent_id to observations
            agent_ids: List of agent IDs to process (None for all)
            deterministic: Whether to use deterministic selection
            
        Returns:
            Dict mapping agent_id to option selection results
        """
        if agent_ids is None:
            agent_ids = list(observations.keys())
        
        results = {}
        
        for agent_id in agent_ids:
            if agent_id not in observations:
                continue
                
            obs = observations[agent_id]
            
            if self.parameter_sharing:
                result = self.shared_policy(obs, deterministic=deterministic)
            else:
                if agent_id in self.agent_policies:
                    result = self.agent_policies[agent_id](obs, deterministic=deterministic)
                else:
                    # Fallback to first agent's policy
                    first_agent = list(self.agent_policies.keys())[0]
                    result = self.agent_policies[first_agent](obs, deterministic=deterministic)
            
            results[agent_id] = result
        
        return results
    
    def update_exploration(self):
        """Update exploration for all policies."""
        if self.parameter_sharing:
            self.shared_policy.update_exploration()
        else:
            for policy in self.agent_policies.values():
                policy.update_exploration()
    
    def get_training_info(self) -> Dict[str, Any]:
        """Get training information for all policies."""
        if self.parameter_sharing:
            return {"shared": self.shared_policy.get_training_info()}
        else:
            return {
                agent_id: policy.get_training_info()
                for agent_id, policy in self.agent_policies.items()
            }