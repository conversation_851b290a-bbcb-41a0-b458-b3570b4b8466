"""
Worker Policy (π^L_z)

Low-level policy that executes primitive actions conditioned on selected options.
Each option can have its own specialized worker or share parameters across options.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Categorical, Normal
from typing import Dict, Tuple, Optional, Any, Union
import numpy as np
import logging

logger = logging.getLogger(__name__)


class WorkerPolicy(nn.Module):
    """
    Low-level worker policy π^L_z.
    
    Executes primitive actions conditioned on the selected option z.
    Supports both discrete and continuous action spaces.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.obs_dim = config.get("obs_dim", 128)
        self.action_dim = config.get("action_dim", 5)  # GridWorld: up, down, left, right, stay
        self.num_options = config.get("num_options", 6)
        self.hidden_dim = config.get("hidden_dim", 256)
        self.num_layers = config.get("num_layers", 2)
        self.activation = config.get("activation", "relu")
        
        # Action space configuration
        self.action_space_type = config.get("action_space_type", "discrete")
        self.action_bound = config.get("action_bound", 1.0)  # For continuous actions
        
        # Option conditioning
        self.option_conditioning = config.get("option_conditioning", True)
        self.option_embed_dim = config.get("option_embed_dim", 32)
        self.separate_workers = config.get("separate_workers", False)
        
        # Network architecture
        self._build_network()
        
        logger.info(f"WorkerPolicy initialized: {self.action_space_type} actions, "
                   f"option conditioning: {self.option_conditioning}, "
                   f"separate workers: {self.separate_workers}")
    
    def _build_network(self):
        """Build the worker policy network."""
        
        # Activation function
        if self.activation == "relu":
            activation_fn = nn.ReLU
        elif self.activation == "tanh":
            activation_fn = nn.Tanh
        elif self.activation == "gelu":
            activation_fn = nn.GELU
        else:
            activation_fn = nn.ReLU
        
        # Input dimension calculation
        input_dim = self.obs_dim
        if self.option_conditioning:
            input_dim += self.option_embed_dim
        
        if self.separate_workers:
            # Separate worker networks for each option
            self.worker_networks = nn.ModuleDict()
            self.worker_feature_extractors = nn.ModuleDict()
            
            for option_id in range(self.num_options):
                # Feature extractor
                feature_layers = []
                current_dim = input_dim
                
                for i in range(self.num_layers):
                    feature_layers.extend([
                        nn.Linear(current_dim, self.hidden_dim),
                        activation_fn(),
                        nn.LayerNorm(self.hidden_dim) if self.config.get("use_layer_norm", True) else nn.Identity()
                    ])
                    current_dim = self.hidden_dim
                
                self.worker_feature_extractors[f"option_{option_id}"] = nn.Sequential(*feature_layers)
                
                # Action head
                if self.action_space_type == "discrete":
                    action_head = nn.Linear(self.hidden_dim, self.action_dim)
                else:  # continuous
                    action_head = nn.Linear(self.hidden_dim, self.action_dim * 2)
                
                self.worker_networks[f"option_{option_id}"] = action_head
        else:
            # Shared worker network for all options
            layers = []
            current_dim = input_dim
            
            # Hidden layers
            for i in range(self.num_layers):
                layers.extend([
                    nn.Linear(current_dim, self.hidden_dim),
                    activation_fn(),
                    nn.LayerNorm(self.hidden_dim) if self.config.get("use_layer_norm", True) else nn.Identity()
                ])
                current_dim = self.hidden_dim
            
            self.shared_feature_extractor = nn.Sequential(*layers)
            
            # Action heads
            if self.action_space_type == "discrete":
                self.action_head = nn.Linear(self.hidden_dim, self.action_dim)
            else:  # continuous
                self.action_mean_head = nn.Linear(self.hidden_dim, self.action_dim)
                self.action_logstd_head = nn.Linear(self.hidden_dim, self.action_dim)
        
        # Option embeddings
        if self.option_conditioning:
            self.option_embeddings = nn.Embedding(self.num_options, self.option_embed_dim)
        
        # Value head for worker-level value estimation
        self.value_head = nn.Linear(self.hidden_dim, 1)
        
        # Initialize weights
        self._initialize_weights()
    
    def _initialize_weights(self):
        """Initialize network weights."""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                if self.config.get("use_orthogonal_init", True):
                    nn.init.orthogonal_(module.weight)
                else:
                    nn.init.xavier_uniform_(module.weight)
                nn.init.constant_(module.bias, 0.0)
            elif isinstance(module, nn.Embedding):
                nn.init.normal_(module.weight, 0.0, 0.1)
    
    def forward(self, observations: torch.Tensor,
                options: torch.Tensor,
                deterministic: bool = False,
                return_features: bool = False) -> Dict[str, torch.Tensor]:
        """
        Forward pass for action selection.
        
        Args:
            observations: Batch of observations [batch_size, obs_dim]
            options: Selected options [batch_size]
            deterministic: Whether to use deterministic action selection
            return_features: Whether to return intermediate features
            
        Returns:
            Dictionary containing:
                - actions: Selected actions [batch_size, action_dim]
                - action_log_probs: Log probabilities of actions [batch_size]
                - action_probs: Action probabilities (discrete) or None (continuous)
                - values: State values [batch_size]
                - features: Feature representations (if requested)
        """
        batch_size = observations.shape[0]
        
        # Prepare input
        if self.option_conditioning:
            option_embeds = self.option_embeddings(options)
            network_input = torch.cat([observations, option_embeds], dim=-1)
        else:
            network_input = observations
        
        if self.separate_workers:
            # Use separate networks for each option
            actions_list = []
            log_probs_list = []
            values_list = []
            features_list = []
            
            for i in range(batch_size):
                option_id = options[i].item()
                single_input = network_input[i:i+1]
                
                # Extract features and get network output for this option
                features = self.worker_feature_extractors[f"option_{option_id}"](single_input)
                network_output = self.worker_networks[f"option_{option_id}"](features)
                
                if self.action_space_type == "discrete":
                    action_logits = network_output
                    action_probs = F.softmax(action_logits, dim=-1)
                    
                    if deterministic:
                        action = torch.argmax(action_logits, dim=-1)
                        log_prob = F.log_softmax(action_logits, dim=-1).gather(1, action.unsqueeze(1)).squeeze(1)
                    else:
                        dist = Categorical(logits=action_logits)
                        action = dist.sample()
                        log_prob = dist.log_prob(action)
                    
                    actions_list.append(action)
                    log_probs_list.append(log_prob)
                else:  # continuous
                    mean_logstd = network_output
                    mean = mean_logstd[:, :self.action_dim]
                    log_std = mean_logstd[:, self.action_dim:]
                    log_std = torch.clamp(log_std, -20, 2)
                    std = torch.exp(log_std)
                    
                    if deterministic:
                        action = mean
                        log_prob = torch.zeros(1, device=mean.device)
                    else:
                        dist = Normal(mean, std)
                        action = dist.sample()
                        log_prob = dist.log_prob(action).sum(dim=-1)
                        
                        # Apply action bounds
                        action = torch.tanh(action) * self.action_bound
                    
                    actions_list.append(action)
                    log_probs_list.append(log_prob)
                
                # Compute value using the extracted features
                value = self.value_head(features)
                values_list.append(value)
                features_list.append(features)
            
            actions = torch.cat(actions_list, dim=0)
            action_log_probs = torch.cat(log_probs_list, dim=0)
            values = torch.cat(values_list, dim=0).squeeze(-1)
            features = torch.cat(features_list, dim=0) if return_features else None
            
        else:
            # Use shared network
            features = self.shared_feature_extractor(network_input)
            
            if self.action_space_type == "discrete":
                action_logits = self.action_head(features)
                action_probs = F.softmax(action_logits, dim=-1)
                
                if deterministic:
                    actions = torch.argmax(action_logits, dim=-1)
                    action_log_probs = F.log_softmax(action_logits, dim=-1).gather(1, actions.unsqueeze(1)).squeeze(1)
                else:
                    dist = Categorical(logits=action_logits)
                    actions = dist.sample()
                    action_log_probs = dist.log_prob(actions)
            else:  # continuous
                action_mean = self.action_mean_head(features)
                action_log_std = self.action_logstd_head(features)
                action_log_std = torch.clamp(action_log_std, -20, 2)
                action_std = torch.exp(action_log_std)
                
                if deterministic:
                    actions = action_mean
                    action_log_probs = torch.zeros(batch_size, device=action_mean.device)
                else:
                    dist = Normal(action_mean, action_std)
                    actions = dist.sample()
                    action_log_probs = dist.log_prob(actions).sum(dim=-1)
                    
                    # Apply action bounds
                    actions = torch.tanh(actions) * self.action_bound
            
            # Compute values
            values = self.value_head(features).squeeze(-1)
        
        result = {
            "actions": actions,
            "action_log_probs": action_log_probs,
            "values": values
        }
        
        if self.action_space_type == "discrete":
            result["action_probs"] = action_probs if not self.separate_workers else None
        
        if return_features:
            result["features"] = features
        
        return result
    
    def get_action_distribution(self, observations: torch.Tensor, 
                              options: torch.Tensor) -> Union[Categorical, Normal]:
        """Get action distribution for given observations and options."""
        with torch.no_grad():
            if self.option_conditioning:
                option_embeds = self.option_embeddings(options)
                network_input = torch.cat([observations, option_embeds], dim=-1)
            else:
                network_input = observations
            
            if self.separate_workers:
                # For simplicity, return distribution for first batch element
                option_id = options[0].item()
                network_output = self.worker_networks[f"option_{option_id}"](network_input[:1])
            else:
                features = self.shared_feature_extractor(network_input)
                if self.action_space_type == "discrete":
                    network_output = self.action_head(features)
                else:
                    action_mean = self.action_mean_head(features)
                    action_log_std = self.action_logstd_head(features)
                    action_std = torch.exp(torch.clamp(action_log_std, -20, 2))
                    return Normal(action_mean, action_std)
            
            if self.action_space_type == "discrete":
                return Categorical(logits=network_output)
            else:
                mean_logstd = network_output
                mean = mean_logstd[:, :self.action_dim]
                log_std = mean_logstd[:, self.action_dim:]
                std = torch.exp(torch.clamp(log_std, -20, 2))
                return Normal(mean, std)


class MultiAgentWorkerPolicy(nn.Module):
    """
    Multi-agent wrapper for worker policies.
    
    Supports both parameter sharing and individual policies per agent.
    """
    
    def __init__(self, config: Dict[str, Any], num_agents: int):
        super().__init__()
        
        self.config = config
        self.num_agents = num_agents
        self.parameter_sharing = config.get("parameter_sharing", False)
        
        if self.parameter_sharing:
            # Shared policy for all agents
            self.shared_policy = WorkerPolicy(config)
            logger.info(f"MultiAgentWorkerPolicy: Parameter sharing enabled")
        else:
            # Individual policies per agent
            self.agent_policies = nn.ModuleDict({
                f"agent_{i}": WorkerPolicy(config) 
                for i in range(num_agents)
            })
            logger.info(f"MultiAgentWorkerPolicy: Individual policies for {num_agents} agents")
    
    def forward(self, observations: Dict[str, torch.Tensor],
                options: Dict[str, torch.Tensor],
                agent_ids: Optional[list] = None,
                deterministic: bool = False) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Forward pass for multiple agents.
        
        Args:
            observations: Dict mapping agent_id to observations
            options: Dict mapping agent_id to selected options
            agent_ids: List of agent IDs to process (None for all)
            deterministic: Whether to use deterministic action selection
            
        Returns:
            Dict mapping agent_id to action selection results
        """
        if agent_ids is None:
            agent_ids = list(observations.keys())
        
        results = {}
        
        for agent_id in agent_ids:
            if agent_id not in observations or agent_id not in options:
                continue
                
            obs = observations[agent_id]
            opt = options[agent_id]
            
            if self.parameter_sharing:
                result = self.shared_policy(obs, opt, deterministic=deterministic)
            else:
                if agent_id in self.agent_policies:
                    result = self.agent_policies[agent_id](obs, opt, deterministic=deterministic)
                else:
                    # Fallback to first agent's policy
                    first_agent = list(self.agent_policies.keys())[0]
                    result = self.agent_policies[first_agent](obs, opt, deterministic=deterministic)
            
            results[agent_id] = result
        
        return results