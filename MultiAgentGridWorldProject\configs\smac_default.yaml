defaults:
  - env: env_smac
  - agent: agent_hrl_icp
  - training: training_defaults
  - _self_

# Experiment identification
experiment_name: "smac_hrl"
experiment_group: "starcraft"
tags: ["hierarchical", "smac", "starcraft", "multi_agent"]

# Environment settings
multi_agent: true
num_agents: 5  # Depends on SMAC scenario
seed: 42

# Results and logging
results_dir: "results"
log_level: "INFO"

# Hardware configuration
device: "auto"
mixed_precision: true  # SMAC benefits from mixed precision
num_workers: 4

# Reproducibility
deterministic: false

# Hydra configuration
hydra:
  run:
    dir: ./outputs/${experiment_name}/${now:%Y-%m-%d_%H-%M-%S}
  job:
    chdir: false
  sweep:
    dir: ./multirun/${experiment_name}
    subdir: ${hydra:job.num}