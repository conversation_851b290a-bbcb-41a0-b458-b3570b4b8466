"""
Environment Smoke Tests

Comprehensive validation of environment components without training.
Tests basic functionality, wrappers, dynamic events, and config integration.
"""

import pytest
import numpy as np
from typing import Dict, Any
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.environment import (
    GridWorldEnv, 
    EventScheduler, 
    MovingWallEvent, 
    HazardPulseEvent, 
    GoalRelocationEvent,
    TimeLimitWrapper,
    FailureInjectionWrapper,
    RewardClipWrapper,
    ObsNormalizationWrapper,
    MaskInvalidActionsWrapper,
    CurriculumScheduler,
    suppress_environment_warnings
)

# Suppress warnings for cleaner test output
suppress_environment_warnings()


class TestGridWorldEnvironment:
    """Test GridWorld environment basic functionality."""
    
    @pytest.fixture
    def basic_config(self):
        """Basic configuration for GridWorld."""
        return {
            "map_size": [10, 10],
            "num_agents": 4,
            "obstacles": 5,
            "goal_locations": [[1, 1], [8, 8]],
            "episode_limit": 200,
            "observation_type": "partial",
            "observation_radius": 3,
            "seed": 42
        }
    
    @pytest.fixture
    def env(self, basic_config):
        """Create GridWorld environment."""
        return GridWorldEnv(basic_config)
    
    def test_basic_instantiation(self, env):
        """Test 1: Basic Environment Instantiation."""
        assert env is not None
        assert env.num_agents == 4
        assert env.map_size == [10, 10]
        assert len(env.agent_ids) == 4
        assert env.action_space is not None
        assert env.observation_space is not None
        print("✅ Basic instantiation test passed")
    
    def test_reset_functionality(self, env):
        """Test environment reset returns valid observations and state."""
        obs, info = env.reset()
        
        # Check observations
        assert isinstance(obs, dict)
        assert len(obs) == env.num_agents
        
        for agent_id in env.agent_ids:
            assert agent_id in obs
            assert isinstance(obs[agent_id], np.ndarray)
            assert obs[agent_id].shape == env.observation_space.shape
        
        # Check state
        state = env.get_state()
        assert isinstance(state, np.ndarray)
        assert state.shape[0] > 0
        
        # Check info
        assert isinstance(info, dict)
        assert "step" in info
        assert info["step"] == 0
        
        print("✅ Reset functionality test passed")
    
    def test_step_functionality(self, env):
        """Test 2: Step Function Test."""
        obs, info = env.reset()
        
        # Create dummy actions (0=up, 1=down, 2=left, 3=right, 4=stay)
        dummy_actions = {
            "agent_0": 0,  # up
            "agent_1": 1,  # down
            "agent_2": 2,  # left
            "agent_3": 3   # right
        }
        
        next_obs, rewards, dones, step_info = env.step(dummy_actions)
        
        # Check observations
        assert isinstance(next_obs, dict)
        assert len(next_obs) == env.num_agents
        
        # Check rewards
        assert isinstance(rewards, dict)
        assert len(rewards) == env.num_agents
        for reward in rewards.values():
            assert isinstance(reward, (int, float))
        
        # Check dones
        assert isinstance(dones, dict)
        assert len(dones) == env.num_agents
        for done in dones.values():
            assert isinstance(done, bool)
        
        # Check info
        assert isinstance(step_info, dict)
        
        print("✅ Step functionality test passed")
    
    def test_multiple_steps(self, env):
        """Test multiple environment steps."""
        obs, info = env.reset()
        
        for step in range(10):
            actions = {}
            for agent_id in env.agent_ids:
                actions[agent_id] = np.random.randint(0, 5)  # Random action
            
            obs, rewards, dones, info = env.step(actions)
            
            # Environment should maintain consistency
            assert len(obs) == env.num_agents
            assert len(rewards) == env.num_agents
            assert len(dones) == env.num_agents
            
            if any(dones.values()):
                break
        
        print("✅ Multiple steps test passed")
    
    def test_different_modes(self, basic_config):
        """Test different GridWorld modes."""
        modes = ["static", "multi_goal", "interactive"]
        
        for mode in modes:
            config = basic_config.copy()
            config["mode"] = mode
            
            env = GridWorldEnv(config)
            obs, info = env.reset()
            
            # Basic functionality should work in all modes
            actions = {f"agent_{i}": 4 for i in range(env.num_agents)}  # Stay action
            obs, rewards, dones, info = env.step(actions)
            
            assert len(obs) == env.num_agents
            assert len(rewards) == env.num_agents
            
            env.close()
        
        print("✅ Different modes test passed")


class TestEnvironmentWrappers:
    """Test environment wrappers functionality."""
    
    @pytest.fixture
    def base_env(self):
        """Create base environment for wrapping."""
        config = {
            "map_size": [8, 8],
            "num_agents": 3,
            "obstacles": 3,
            "episode_limit": 100,
            "seed": 42
        }
        return GridWorldEnv(config)
    
    def test_time_limit_wrapper(self, base_env):
        """Test 3: Wrappers Apply Cleanly - TimeLimitWrapper."""
        max_steps = 50
        wrapped_env = TimeLimitWrapper(base_env, max_episode_steps=max_steps)
        
        obs, info = wrapped_env.reset()
        assert "time_limit" in info
        assert info["time_limit"] == max_steps
        
        # Run until time limit
        for step in range(max_steps + 10):
            actions = {f"agent_{i}": 4 for i in range(wrapped_env.num_agents)}  # Stay
            obs, rewards, dones, info = wrapped_env.step(actions)
            
            if step >= max_steps - 1:
                assert all(dones.values()), f"Should be done at step {step}"
                assert info.get("time_limit_reached", False)
                break
        
        wrapped_env.close()
        print("✅ TimeLimitWrapper test passed")
    
    def test_failure_injection_wrapper(self, base_env):
        """Test FailureInjectionWrapper."""
        wrapped_env = FailureInjectionWrapper(
            base_env, 
            failure_probability=0.5,  # High probability for testing
            recovery_probability=0.3,
            max_simultaneous_failures=2
        )
        
        obs, info = wrapped_env.reset()
        assert "failed_agents" in info
        
        # Run several steps to potentially trigger failures
        for step in range(20):
            actions = {f"agent_{i}": 4 for i in range(wrapped_env.num_agents)}
            obs, rewards, dones, info = wrapped_env.step(actions)
            
            assert "failed_agents" in info
            assert "failure_count" in info
            assert isinstance(info["failed_agents"], list)
        
        wrapped_env.close()
        print("✅ FailureInjectionWrapper test passed")
    
    def test_reward_clip_wrapper(self, base_env):
        """Test RewardClipWrapper."""
        wrapped_env = RewardClipWrapper(base_env, min_reward=-5.0, max_reward=5.0)
        
        obs, info = wrapped_env.reset()
        actions = {f"agent_{i}": 4 for i in range(wrapped_env.num_agents)}
        obs, rewards, dones, info = wrapped_env.step(actions)
        
        # Check rewards are within bounds
        for reward in rewards.values():
            assert -5.0 <= reward <= 5.0
        
        wrapped_env.close()
        print("✅ RewardClipWrapper test passed")
    
    def test_obs_normalization_wrapper(self, base_env):
        """Test ObsNormalizationWrapper."""
        wrapped_env = ObsNormalizationWrapper(base_env)
        
        obs, info = wrapped_env.reset()
        
        # Run several steps to build statistics
        for step in range(10):
            actions = {f"agent_{i}": np.random.randint(0, 5) for i in range(wrapped_env.num_agents)}
            obs, rewards, dones, info = wrapped_env.step(actions)
            
            # Check observations are normalized (should have reasonable values)
            for agent_obs in obs.values():
                assert not np.any(np.isnan(agent_obs))
                assert not np.any(np.isinf(agent_obs))
        
        wrapped_env.close()
        print("✅ ObsNormalizationWrapper test passed")
    
    def test_multiple_wrappers(self, base_env):
        """Test multiple wrappers applied together."""
        # Apply multiple wrappers
        wrapped_env = base_env
        wrapped_env = TimeLimitWrapper(wrapped_env, max_episode_steps=30)
        wrapped_env = RewardClipWrapper(wrapped_env, min_reward=-2.0, max_reward=2.0)
        wrapped_env = FailureInjectionWrapper(wrapped_env, failure_probability=0.1)
        
        obs, info = wrapped_env.reset()
        
        # Should work with all wrappers
        for step in range(35):  # More than time limit
            actions = {f"agent_{i}": 4 for i in range(wrapped_env.num_agents)}
            obs, rewards, dones, info = wrapped_env.step(actions)
            
            # Check reward clipping
            for reward in rewards.values():
                assert -2.0 <= reward <= 2.0
            
            if all(dones.values()):
                break
        
        wrapped_env.close()
        print("✅ Multiple wrappers test passed")


class TestDynamicEvents:
    """Test dynamic events system."""
    
    @pytest.fixture
    def env_with_events(self):
        """Create environment with event scheduler."""
        config = {
            "map_size": [10, 10],
            "num_agents": 3,
            "obstacles": 3,
            "episode_limit": 100,
            "seed": 42
        }
        return GridWorldEnv(config)
    
    def test_event_scheduler_creation(self):
        """Test EventScheduler instantiation."""
        scheduler = EventScheduler({})
        assert scheduler is not None
        assert len(scheduler.events) == 0
        print("✅ EventScheduler creation test passed")
    
    def test_moving_wall_event(self, env_with_events):
        """Test 4: Dynamic Events Trigger - MovingWallEvent."""
        event = MovingWallEvent(
            event_id="test_wall",
            wall_positions=[(5, 5), (5, 6)],
            movement_pattern="linear",
            speed=0.5,
            trigger_probability=1.0
        )
        
        scheduler = EventScheduler({})
        scheduler.add_event(event, trigger_timestep=5)
        
        env_with_events.reset()
        
        # Run simulation with events
        for timestep in range(20):
            scheduler.update(env_with_events, timestep)
            
            if timestep >= 5:
                assert event.is_active
            
            # Step environment
            actions = {f"agent_{i}": 4 for i in range(env_with_events.num_agents)}
            env_with_events.step(actions)
        
        print("✅ MovingWallEvent test passed")
    
    def test_hazard_pulse_event(self, env_with_events):
        """Test HazardPulseEvent."""
        event = HazardPulseEvent(
            event_id="test_hazard",
            hazard_zones=[(5, 5, 2)],  # x, y, radius
            pulse_frequency=5,
            damage=-1.0,
            trigger_probability=1.0
        )
        
        scheduler = EventScheduler({})
        scheduler.add_event(event, trigger_timestep=3)
        
        env_with_events.reset()
        
        for timestep in range(15):
            scheduler.update(env_with_events, timestep)
            
            if timestep >= 3:
                assert event.is_active
            
            actions = {f"agent_{i}": 4 for i in range(env_with_events.num_agents)}
            env_with_events.step(actions)
        
        print("✅ HazardPulseEvent test passed")
    
    def test_goal_relocation_event(self, env_with_events):
        """Test GoalRelocationEvent."""
        original_goals = env_with_events.goal_locations.copy()
        new_goals = [[7, 7], [2, 2]]
        
        event = GoalRelocationEvent(
            event_id="test_relocation",
            new_goal_positions=new_goals,
            trigger_probability=1.0
        )
        
        scheduler = EventScheduler({})
        scheduler.add_event(event, trigger_timestep=10)
        
        env_with_events.reset()
        
        for timestep in range(15):
            scheduler.update(env_with_events, timestep)
            
            if timestep == 10:
                # Goals should be relocated
                assert env_with_events.goal_locations == new_goals
            
            actions = {f"agent_{i}": 4 for i in range(env_with_events.num_agents)}
            env_with_events.step(actions)
        
        print("✅ GoalRelocationEvent test passed")


class TestCurriculumScheduler:
    """Test curriculum learning functionality."""
    
    def test_curriculum_creation(self):
        """Test CurriculumScheduler instantiation."""
        config = {
            "metric": "success_rate",
            "evaluation_window": 10,
            "progression_threshold": 0.8,
            "stages": [
                {
                    "name": "Easy",
                    "parameters": {"map_size": [6, 6], "obstacles": 2},
                    "success_threshold": 0.7,
                    "min_episodes": 5,
                    "max_episodes": 20
                },
                {
                    "name": "Hard", 
                    "parameters": {"map_size": [10, 10], "obstacles": 5},
                    "success_threshold": 0.8,
                    "min_episodes": 10,
                    "max_episodes": 30
                }
            ]
        }
        
        curriculum = CurriculumScheduler(config)
        assert curriculum is not None
        assert len(curriculum.stages) == 2
        assert curriculum.current_stage == 0
        print("✅ CurriculumScheduler creation test passed")
    
    def test_curriculum_progression(self):
        """Test curriculum stage progression."""
        config = {
            "metric": "success_rate",
            "evaluation_window": 5,
            "progression_threshold": 0.8,
            "stages": [
                {
                    "name": "Easy",
                    "parameters": {"obstacles": 2},
                    "success_threshold": 0.7,
                    "min_episodes": 3,
                    "max_episodes": 10
                },
                {
                    "name": "Hard",
                    "parameters": {"obstacles": 5},
                    "success_threshold": 0.8,
                    "min_episodes": 5,
                    "max_episodes": 15
                }
            ]
        }
        
        curriculum = CurriculumScheduler(config)
        env_config = {"map_size": [8, 8], "num_agents": 2, "obstacles": 2, "episode_limit": 50}
        env = GridWorldEnv(env_config)
        
        # Simulate high performance episodes
        for episode in range(10):
            episode_metrics = {"success_rate": 0.9}  # High success rate
            stage_changed = curriculum.update(env, episode_metrics)
            
            if episode >= 3 and curriculum.current_stage == 1:
                # Should have progressed to stage 1
                break
        
        assert curriculum.current_stage == 1
        print("✅ Curriculum progression test passed")


def test_environment_integration():
    """Integration test with all components."""
    # Create environment with full configuration
    config = {
        "map_size": [8, 8],
        "num_agents": 3,
        "obstacles": 4,
        "episode_limit": 50,
        "mode": "multi_goal",
        "goal_locations": [[1, 1], [6, 6]],
        "observation_type": "partial",
        "seed": 42
    }
    
    # Create environment
    env = GridWorldEnv(config)
    
    # Add wrappers
    env = TimeLimitWrapper(env, max_episode_steps=30)
    env = RewardClipWrapper(env, min_reward=-5.0, max_reward=5.0)
    
    # Add dynamic events
    scheduler = EventScheduler({})
    moving_wall = MovingWallEvent("wall_1", [(4, 4)], trigger_probability=1.0)
    scheduler.add_event(moving_wall, trigger_timestep=10)
    
    # Add curriculum
    curriculum_config = {
        "metric": "success_rate",
        "stages": [{"name": "Test", "parameters": {}, "success_threshold": 0.5, "min_episodes": 1, "max_episodes": 5}]
    }
    curriculum = CurriculumScheduler(curriculum_config)
    
    # Run integrated test
    obs, info = env.reset()
    
    for timestep in range(35):
        # Update events
        scheduler.update(env, timestep)
        
        # Take actions
        actions = {f"agent_{i}": np.random.randint(0, 5) for i in range(env.num_agents)}
        obs, rewards, dones, info = env.step(actions)
        
        # Update curriculum
        episode_metrics = {"success_rate": 0.6}
        curriculum.update(env, episode_metrics)
        
        if all(dones.values()):
            break
    
    env.close()
    print("✅ Full integration test passed")


if __name__ == "__main__":
    # Run tests manually if not using pytest
    print("Running Environment Validation Tests...")
    
    # Basic environment tests
    config = {
        "map_size": [10, 10],
        "num_agents": 4,
        "obstacles": 5,
        "goal_locations": [[1, 1], [8, 8]],
        "episode_limit": 200,
        "observation_type": "partial",
        "observation_radius": 3,
        "seed": 42
    }
    
    test_env = TestGridWorldEnvironment()
    env = GridWorldEnv(config)
    
    test_env.test_basic_instantiation(env)
    test_env.test_reset_functionality(env)
    test_env.test_step_functionality(env)
    test_env.test_multiple_steps(env)
    
    # Wrapper tests
    wrapper_test = TestEnvironmentWrappers()
    wrapper_test.test_time_limit_wrapper(env)
    
    # Event tests
    event_test = TestDynamicEvents()
    event_test.test_event_scheduler_creation()
    event_test.test_moving_wall_event(env)
    
    # Integration test
    test_environment_integration()
    
    print("\n🎉 All Environment Validation Tests Passed!")
    print("✅ Environment is ready for training integration")