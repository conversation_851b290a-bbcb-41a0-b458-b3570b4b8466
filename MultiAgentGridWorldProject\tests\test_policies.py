"""
Policy Module Tests

Comprehensive validation of hierarchical policy components including
option selection, worker execution, and termination functions.
"""

import pytest
import torch
import numpy as np
from typing import Dict, Any
import sys
import os

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.policies import (
    OptionPolicy,
    MultiAgentOptionPolicy,
    WorkerPolicy,
    MultiAgentWorkerPolicy,
    TerminationFunction,
    MultiAgentTerminationFunction,
    HierarchicalPolicy,
    MultiAgentHierarchicalPolicy
)


class TestOptionPolicy:
    """Test option selection policy π^H."""
    
    @pytest.fixture
    def config(self):
        """Basic configuration for option policy."""
        return {
            "obs_dim": 64,
            "num_options": 6,
            "hidden_dim": 128,
            "num_layers": 2,
            "use_gumbel_softmax": True,
            "use_option_embeddings": True,
            "option_embed_dim": 32
        }
    
    @pytest.fixture
    def policy(self, config):
        """Create option policy."""
        return OptionPolicy(config)
    
    def test_option_policy_creation(self, policy, config):
        """Test option policy instantiation."""
        assert policy is not None
        assert policy.num_options == config["num_options"]
        assert policy.hidden_dim == config["hidden_dim"]
        assert policy.use_gumbel_softmax == config["use_gumbel_softmax"]
        print("✅ OptionPolicy creation test passed")
    
    def test_option_selection(self, policy):
        """Test option selection forward pass."""
        batch_size = 4
        obs_dim = 64
        
        observations = torch.randn(batch_size, obs_dim)
        
        # Test deterministic selection
        result = policy(observations, deterministic=True)
        
        assert "selected_options" in result
        assert "option_log_probs" in result
        assert "option_probs" in result
        assert "option_values" in result
        
        assert result["selected_options"].shape == (batch_size,)
        assert result["option_log_probs"].shape == (batch_size,)
        assert result["option_probs"].shape == (batch_size, policy.num_options)
        assert result["option_values"].shape == (batch_size, policy.num_options)
        
        print("✅ Option selection test passed")
    
    def test_option_embeddings(self, policy):
        """Test option embedding functionality."""
        option_indices = torch.tensor([0, 1, 2, 3])
        embeddings = policy.get_option_embedding(option_indices)
        
        if policy.use_option_embeddings:
            assert embeddings.shape == (4, policy.option_embed_dim)
        else:
            assert embeddings.shape == (4, policy.num_options)
        
        print("✅ Option embeddings test passed")
    
    def test_exploration_update(self, policy):
        """Test exploration parameter updates."""
        initial_epsilon = policy.current_epsilon
        initial_temp = policy.gumbel_temperature
        
        policy.update_exploration()
        
        assert policy.current_epsilon <= initial_epsilon
        assert policy.gumbel_temperature <= initial_temp
        assert policy.training_step > 0
        
        print("✅ Exploration update test passed")


class TestWorkerPolicy:
    """Test worker policy π^L_z."""
    
    @pytest.fixture
    def config(self):
        """Basic configuration for worker policy."""
        return {
            "obs_dim": 64,
            "action_dim": 5,
            "num_options": 6,
            "hidden_dim": 128,
            "num_layers": 2,
            "action_space_type": "discrete",
            "option_conditioning": True,
            "separate_workers": False
        }
    
    @pytest.fixture
    def policy(self, config):
        """Create worker policy."""
        return WorkerPolicy(config)
    
    def test_worker_policy_creation(self, policy, config):
        """Test worker policy instantiation."""
        assert policy is not None
        assert policy.action_dim == config["action_dim"]
        assert policy.num_options == config["num_options"]
        assert policy.option_conditioning == config["option_conditioning"]
        print("✅ WorkerPolicy creation test passed")
    
    def test_action_selection(self, policy):
        """Test action selection forward pass."""
        batch_size = 4
        obs_dim = 64
        
        observations = torch.randn(batch_size, obs_dim)
        options = torch.randint(0, policy.num_options, (batch_size,))
        
        # Test deterministic action selection
        result = policy(observations, options, deterministic=True)
        
        assert "actions" in result
        assert "action_log_probs" in result
        assert "values" in result
        
        assert result["actions"].shape == (batch_size,)
        assert result["action_log_probs"].shape == (batch_size,)
        assert result["values"].shape == (batch_size,)
        
        print("✅ Action selection test passed")
    
    def test_separate_workers(self, config):
        """Test separate worker networks per option."""
        config["separate_workers"] = True
        policy = WorkerPolicy(config)
        
        batch_size = 2
        observations = torch.randn(batch_size, config["obs_dim"])
        options = torch.tensor([0, 1])
        
        result = policy(observations, options)
        
        assert result["actions"].shape == (batch_size,)
        assert result["action_log_probs"].shape == (batch_size,)
        
        print("✅ Separate workers test passed")


class TestTerminationFunction:
    """Test termination function β_z."""
    
    @pytest.fixture
    def config(self):
        """Basic configuration for termination function."""
        return {
            "obs_dim": 64,
            "num_options": 6,
            "hidden_dim": 128,
            "termination_strategy": "learned",
            "separate_terminators": True,
            "option_conditioning": True
        }
    
    @pytest.fixture
    def termination_fn(self, config):
        """Create termination function."""
        return TerminationFunction(config)
    
    def test_termination_creation(self, termination_fn, config):
        """Test termination function instantiation."""
        assert termination_fn is not None
        assert termination_fn.num_options == config["num_options"]
        assert termination_fn.termination_strategy == config["termination_strategy"]
        print("✅ TerminationFunction creation test passed")
    
    def test_learned_termination(self, termination_fn):
        """Test learned termination decision."""
        batch_size = 4
        obs_dim = 64
        
        observations = torch.randn(batch_size, obs_dim)
        options = torch.randint(0, termination_fn.num_options, (batch_size,))
        
        result = termination_fn(observations, options)
        
        assert "termination_probs" in result
        assert "should_terminate" in result
        assert "termination_logits" in result
        
        assert result["termination_probs"].shape == (batch_size,)
        assert result["should_terminate"].shape == (batch_size,)
        assert result["termination_logits"].shape == (batch_size,)
        
        print("✅ Learned termination test passed")
    
    def test_fixed_termination(self, config):
        """Test fixed duration termination."""
        config["termination_strategy"] = "fixed"
        config["fixed_duration"] = 5
        termination_fn = TerminationFunction(config)
        
        batch_size = 4
        observations = torch.randn(batch_size, 64)
        options = torch.randint(0, 6, (batch_size,))
        option_durations = torch.tensor([3, 5, 7, 2])
        
        result = termination_fn(observations, options, option_durations=option_durations)
        
        # Should terminate when duration >= fixed_duration
        expected_termination = option_durations >= 5
        assert torch.equal(result["should_terminate"], expected_termination)
        
        print("✅ Fixed termination test passed")
    
    def test_entropy_termination(self, config):
        """Test entropy-based termination."""
        config["termination_strategy"] = "entropy"
        config["entropy_threshold"] = 0.5
        termination_fn = TerminationFunction(config)
        
        batch_size = 4
        observations = torch.randn(batch_size, 64)
        options = torch.randint(0, 6, (batch_size,))
        worker_entropy = torch.tensor([0.3, 0.7, 0.2, 0.8])
        
        result = termination_fn(observations, options, worker_entropy=worker_entropy)
        
        # Should terminate when entropy < threshold
        expected_termination = worker_entropy < 0.5
        assert torch.equal(result["should_terminate"], expected_termination)
        
        print("✅ Entropy termination test passed")


class TestHierarchicalPolicy:
    """Test complete hierarchical policy system."""
    
    @pytest.fixture
    def config(self):
        """Basic configuration for hierarchical policy."""
        return {
            "obs_dim": 64,
            "action_dim": 5,
            "num_options": 6,
            "option_freq": 8,
            "hidden_dim": 128,
            "action_space_type": "discrete",
            "termination_strategy": "learned"
        }
    
    @pytest.fixture
    def policy(self, config):
        """Create hierarchical policy."""
        return HierarchicalPolicy(config)
    
    def test_hierarchical_policy_creation(self, policy, config):
        """Test hierarchical policy instantiation."""
        assert policy is not None
        assert policy.option_freq == config["option_freq"]
        assert policy.num_options == config["num_options"]
        assert hasattr(policy, 'option_policy')
        assert hasattr(policy, 'worker_policy')
        assert hasattr(policy, 'termination_fn')
        print("✅ HierarchicalPolicy creation test passed")
    
    def test_hierarchical_forward_pass(self, policy):
        """Test complete hierarchical forward pass."""
        batch_size = 1  # Single agent
        obs_dim = 64
        
        observations = torch.randn(batch_size, obs_dim)
        
        # First forward pass (should select new option)
        result1 = policy(observations)
        
        assert "actions" in result1
        assert "current_options" in result1
        assert "option_changed" in result1
        assert result1["option_changed"] == True  # First call should select option
        
        # Second forward pass (should continue with same option unless terminated)
        result2 = policy(observations)
        
        # The option might change if termination occurred, so we check more flexibly
        if not result1["should_terminate"].any():
            assert result2["option_changed"] == False  # Should continue with same option
            assert torch.equal(result1["current_options"], result2["current_options"])
        else:
            # If terminated, option might change
            pass
        
        print("✅ Hierarchical forward pass test passed")
    
    def test_option_switching(self, policy):
        """Test option switching behavior."""
        batch_size = 1
        observations = torch.randn(batch_size, 64)
        
        # Run for option_freq steps
        first_option = None
        option_switched = False
        
        for step in range(policy.option_freq + 2):  # Run a bit longer to ensure switching
            result = policy(observations)
            
            if step == 0:
                first_option = result["current_options"].clone()
            elif result["option_changed"]:
                option_switched = True
                break
        
        # Should have switched at some point
        assert option_switched, "Option should have switched during the test"
        
        print("✅ Option switching test passed")
    
    def test_policy_reset(self, policy):
        """Test policy reset functionality."""
        observations = torch.randn(1, 64)
        
        # Run a few steps
        for _ in range(3):
            policy(observations)
        
        assert policy.current_option is not None
        assert policy.option_step_count > 0
        
        # Reset policy
        policy.reset()
        
        assert policy.current_option is None
        assert policy.option_step_count == 0
        assert len(policy.option_history) == 0
        
        print("✅ Policy reset test passed")
    
    def test_metrics_collection(self, policy):
        """Test metrics collection."""
        observations = torch.randn(1, 64)
        
        # Run several steps
        for _ in range(20):
            policy(observations)
        
        metrics = policy.get_metrics()
        
        assert "option_switches" in metrics
        assert "total_steps" in metrics
        assert "avg_option_duration" in metrics
        assert "option_switch_rate" in metrics
        
        assert metrics["total_steps"] == 20
        
        print("✅ Metrics collection test passed")


class TestMultiAgentPolicies:
    """Test multi-agent policy wrappers."""
    
    @pytest.fixture
    def config(self):
        """Configuration for multi-agent policies."""
        return {
            "obs_dim": 64,
            "action_dim": 5,
            "num_options": 6,
            "option_freq": 8,
            "hidden_dim": 128,
            "parameter_sharing": False
        }
    
    def test_multi_agent_option_policy(self, config):
        """Test multi-agent option policy."""
        num_agents = 3
        policy = MultiAgentOptionPolicy(config, num_agents)
        
        observations = {
            "agent_0": torch.randn(1, 64),
            "agent_1": torch.randn(1, 64),
            "agent_2": torch.randn(1, 64)
        }
        
        results = policy(observations)
        
        assert len(results) == 3
        for agent_id in observations.keys():
            assert agent_id in results
            assert "selected_options" in results[agent_id]
        
        print("✅ Multi-agent option policy test passed")
    
    def test_multi_agent_hierarchical_policy(self, config):
        """Test multi-agent hierarchical policy."""
        num_agents = 3
        policy = MultiAgentHierarchicalPolicy(config, num_agents)
        
        observations = {
            "agent_0": torch.randn(1, 64),
            "agent_1": torch.randn(1, 64),
            "agent_2": torch.randn(1, 64)
        }
        
        results = policy(observations)
        
        assert len(results) == 3
        for agent_id in observations.keys():
            assert agent_id in results
            assert "actions" in results[agent_id]
            assert "current_options" in results[agent_id]
        
        print("✅ Multi-agent hierarchical policy test passed")
    
    def test_parameter_sharing(self, config):
        """Test parameter sharing functionality."""
        config["parameter_sharing"] = True
        num_agents = 3
        
        policy = MultiAgentOptionPolicy(config, num_agents)
        
        assert hasattr(policy, 'shared_policy')
        assert not hasattr(policy, 'agent_policies')
        
        print("✅ Parameter sharing test passed")


def test_policy_integration():
    """Integration test with all policy components."""
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 4,
        "option_freq": 5,
        "hidden_dim": 64,
        "num_layers": 2,
        "action_space_type": "discrete",
        "termination_strategy": "learned",
        "parameter_sharing": False
    }
    
    # Test single agent
    single_policy = HierarchicalPolicy(config)
    observations = torch.randn(1, 64)
    
    # Run episode
    for step in range(15):
        result = single_policy(observations)
        assert result["actions"] is not None
        assert result["current_options"] is not None
    
    # Test multi-agent
    multi_policy = MultiAgentHierarchicalPolicy(config, num_agents=2)
    multi_observations = {
        "agent_0": torch.randn(1, 64),
        "agent_1": torch.randn(1, 64)
    }
    
    # Run multi-agent episode
    for step in range(10):
        results = multi_policy(multi_observations)
        assert len(results) == 2
        for agent_id in results:
            assert results[agent_id]["actions"] is not None
    
    print("✅ Policy integration test passed")


if __name__ == "__main__":
    # Run tests manually if not using pytest
    print("Running Policy Module Tests...")
    
    # Basic component tests
    config = {
        "obs_dim": 64,
        "action_dim": 5,
        "num_options": 6,
        "option_freq": 8,
        "hidden_dim": 128,
        "action_space_type": "discrete",
        "termination_strategy": "learned"
    }
    
    # Test option policy
    option_test = TestOptionPolicy()
    option_policy = OptionPolicy(config)
    option_test.test_option_policy_creation(option_policy, config)
    option_test.test_option_selection(option_policy)
    
    # Test worker policy
    worker_test = TestWorkerPolicy()
    worker_policy = WorkerPolicy(config)
    worker_test.test_worker_policy_creation(worker_policy, config)
    worker_test.test_action_selection(worker_policy)
    
    # Test termination function
    term_test = TestTerminationFunction()
    term_fn = TerminationFunction(config)
    term_test.test_termination_creation(term_fn, config)
    term_test.test_learned_termination(term_fn)
    
    # Test hierarchical policy
    hier_test = TestHierarchicalPolicy()
    hier_policy = HierarchicalPolicy(config)
    hier_test.test_hierarchical_policy_creation(hier_policy, config)
    hier_test.test_hierarchical_forward_pass(hier_policy)
    
    # Integration test
    test_policy_integration()
    
    print("\n🎉 All Policy Module Tests Passed!")
    print("✅ Hierarchical policy system is ready for training integration")