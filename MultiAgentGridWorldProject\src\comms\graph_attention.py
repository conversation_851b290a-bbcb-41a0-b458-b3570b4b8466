"""
Graph Attention Communication Module

Implements differentiable attention-based communication using Graph Attention Networks (GAT)
for multi-agent coordination with proximity-based, role-based, and goal-based messaging.
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Dict, Tuple, Optional, Any, List
import numpy as np
import logging

logger = logging.getLogger(__name__)

try:
    from torch_geometric.nn import GATConv
    from torch_geometric.utils import add_self_loops, remove_self_loops
    TORCH_GEOMETRIC_AVAILABLE = True
except ImportError:
    TORCH_GEOMETRIC_AVAILABLE = False
    logger.warning("torch_geometric not available. Communication module will use fallback implementation.")


class GraphAttentionEncoder(nn.Module):
    """
    Graph Attention Network encoder for multi-agent communication.
    
    Uses GAT layers to enable agents to communicate based on graph structure,
    with support for masking, attention visualization, and KL regularization.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Architecture parameters
        self.input_dim = config.get("input_dim", 128)
        self.hidden_dim = config.get("hidden_dim", 128)
        self.output_dim = config.get("output_dim", 128)
        self.num_heads = config.get("num_heads", 4)
        self.num_layers = config.get("num_layers", 2)
        self.dropout = config.get("dropout", 0.1)
        
        # Communication parameters
        self.use_residual = config.get("use_residual", True)
        self.use_layer_norm = config.get("use_layer_norm", True)
        self.attention_dropout = config.get("attention_dropout", 0.1)
        
        # KL regularization
        self.kl_weight = config.get("kl_weight", 0.01)
        self.use_kl_regularization = config.get("use_kl_regularization", True)
        
        # Build network
        self._build_network()
        
        # Store attention weights for visualization
        self.attention_weights = None
        
        logger.info(f"GraphAttentionEncoder initialized: {self.input_dim}->{self.output_dim}, "
                   f"{self.num_heads} heads, {self.num_layers} layers")
    
    def _build_network(self):
        """Build the GAT network layers."""
        
        if TORCH_GEOMETRIC_AVAILABLE:
            # Use torch_geometric GAT implementation
            self.gat_layers = nn.ModuleList()
            
            # Input layer
            self.gat_layers.append(
                GATConv(
                    in_channels=self.input_dim,
                    out_channels=self.hidden_dim // self.num_heads,
                    heads=self.num_heads,
                    dropout=self.attention_dropout,
                    concat=True
                )
            )
            
            # Hidden layers
            for _ in range(self.num_layers - 2):
                self.gat_layers.append(
                    GATConv(
                        in_channels=self.hidden_dim,
                        out_channels=self.hidden_dim // self.num_heads,
                        heads=self.num_heads,
                        dropout=self.attention_dropout,
                        concat=True
                    )
                )
            
            # Output layer
            if self.num_layers > 1:
                self.gat_layers.append(
                    GATConv(
                        in_channels=self.hidden_dim,
                        out_channels=self.output_dim,
                        heads=1,
                        dropout=self.attention_dropout,
                        concat=False
                    )
                )
        else:
            # Fallback implementation without torch_geometric
            self._build_fallback_network()
        
        # Layer normalization
        if self.use_layer_norm:
            self.layer_norms = nn.ModuleList([
                nn.LayerNorm(self.hidden_dim if i < self.num_layers - 1 else self.output_dim)
                for i in range(self.num_layers)
            ])
        
        # Residual projection
        if self.use_residual and self.input_dim != self.output_dim:
            self.residual_proj = nn.Linear(self.input_dim, self.output_dim)
    
    def _build_fallback_network(self):
        """Build fallback attention network without torch_geometric."""
        self.attention_layers = nn.ModuleList()
        self.value_layers = nn.ModuleList()
        
        for i in range(self.num_layers):
            input_size = self.input_dim if i == 0 else self.hidden_dim
            output_size = self.output_dim if i == self.num_layers - 1 else self.hidden_dim
            
            # Multi-head attention
            self.attention_layers.append(
                nn.MultiheadAttention(
                    embed_dim=input_size,
                    num_heads=self.num_heads,
                    dropout=self.attention_dropout,
                    batch_first=True
                )
            )
            
            # Value projection
            self.value_layers.append(nn.Linear(input_size, output_size))
    
    def forward(self, x: torch.Tensor, edge_index: torch.Tensor, 
                mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        Forward pass through graph attention network.
        
        Args:
            x: Node features [num_nodes, input_dim]
            edge_index: Graph edges [2, num_edges]
            mask: Communication mask [num_nodes] (1=active, 0=muted)
            
        Returns:
            Updated node features [num_nodes, output_dim]
        """
        if TORCH_GEOMETRIC_AVAILABLE:
            return self._forward_geometric(x, edge_index, mask)
        else:
            return self._forward_fallback(x, edge_index, mask)
    
    def _forward_geometric(self, x: torch.Tensor, edge_index: torch.Tensor,
                          mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Forward pass using torch_geometric."""
        
        # Store input for residual connection
        residual = x
        
        # Apply mask to input features
        if mask is not None:
            x = x * mask.unsqueeze(-1)
            # Replace NaN values with zeros for muted agents
            x = torch.where(torch.isfinite(x), x, torch.zeros_like(x))
        
        # Apply GAT layers
        for i, gat_layer in enumerate(self.gat_layers):
            # GAT forward pass
            x_new, attention_weights = gat_layer(x, edge_index, return_attention_weights=True)
            
            # Store attention weights for visualization (last layer only)
            if i == len(self.gat_layers) - 1:
                self.attention_weights = attention_weights
            
            # Apply activation (except last layer)
            if i < len(self.gat_layers) - 1:
                x_new = F.relu(x_new)
                x_new = F.dropout(x_new, p=self.dropout, training=self.training)
            
            # Layer normalization
            if self.use_layer_norm:
                x_new = self.layer_norms[i](x_new)
            
            x = x_new
        
        # Residual connection
        if self.use_residual:
            if hasattr(self, 'residual_proj'):
                residual = self.residual_proj(residual)
            x = x + residual
        
        # Apply mask to output
        if mask is not None:
            x = x * mask.unsqueeze(-1)
        
        return x
    
    def _forward_fallback(self, x: torch.Tensor, edge_index: torch.Tensor,
                         mask: Optional[torch.Tensor] = None) -> torch.Tensor:
        """Fallback forward pass without torch_geometric."""
        
        # Store input shape for attention map fallback
        self._last_input_shape = x.shape
        
        # Convert to batch format for MultiheadAttention
        x = x.unsqueeze(0)  # [1, num_nodes, input_dim]
        
        # Store input for residual
        residual = x
        
        # Apply input mask (zero out muted agents)
        if mask is not None:
            x = x * mask.unsqueeze(0).unsqueeze(-1)  # [1, num_nodes, input_dim]
        
        # Apply attention layers
        for i, (attn_layer, value_layer) in enumerate(zip(self.attention_layers, self.value_layers)):
            # Self-attention (simplified graph attention)
            attn_output, attention_weights = attn_layer(x, x, x)
            
            # Store attention weights (only from last layer)
            if i == len(self.attention_layers) - 1:
                self.attention_weights = attention_weights
            
            # Value projection
            x = value_layer(attn_output)
            
            # Apply mask after each layer
            if mask is not None:
                x = x * mask.unsqueeze(0).unsqueeze(-1)
            
            # Activation and dropout (except last layer)
            if i < len(self.attention_layers) - 1:
                x = F.relu(x)
                x = F.dropout(x, p=self.dropout, training=self.training)
            
            # Layer normalization
            if self.use_layer_norm:
                x = self.layer_norms[i](x)
        
        # Residual connection
        if self.use_residual:
            if hasattr(self, 'residual_proj'):
                residual = self.residual_proj(residual)
            x = x + residual
        
        # Remove batch dimension
        x = x.squeeze(0)  # [num_nodes, output_dim]
        
        # Final mask application
        if mask is not None:
            x = x * mask.unsqueeze(-1)
            # Replace any NaN values with zeros
            x = torch.where(torch.isfinite(x), x, torch.zeros_like(x))
        
        return x
    
    def _create_attention_mask(self, edge_index: torch.Tensor, 
                              mask: torch.Tensor) -> torch.Tensor:
        """Create attention mask from edge index and communication mask."""
        num_nodes = mask.shape[0]
        attention_mask = torch.zeros(num_nodes, num_nodes, dtype=torch.bool)
        
        # Set edges based on edge_index
        if edge_index.shape[1] > 0:
            attention_mask[edge_index[0], edge_index[1]] = True
        
        # Apply communication mask
        muted_agents = (mask == 0)
        attention_mask[muted_agents, :] = True  # Muted agents can't send
        attention_mask[:, muted_agents] = True  # Muted agents can't receive
        
        return attention_mask
    
    def compute_kl_divergence(self) -> torch.Tensor:
        """Compute KL divergence between attention weights and uniform distribution."""
        if not self.use_kl_regularization or self.attention_weights is None:
            return torch.tensor(0.0, device=next(self.parameters()).device)
        
        if TORCH_GEOMETRIC_AVAILABLE:
            # For torch_geometric, attention_weights is (edge_index, alpha)
            edge_index, alpha = self.attention_weights
            
            if alpha.numel() == 0:
                return torch.tensor(0.0, device=alpha.device)
            
            # Normalize attention weights per source node
            num_nodes = edge_index.max().item() + 1
            kl_loss = 0.0
            
            for node in range(num_nodes):
                # Get attention weights for edges from this node
                mask = edge_index[0] == node
                if mask.sum() > 0:
                    node_attention = alpha[mask]
                    # Uniform distribution
                    uniform = torch.ones_like(node_attention) / node_attention.shape[0]
                    # KL divergence
                    kl_loss += F.kl_div(
                        F.log_softmax(node_attention, dim=0),
                        uniform,
                        reduction='sum'
                    )
            
            return self.kl_weight * kl_loss / num_nodes
        
        else:
            # For fallback implementation
            if self.attention_weights.dim() < 3:
                return torch.tensor(0.0, device=next(self.parameters()).device)
                
            attention_weights = self.attention_weights  # [1, num_heads, num_nodes, num_nodes]
            
            # Average over heads and batch
            avg_attention = attention_weights.mean(dim=(0, 1))  # [num_nodes, num_nodes]
            
            # Compute KL divergence for each row (source node)
            kl_loss = torch.tensor(0.0, device=avg_attention.device)
            for i in range(avg_attention.shape[0]):
                row_attention = avg_attention[i]
                # Skip if no attention (all zeros or NaN)
                if torch.isfinite(row_attention).all() and row_attention.sum() > 1e-8:
                    row_attention = row_attention / (row_attention.sum() + 1e-8)  # Normalize
                    uniform = torch.ones_like(row_attention) / row_attention.numel()
                    kl_loss += F.kl_div(
                        torch.log(row_attention + 1e-8),
                        uniform,
                        reduction='sum'
                    )
            
            return self.kl_weight * kl_loss / avg_attention.shape[0]
    
    def get_attention_map(self) -> Optional[torch.Tensor]:
        """Get attention weights for visualization."""
        if self.attention_weights is None:
            return None
        
        if TORCH_GEOMETRIC_AVAILABLE:
            edge_index, alpha = self.attention_weights
            num_nodes = edge_index.max().item() + 1
            
            # Convert sparse attention to dense matrix
            attention_matrix = torch.zeros(num_nodes, num_nodes, device=alpha.device)
            attention_matrix[edge_index[0], edge_index[1]] = alpha
            
            return attention_matrix
        else:
            # For fallback, attention_weights from MultiheadAttention is [batch, seq_len, seq_len]
            if self.attention_weights.dim() == 3:
                # [batch, seq_len, seq_len] -> [seq_len, seq_len]
                attention_map = self.attention_weights.squeeze(0)  # Remove batch dimension
                return attention_map
            elif self.attention_weights.dim() == 2:
                # Already 2D [seq_len, seq_len]
                return self.attention_weights
            else:
                # Unexpected dimension - create identity matrix as fallback
                # Infer sequence length from the stored input
                if hasattr(self, '_last_input_shape'):
                    seq_len = self._last_input_shape[0]
                else:
                    seq_len = 4  # Default fallback
                attention_map = torch.eye(seq_len, device=self.attention_weights.device)
                return attention_map


class MultiAgentCommunicationModule(nn.Module):
    """
    Complete multi-agent communication module with graph attention.
    
    Integrates with hierarchical policies to enable coordinated decision-making
    through differentiable message passing.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        self.num_agents = config.get("num_agents", 4)
        self.communication_dim = config.get("communication_dim", 128)
        self.max_communication_range = config.get("max_communication_range", 5.0)
        
        # Graph attention encoder
        gat_config = config.copy()
        gat_config.update({
            "input_dim": self.communication_dim,
            "output_dim": self.communication_dim,
            "hidden_dim": config.get("comm_hidden_dim", 128),
            "num_heads": config.get("attention_heads", 4),
            "num_layers": config.get("comm_layers", 2)
        })
        
        self.graph_attention = GraphAttentionEncoder(gat_config)
        
        # Message processing
        self.message_encoder = nn.Sequential(
            nn.Linear(self.communication_dim, self.communication_dim),
            nn.ReLU(),
            nn.Linear(self.communication_dim, self.communication_dim)
        )
        
        self.message_decoder = nn.Sequential(
            nn.Linear(self.communication_dim, self.communication_dim),
            nn.ReLU(),
            nn.Linear(self.communication_dim, self.communication_dim)
        )
        
        # Communication gating
        self.communication_gate = nn.Sequential(
            nn.Linear(self.communication_dim * 2, self.communication_dim),
            nn.Sigmoid()
        )
        
        logger.info(f"MultiAgentCommunicationModule initialized: {self.num_agents} agents, "
                   f"{self.communication_dim}D communication")
    
    def forward(self, agent_features: Dict[str, torch.Tensor],
                agent_positions: Optional[Dict[str, torch.Tensor]] = None,
                communication_mask: Optional[Dict[str, bool]] = None) -> Dict[str, torch.Tensor]:
        """
        Forward pass for multi-agent communication.
        
        Args:
            agent_features: Dict mapping agent_id to feature tensor
            agent_positions: Dict mapping agent_id to position tensor (for proximity)
            communication_mask: Dict mapping agent_id to communication status
            
        Returns:
            Dict mapping agent_id to updated features after communication
        """
        
        # Convert to tensor format
        agent_ids = list(agent_features.keys())
        x = torch.stack([agent_features[agent_id] for agent_id in agent_ids])  # [num_agents, feature_dim]
        
        # Create communication graph
        edge_index = self._create_communication_graph(agent_ids, agent_positions)
        
        # Create communication mask
        mask = self._create_communication_mask(agent_ids, communication_mask)
        
        # Encode messages
        messages = self.message_encoder(x)
        
        # Apply graph attention
        attended_messages = self.graph_attention(messages, edge_index, mask)
        
        # Decode messages
        decoded_messages = self.message_decoder(attended_messages)
        
        # Apply communication gating
        combined = torch.cat([x, decoded_messages], dim=-1)
        gate = self.communication_gate(combined)
        updated_features = x + gate * decoded_messages
        
        # Convert back to dictionary format
        result = {}
        for i, agent_id in enumerate(agent_ids):
            result[agent_id] = updated_features[i]
        
        return result
    
    def _create_communication_graph(self, agent_ids: List[str],
                                   agent_positions: Optional[Dict[str, torch.Tensor]]) -> torch.Tensor:
        """Create communication graph based on proximity or full connectivity."""
        num_agents = len(agent_ids)
        
        if agent_positions is not None:
            # Proximity-based communication
            edges = []
            positions = torch.stack([agent_positions[agent_id] for agent_id in agent_ids])
            
            for i in range(num_agents):
                for j in range(num_agents):
                    if i != j:
                        distance = torch.norm(positions[i] - positions[j])
                        if distance <= self.max_communication_range:
                            edges.append([i, j])
            
            if edges:
                edge_index = torch.tensor(edges, dtype=torch.long).t()
            else:
                # Fallback to self-loops if no connections
                edge_index = torch.tensor([[i, i] for i in range(num_agents)], dtype=torch.long).t()
        else:
            # Full connectivity (all-to-all communication)
            edges = []
            for i in range(num_agents):
                for j in range(num_agents):
                    if i != j:  # No self-loops
                        edges.append([i, j])
            
            edge_index = torch.tensor(edges, dtype=torch.long).t() if edges else torch.empty((2, 0), dtype=torch.long)
        
        return edge_index
    
    def _create_communication_mask(self, agent_ids: List[str],
                                  communication_mask: Optional[Dict[str, bool]]) -> torch.Tensor:
        """Create communication mask tensor."""
        if communication_mask is None:
            return torch.ones(len(agent_ids))
        
        mask = torch.tensor([
            1.0 if communication_mask.get(agent_id, True) else 0.0
            for agent_id in agent_ids
        ])
        
        return mask
    
    def get_communication_metrics(self) -> Dict[str, float]:
        """Get communication-related metrics."""
        metrics = {}
        
        # KL divergence
        kl_loss = self.graph_attention.compute_kl_divergence()
        if isinstance(kl_loss, torch.Tensor):
            metrics["communication_kl_loss"] = kl_loss.item()
        else:
            metrics["communication_kl_loss"] = float(kl_loss)
        
        # Attention entropy
        attention_map = self.graph_attention.get_attention_map()
        if attention_map is not None and attention_map.dim() == 2:
            # Ensure finite values
            if torch.isfinite(attention_map).all():
                # Compute entropy of attention distribution
                attention_probs = F.softmax(attention_map, dim=-1)
                entropy = -torch.sum(attention_probs * torch.log(attention_probs + 1e-8), dim=-1)
                metrics["attention_entropy"] = entropy.mean().item()
            else:
                metrics["attention_entropy"] = 0.0
        else:
            metrics["attention_entropy"] = 0.0
        
        return metrics
    
    def visualize_attention(self) -> Optional[torch.Tensor]:
        """Get attention weights for visualization."""
        return self.graph_attention.get_attention_map()