"""
Training System Tests

Tests the complete training pipeline including rollout collection,
training steps, callbacks, and the full training runner.
"""

import pytest
import torch
import numpy as np
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch

from src.training.rollout import RolloutCollector, MultiAgentRolloutCollector
from src.training.trainer import HierarchicalTrainer
from src.training.runner import <PERSON>Runner
from src.training.callbacks import EarlyStopping, Model<PERSON>heckpoint, MetricsLogger, CallbackManager
from src.controllers.hierarchical_agent import HierarchicalAgent
from src.environment.grid_world import GridWorldEnv


class MockEnvironment:
    """Mock environment for testing."""
    
    def __init__(self, obs_dim=10, max_steps=20):
        self.obs_dim = obs_dim
        self.max_steps = max_steps
        self.current_step = 0
        
    def reset(self):
        self.current_step = 0
        return {
            "state": torch.randn(8),
            "position": torch.tensor([0.0, 0.0])
        }
    
    def step(self, action):
        self.current_step += 1
        
        next_obs = {
            "state": torch.randn(8),
            "position": torch.tensor([float(self.current_step), 0.0])
        }
        
        reward = np.random.random() - 0.5  # Random reward
        done = self.current_step >= self.max_steps
        info = {}
        
        return next_obs, reward, done, info


class MockMultiAgentEnvironment:
    """Mock multi-agent environment for testing."""
    
    def __init__(self, num_agents=2, obs_dim=10, max_steps=20):
        self.num_agents = num_agents
        self.obs_dim = obs_dim
        self.max_steps = max_steps
        self.current_step = 0
        
    def reset(self):
        self.current_step = 0
        return {
            f"agent_{i}": {
                "state": torch.randn(8),
                "position": torch.tensor([0.0, float(i)])
            }
            for i in range(self.num_agents)
        }
    
    def step(self, actions):
        self.current_step += 1
        
        next_obs = {
            f"agent_{i}": {
                "state": torch.randn(8),
                "position": torch.tensor([float(self.current_step), float(i)])
            }
            for i in range(self.num_agents)
        }
        
        rewards = {
            f"agent_{i}": np.random.random() - 0.5
            for i in range(self.num_agents)
        }
        
        done = self.current_step >= self.max_steps
        dones = {f"agent_{i}": done for i in range(self.num_agents)}
        
        info = {}
        
        return next_obs, rewards, dones, info


class TestRolloutCollector:
    """Test rollout collection functionality."""
    
    @pytest.fixture
    def rollout_config(self):
        return {
            "horizon": 50,
            "track_attention": False,
            "track_options": True
        }
    
    @pytest.fixture
    def agent_config(self):
        return {
            "policy": {
                "num_options": 4,
                "obs_dim": 10,
                "action_dim": 4,
                "hidden_dim": 64
            },
            "use_communication": False,
            "max_option_duration": 8,
            "option_frequency": 4
        }
    
    @pytest.fixture
    def collector(self, rollout_config):
        return RolloutCollector(rollout_config)
    
    @pytest.fixture
    def agent(self, agent_config):
        return HierarchicalAgent("test_agent", agent_config)
    
    @pytest.fixture
    def env(self):
        return MockEnvironment(max_steps=10)
    
    def test_rollout_collector_initialization(self, collector, rollout_config):
        """Test rollout collector initialization."""
        assert collector.horizon == rollout_config["horizon"]
        assert collector.track_attention == rollout_config["track_attention"]
        assert collector.track_options == rollout_config["track_options"]
    
    def test_rollout_collection_shapes(self, collector, agent, env):
        """Test that rollout collection produces correct tensor shapes."""
        rollout_data = collector.collect_rollout(env, agent, max_steps=20)
        
        # Check that we have data
        assert "observations" in rollout_data
        assert "actions" in rollout_data
        assert "rewards" in rollout_data
        assert "dones" in rollout_data
        
        batch_size = rollout_data["batch_size"]
        assert batch_size > 0
        
        # Check shapes
        assert rollout_data["observations"].shape[0] == batch_size
        assert rollout_data["actions"].shape == (batch_size,)
        assert rollout_data["rewards"].shape == (batch_size,)
        assert rollout_data["dones"].shape == (batch_size,)
        
        # Check hierarchical fields
        assert rollout_data["options"].shape == (batch_size,)
        assert rollout_data["option_durations"].shape == (batch_size,)
        assert rollout_data["terminations"].shape == (batch_size,)
        assert rollout_data["option_log_probs"].shape == (batch_size,)
        assert rollout_data["action_log_probs"].shape == (batch_size,)
        assert rollout_data["values"].shape == (batch_size,)
    
    def test_rollout_data_validity(self, collector, agent, env):
        """Test that rollout data contains valid values."""
        rollout_data = collector.collect_rollout(env, agent, max_steps=15)
        
        # Check that all tensors are finite
        for key, tensor in rollout_data.items():
            if isinstance(tensor, torch.Tensor):
                assert torch.isfinite(tensor).all(), f"Non-finite values in {key}"
        
        # Check value ranges
        assert torch.all(rollout_data["actions"] >= 0), "Actions should be non-negative"
        assert torch.all(rollout_data["actions"] < 4), "Actions should be within action space"
        
        assert torch.all(rollout_data["options"] >= 0), "Options should be non-negative"
        assert torch.all(rollout_data["options"] < 4), "Options should be within option space"
        
        assert torch.all(rollout_data["terminations"] >= 0), "Termination probs should be non-negative"
        assert torch.all(rollout_data["terminations"] <= 1), "Termination probs should be <= 1"
    
    def test_episode_statistics(self, collector, agent, env):
        """Test episode statistics collection."""
        rollout_data = collector.collect_rollout(env, agent, max_steps=25)
        
        # Should have episode statistics
        assert "episode_rewards" in rollout_data
        assert "episode_lengths" in rollout_data
        
        episode_rewards = rollout_data["episode_rewards"]
        episode_lengths = rollout_data["episode_lengths"]
        
        # Should have at least one episode
        assert len(episode_rewards) > 0
        assert len(episode_lengths) > 0
        assert len(episode_rewards) == len(episode_lengths)
        
        # Episode lengths should be positive
        assert all(length > 0 for length in episode_lengths)


class TestMultiAgentRolloutCollector:
    """Test multi-agent rollout collection."""
    
    @pytest.fixture
    def rollout_config(self):
        return {
            "num_agents": 2,
            "horizon": 50,
            "use_communication": False
        }
    
    @pytest.fixture
    def agent_config(self):
        return {
            "policy": {
                "num_options": 4,
                "obs_dim": 10,
                "action_dim": 4,
                "hidden_dim": 64
            },
            "use_communication": False
        }
    
    @pytest.fixture
    def collector(self, rollout_config):
        return MultiAgentRolloutCollector(rollout_config)
    
    @pytest.fixture
    def agents(self, agent_config):
        return {
            "agent_0": HierarchicalAgent("agent_0", agent_config),
            "agent_1": HierarchicalAgent("agent_1", agent_config)
        }
    
    @pytest.fixture
    def env(self):
        return MockMultiAgentEnvironment(num_agents=2, max_steps=10)
    
    def test_multi_agent_rollout_collection(self, collector, agents, env):
        """Test multi-agent rollout collection."""
        rollout_data = collector.collect_rollout(env, agents, max_steps=20)
        
        # Check structure
        assert "agents" in rollout_data
        assert "global_stats" in rollout_data
        
        # Check agent data
        for agent_id in agents.keys():
            assert agent_id in rollout_data["agents"]
            agent_data = rollout_data["agents"][agent_id]
            
            batch_size = agent_data["batch_size"]
            assert batch_size > 0
            
            # Check shapes
            assert agent_data["observations"].shape[0] == batch_size
            assert agent_data["actions"].shape == (batch_size,)
            assert agent_data["rewards"].shape == (batch_size,)
            assert agent_data["options"].shape == (batch_size,)
        
        # Check global stats
        global_stats = rollout_data["global_stats"]
        assert "episode_rewards" in global_stats
        assert "episode_lengths" in global_stats
        assert global_stats["num_agents"] == 2


class TestHierarchicalTrainer:
    """Test hierarchical trainer functionality."""
    
    @pytest.fixture
    def trainer_config(self):
        return {
            "learning_rate": 3e-4,
            "gamma": 0.99,
            "gae_lambda": 0.95,
            "clip_grad_norm": 1.0,
            "entropy_coef": 0.01,
            "value_coef": 0.5,
            "separate_optimizers": True
        }
    
    @pytest.fixture
    def agent_config(self):
        return {
            "policy": {
                "num_options": 4,
                "obs_dim": 10,
                "action_dim": 4,
                "hidden_dim": 64
            },
            "use_communication": False
        }
    
    @pytest.fixture
    def trainer(self, trainer_config):
        return HierarchicalTrainer(trainer_config)
    
    @pytest.fixture
    def agent(self, agent_config):
        return HierarchicalAgent("test_agent", agent_config)
    
    @pytest.fixture
    def sample_batch(self):
        batch_size = 16
        return {
            "observations": torch.randn(batch_size, 10),
            "actions": torch.randint(0, 4, (batch_size,)),
            "rewards": torch.randn(batch_size),
            "dones": torch.randint(0, 2, (batch_size,)).bool(),
            "next_observations": torch.randn(batch_size, 10),
            "options": torch.randint(0, 4, (batch_size,)),
            "option_durations": torch.randint(1, 5, (batch_size,)),
            "terminations": torch.rand(batch_size),
            "option_log_probs": torch.randn(batch_size),
            "action_log_probs": torch.randn(batch_size),
            "values": torch.randn(batch_size),
            "batch_size": batch_size
        }
    
    def test_trainer_initialization(self, trainer, trainer_config):
        """Test trainer initialization."""
        assert trainer.learning_rate == trainer_config["learning_rate"]
        assert trainer.gamma == trainer_config["gamma"]
        assert trainer.separate_optimizers == trainer_config["separate_optimizers"]
    
    def test_agent_setup(self, trainer, agent):
        """Test agent setup in trainer."""
        agents = {"test_agent": agent}
        trainer.setup_agents(agents)
        
        # Check that optimizers were created
        assert len(trainer.optimizers) > 0
        
        # With separate optimizers, should have multiple optimizers
        if trainer.separate_optimizers:
            assert "test_agent_option" in trainer.optimizers
            assert "test_agent_worker" in trainer.optimizers
            assert "test_agent_termination" in trainer.optimizers
    
    def test_training_step_no_nan(self, trainer, agent, sample_batch):
        """Test that training step produces no NaN values."""
        agents = {"test_agent": agent}
        trainer.setup_agents(agents)
        
        # Ensure agent parameters require gradients
        for param in agent.policy.parameters():
            param.requires_grad_(True)
        
        # Training step
        metrics = trainer.train_step(sample_batch)
        
        # Check that all metrics are finite
        for key, value in metrics.items():
            assert np.isfinite(value), f"NaN/Inf value in metric {key}: {value}"
        
        # Check that we have expected metrics
        assert "total_loss" in metrics
        assert metrics["total_loss"] >= 0, "Total loss should be non-negative"
    
    def test_loss_computation(self, trainer, agent, sample_batch):
        """Test loss computation produces reasonable values."""
        agents = {"test_agent": agent}
        trainer.setup_agents(agents)
        
        metrics = trainer.train_step(sample_batch)
        
        # Check loss components
        expected_losses = [
            "option_policy_loss", "worker_policy_loss", "worker_value_loss",
            "termination_loss", "total_loss"
        ]
        
        for loss_name in expected_losses:
            if loss_name in metrics:
                loss_value = metrics[loss_name]
                assert np.isfinite(loss_value), f"{loss_name} is not finite: {loss_value}"
                assert loss_value < 1000, f"{loss_name} is too large: {loss_value}"


class TestCallbacks:
    """Test training callbacks."""
    
    def test_early_stopping(self):
        """Test early stopping callback."""
        early_stopping = EarlyStopping(metric="avg_reward", patience=3, min_delta=0.01)
        
        # No improvement for several epochs
        metrics_sequence = [
            {"avg_reward": 1.0},
            {"avg_reward": 1.005},  # Small improvement, below min_delta
            {"avg_reward": 0.99},   # Worse
            {"avg_reward": 0.98},   # Worse
            {"avg_reward": 0.97},   # Worse - should trigger stopping
        ]
        
        agents = {}
        should_stop = False
        
        for epoch, metrics in enumerate(metrics_sequence):
            result = early_stopping.on_epoch_end(epoch, metrics, agents)
            if result.get("stop_training", False):
                should_stop = True
                break
        
        assert should_stop, "Early stopping should have been triggered"
    
    def test_model_checkpoint(self):
        """Test model checkpoint callback."""
        with tempfile.TemporaryDirectory() as temp_dir:
            checkpoint_dir = Path(temp_dir)
            
            checkpoint_callback = ModelCheckpoint(
                checkpoint_dir=checkpoint_dir,
                save_frequency=2,
                save_best=True,
                metric="avg_reward"
            )
            
            # Create a real agent for testing (Mock objects can't be pickled)
            from src.controllers.hierarchical_agent import HierarchicalAgent
            
            agent_config = {
                "policy": {
                    "obs_dim": 10,
                    "action_dim": 4,
                    "num_options": 4,
                    "hidden_dim": 32
                }
            }
            agent = HierarchicalAgent("test_agent", agent_config)
            agents = {"test_agent": agent}
            
            # Test regular checkpoint saving
            result = checkpoint_callback.on_epoch_end(2, {"avg_reward": 1.0}, agents)
            assert result.get("checkpoint_saved", False)
            
            # Test best model saving
            result = checkpoint_callback.on_epoch_end(3, {"avg_reward": 2.0}, agents)
            assert result.get("best_model_saved", False)
    
    def test_metrics_logger(self):
        """Test metrics logger callback."""
        with tempfile.TemporaryDirectory() as temp_dir:
            log_dir = Path(temp_dir)
            
            metrics_logger = MetricsLogger(
                log_dir=log_dir,
                save_frequency=2
            )
            
            agents = {}
            
            # Log some metrics
            metrics_logger.on_epoch_end(1, {"avg_reward": 1.0, "loss": 0.5}, agents)
            metrics_logger.on_epoch_end(2, {"avg_reward": 1.2, "loss": 0.4}, agents)
            
            # Check that metrics were saved
            metrics_file = log_dir / "metrics.json"
            assert metrics_file.exists()
    
    def test_callback_manager(self):
        """Test callback manager coordination."""
        # Create mock callbacks
        callback1 = Mock()
        callback1.on_epoch_end.return_value = {"callback1": True}
        
        callback2 = Mock()
        callback2.on_epoch_end.return_value = {"stop_training": True}
        
        manager = CallbackManager([callback1, callback2])
        
        # Test epoch end
        result = manager.on_epoch_end(1, {"test": 1.0}, {})
        
        # Both callbacks should be called
        callback1.on_epoch_end.assert_called_once()
        callback2.on_epoch_end.assert_called_once()
        
        # Should propagate stop_training signal
        assert result.get("stop_training", False)


class TestTrainingIntegration:
    """Test complete training integration."""
    
    @pytest.fixture
    def temp_dir(self):
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # Windows-friendly cleanup
        try:
            shutil.rmtree(temp_dir)
        except PermissionError:
            # On Windows, files might still be locked
            import time
            time.sleep(0.1)
            try:
                shutil.rmtree(temp_dir)
            except PermissionError:
                # If still locked, just pass - temp files will be cleaned up eventually
                pass
    
    def test_training_runner_setup(self, temp_dir):
        """Test training runner setup."""
        config = {
            "num_epochs": 5,
            "steps_per_epoch": 20,
            "results_dir": temp_dir,
            "experiment_name": "test_experiment"
        }
        
        runner = TrainingRunner(config)
        
        # Check initialization
        assert runner.num_epochs == 5
        assert runner.steps_per_epoch == 20
        assert runner.experiment_name == "test_experiment"
        
        # Check directories were created
        assert runner.run_dir.exists()
        assert (runner.run_dir / "checkpoints").exists()
        assert (runner.run_dir / "plots").exists()
        assert (runner.run_dir / "logs").exists()
    
    def test_short_training_run(self, temp_dir):
        """Test a short training run to ensure everything works together."""
        config = {
            "num_epochs": 3,
            "steps_per_epoch": 10,
            "eval_frequency": 2,
            "eval_episodes": 2,
            "results_dir": temp_dir,
            "experiment_name": "integration_test",
            "multi_agent": False
        }
        
        runner = TrainingRunner(config)
        
        # Setup environment
        env_config = {"max_steps": 5}
        runner.env = MockEnvironment(**env_config)
        runner.eval_env = MockEnvironment(**env_config)
        
        # Setup agents
        agent_config = {
            "policy": {
                "num_options": 4,
                "obs_dim": 10,
                "action_dim": 4,
                "hidden_dim": 32  # Smaller for faster testing
            },
            "use_communication": False
        }
        runner.setup_agents(agent_config)
        
        # Setup training
        training_config = {
            "learning_rate": 1e-3,
            "gamma": 0.99,
            "use_early_stopping": False,  # Disable for short test
            "horizon": 10
        }
        runner.setup_training(training_config)
        
        # Run training
        results = runner.train()
        
        # Check results
        assert "final_evaluation" in results
        assert "training_time" in results
        assert results["training_time"] > 0
        
        # Check that files were created
        assert (runner.run_dir / "results.json").exists()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])