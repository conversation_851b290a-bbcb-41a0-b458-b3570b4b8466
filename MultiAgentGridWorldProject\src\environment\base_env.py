"""
Base Multi-Agent Environment Interface

Provides a unified API for all multi-agent environments with type safety,
evaluation metrics, and research-oriented features.
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Any, Optional, Union
import numpy as np
from gymnasium import spaces
import logging

logger = logging.getLogger(__name__)


class BaseMultiAgentEnv(ABC):
    """
    Abstract base class for multi-agent environments.
    
    Enforces a consistent interface across all environments and provides
    common functionality for evaluation metrics and debugging.
    """
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.num_agents = config.get("num_agents", 4)
        self.episode_limit = config.get("episode_limit", 200)
        self.seed_value = config.get("seed", 0)
        
        # Environment state
        self.current_step = 0
        self.episode_count = 0
        self.total_steps = 0
        
        # Metrics tracking
        self.episode_metrics = {}
        self.cumulative_metrics = {}
        
        # Action and observation spaces (to be defined by subclasses)
        self.action_space: Optional[Union[spaces.Space, List[spaces.Space]]] = None
        self.observation_space: Optional[Union[spaces.Space, List[spaces.Space]]] = None
        
        # Agent management
        self.agent_ids = [f"agent_{i}" for i in range(self.num_agents)]
        self.active_agents = set(self.agent_ids)
        
    @abstractmethod
    def reset(self) -> Tuple[Dict[str, np.ndarray], Dict[str, Any]]:
        """
        Reset the environment to initial state.
        
        Returns:
            observations: Dict mapping agent_id to observation
            info: Dict with environment state information
        """
        pass
    
    @abstractmethod
    def step(self, actions: Dict[str, Any]) -> Tuple[
        Dict[str, np.ndarray],  # observations
        Dict[str, float],       # rewards
        Dict[str, bool],        # dones
        Dict[str, Any]          # info
    ]:
        """
        Execute one environment step.
        
        Args:
            actions: Dict mapping agent_id to action
            
        Returns:
            observations: Dict mapping agent_id to observation
            rewards: Dict mapping agent_id to reward
            dones: Dict mapping agent_id to done flag
            info: Dict with step information and metrics
        """
        pass
    
    @abstractmethod
    def get_obs(self) -> Dict[str, np.ndarray]:
        """Get current observations for all agents."""
        pass
    
    @abstractmethod
    def get_state(self) -> np.ndarray:
        """Get global state for centralized critic."""
        pass
    
    def render(self, mode: str = "human") -> Optional[np.ndarray]:
        """Render the environment."""
        pass
    
    def close(self):
        """Clean up environment resources."""
        pass
    
    def seed(self, seed: int):
        """Set random seed for reproducibility."""
        self.seed_value = seed
        np.random.seed(seed)
    
    # Evaluation Metrics Interface
    def get_episode_metrics(self) -> Dict[str, float]:
        """Get metrics for the current episode."""
        return self.episode_metrics.copy()
    
    def get_cumulative_metrics(self) -> Dict[str, float]:
        """Get cumulative metrics across all episodes."""
        return self.cumulative_metrics.copy()
    
    def reset_metrics(self):
        """Reset all metrics."""
        self.episode_metrics.clear()
        self.cumulative_metrics.clear()
    
    def _update_metrics(self, step_info: Dict[str, Any]):
        """Update metrics based on step information."""
        # Deadlock detection
        if "deadlock" in step_info:
            self.episode_metrics["deadlocks"] = self.episode_metrics.get("deadlocks", 0) + 1
        
        # Coverage metrics
        if "coverage" in step_info:
            self.episode_metrics["coverage"] = step_info["coverage"]
        
        # Cooperation metrics
        if "cooperation_events" in step_info:
            self.episode_metrics["cooperation"] = self.episode_metrics.get("cooperation", 0) + step_info["cooperation_events"]
        
        # Collision metrics
        if "collisions" in step_info:
            self.episode_metrics["collisions"] = self.episode_metrics.get("collisions", 0) + step_info["collisions"]
    
    def _finalize_episode_metrics(self):
        """Finalize metrics at episode end and update cumulatives."""
        # Episode-level metrics
        self.episode_metrics["episode_length"] = self.current_step
        self.episode_metrics["success_rate"] = self.episode_metrics.get("success", 0)
        
        # Update cumulative metrics
        for key, value in self.episode_metrics.items():
            if key not in self.cumulative_metrics:
                self.cumulative_metrics[key] = []
            self.cumulative_metrics[key].append(value)
        
        # Compute running averages (create copy to avoid modification during iteration)
        metrics_copy = dict(self.cumulative_metrics)
        for key, values in metrics_copy.items():
            if isinstance(values, list) and len(values) > 0 and not key.endswith(('_mean', '_std')):
                self.cumulative_metrics[f"{key}_mean"] = np.mean(values)
                self.cumulative_metrics[f"{key}_std"] = np.std(values)
    
    # Agent Management
    def get_active_agents(self) -> List[str]:
        """Get list of currently active agent IDs."""
        return list(self.active_agents)
    
    def disable_agent(self, agent_id: str):
        """Disable an agent (for failure injection)."""
        if agent_id in self.active_agents:
            self.active_agents.remove(agent_id)
            logger.info(f"Agent {agent_id} disabled")
    
    def enable_agent(self, agent_id: str):
        """Re-enable a disabled agent."""
        if agent_id in self.agent_ids and agent_id not in self.active_agents:
            self.active_agents.add(agent_id)
            logger.info(f"Agent {agent_id} enabled")
    
    # Utility Methods
    def get_env_info(self) -> Dict[str, Any]:
        """Get environment information for debugging."""
        return {
            "num_agents": self.num_agents,
            "episode_limit": self.episode_limit,
            "current_step": self.current_step,
            "episode_count": self.episode_count,
            "total_steps": self.total_steps,
            "active_agents": len(self.active_agents),
            "action_space": str(self.action_space),
            "observation_space": str(self.observation_space)
        }
    
    def is_episode_done(self) -> bool:
        """Check if episode should terminate."""
        return (
            self.current_step >= self.episode_limit or
            len(self.active_agents) == 0
        )
    
    @property
    def unwrapped(self):
        """Return the base environment."""
        return self