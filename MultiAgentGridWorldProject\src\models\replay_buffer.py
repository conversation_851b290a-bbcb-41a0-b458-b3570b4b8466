"""
Replay Buffer

Store transitions for learning with support for:
- Standard RL transitions
- HRL option-aware fields
- Prioritized experience replay
- Multi-agent coordination
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Union
from collections import deque, namedtuple
import random
import logging

logger = logging.getLogger(__name__)

# Define transition types
Transition = namedtuple('Transition', [
    'observation', 'action', 'reward', 'next_observation', 'done'
])

HierarchicalTransition = namedtuple('HierarchicalTransition', [
    'observation', 'action', 'reward', 'next_observation', 'done',
    'option', 'option_duration', 'termination', 'option_reward'
])

MultiAgentTransition = namedtuple('MultiAgentTransition', [
    'observations', 'actions', 'rewards', 'next_observations', 'dones',
    'agent_masks'
])


class ReplayBuffer:
    """
    Standard replay buffer for reinforcement learning.
    
    Stores transitions and provides sampling functionality
    with optional prioritization.
    """
    
    def __init__(self, capacity: int, config: Optional[Dict[str, Any]] = None):
        self.capacity = capacity
        self.config = config or {}
        
        # Storage
        self.buffer = deque(maxlen=capacity)
        self.position = 0
        
        # Sampling parameters
        self.batch_size = self.config.get("batch_size", 32)
        self.device = self.config.get("device", "cpu")
        
        logger.info(f"ReplayBuffer initialized: capacity={capacity}")
    
    def add(self, transition: Union[Transition, Dict[str, Any]]) -> None:
        """Add a transition to the buffer."""
        if isinstance(transition, dict):
            # Convert dict to Transition
            transition = Transition(
                observation=transition["observation"],
                action=transition["action"],
                reward=transition["reward"],
                next_observation=transition["next_observation"],
                done=transition["done"]
            )
        
        self.buffer.append(transition)
    
    def sample(self, batch_size: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """Sample a batch of transitions from the replay buffer.
        
        Randomly samples transitions and converts them to batched tensors suitable
        for training. Handles various observation types including tensors, numpy
        arrays, and dictionaries.
        
        Args:
            batch_size (int, optional): Number of transitions to sample. If ``None``,
                uses the default batch size from configuration. If the buffer contains
                fewer transitions than requested, returns all available transitions.
                Default: ``None``.
                
        Returns:
            Dict[str, torch.Tensor]: Batched transitions containing:
                - **observations** (torch.Tensor): Stacked observations with shape
                  ``[batch_size, obs_dim]`` or nested structure for dict observations.
                - **actions** (torch.Tensor): Action indices with shape ``[batch_size]``.
                - **rewards** (torch.Tensor): Reward values with shape ``[batch_size]``.
                - **next_observations** (torch.Tensor): Next state observations with
                  same structure as observations.
                - **dones** (torch.Tensor): Episode termination flags with shape
                  ``[batch_size]``.
                  
        Note:
            All tensors are moved to the device specified in the buffer configuration.
            Dictionary observations are recursively stacked while preserving structure.
        """
        if batch_size is None:
            batch_size = self.batch_size
        
        if len(self.buffer) < batch_size:
            batch_size = len(self.buffer)
        
        # Sample transitions
        transitions = random.sample(self.buffer, batch_size)
        
        # Convert to tensors
        batch = self._collate_transitions(transitions)
        
        return batch
    
    def _collate_transitions(self, transitions: List[Transition]) -> Dict[str, torch.Tensor]:
        """Convert list of transitions to batched tensors."""
        batch = {}
        
        # Stack observations
        observations = [t.observation for t in transitions]
        batch["observations"] = self._stack_observations(observations)
        
        # Stack next observations
        next_observations = [t.next_observation for t in transitions]
        batch["next_observations"] = self._stack_observations(next_observations)
        
        # Stack other fields
        batch["actions"] = torch.tensor([t.action for t in transitions], 
                                       dtype=torch.long, device=self.device)
        batch["rewards"] = torch.tensor([t.reward for t in transitions], 
                                       dtype=torch.float32, device=self.device)
        batch["dones"] = torch.tensor([t.done for t in transitions], 
                                     dtype=torch.bool, device=self.device)
        
        return batch
    
    def _stack_observations(self, observations: List[Any]) -> torch.Tensor:
        """Stack observations into tensor."""
        if isinstance(observations[0], torch.Tensor):
            return torch.stack(observations).to(self.device)
        elif isinstance(observations[0], np.ndarray):
            return torch.tensor(np.stack(observations), dtype=torch.float32, device=self.device)
        elif isinstance(observations[0], dict):
            # Handle dict observations
            stacked = {}
            for key in observations[0].keys():
                values = [obs[key] for obs in observations]
                stacked[key] = self._stack_observations(values)
            return stacked
        else:
            return torch.tensor(observations, dtype=torch.float32, device=self.device)
    
    def clear(self) -> None:
        """Clear the buffer."""
        self.buffer.clear()
        self.position = 0
    
    def __len__(self) -> int:
        return len(self.buffer)
    
    def is_ready(self, min_size: Optional[int] = None) -> bool:
        """Check if buffer has enough samples for training."""
        min_size = min_size or self.batch_size
        return len(self.buffer) >= min_size


class HierarchicalReplayBuffer(ReplayBuffer):
    """
    Replay buffer for hierarchical RL with option-aware storage.
    
    Stores additional fields for options, termination, and option rewards.
    """
    
    def __init__(self, capacity: int, config: Optional[Dict[str, Any]] = None):
        super().__init__(capacity, config)
        
        # HRL-specific parameters
        self.num_options = self.config.get("num_options", 6)
        self.store_option_trajectories = self.config.get("store_option_trajectories", True)
        
        # Option-level storage
        if self.store_option_trajectories:
            self.option_buffer = deque(maxlen=capacity // 4)  # Smaller capacity for option transitions
        
        logger.info(f"HierarchicalReplayBuffer initialized: capacity={capacity}, "
                   f"options={self.num_options}")
    
    def add(self, transition: Union[HierarchicalTransition, Dict[str, Any]]) -> None:
        """Add a hierarchical transition to the buffer."""
        if isinstance(transition, dict):
            # Convert dict to HierarchicalTransition
            transition = HierarchicalTransition(
                observation=transition["observation"],
                action=transition["action"],
                reward=transition["reward"],
                next_observation=transition["next_observation"],
                done=transition["done"],
                option=transition.get("option", 0),
                option_duration=transition.get("option_duration", 1),
                termination=transition.get("termination", False),
                option_reward=transition.get("option_reward", transition["reward"])
            )
        
        self.buffer.append(transition)
    
    def add_option_transition(self, option_transition: Dict[str, Any]) -> None:
        """Add an option-level transition."""
        if self.store_option_trajectories:
            self.option_buffer.append(option_transition)
    
    def sample(self, batch_size: Optional[int] = None, 
               level: str = "action") -> Dict[str, torch.Tensor]:
        """
        Sample transitions at specified level.
        
        Args:
            batch_size: Number of transitions to sample
            level: "action" for action-level, "option" for option-level
        """
        if level == "option" and self.store_option_trajectories:
            return self._sample_option_transitions(batch_size)
        else:
            return self._sample_action_transitions(batch_size)
    
    def _sample_action_transitions(self, batch_size: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """Sample action-level transitions."""
        if batch_size is None:
            batch_size = self.batch_size
        
        if len(self.buffer) < batch_size:
            batch_size = len(self.buffer)
        
        transitions = random.sample(self.buffer, batch_size)
        batch = self._collate_hierarchical_transitions(transitions)
        
        return batch
    
    def _sample_option_transitions(self, batch_size: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """Sample option-level transitions."""
        if not self.store_option_trajectories or len(self.option_buffer) == 0:
            return {}
        
        if batch_size is None:
            batch_size = min(self.batch_size, len(self.option_buffer))
        
        batch_size = min(batch_size, len(self.option_buffer))
        transitions = random.sample(self.option_buffer, batch_size)
        
        # Convert to tensor format
        batch = {}
        for key in transitions[0].keys():
            values = [t[key] for t in transitions]
            if isinstance(values[0], torch.Tensor):
                batch[key] = torch.stack(values).to(self.device)
            else:
                batch[key] = torch.tensor(values, device=self.device)
        
        return batch
    
    def _collate_hierarchical_transitions(self, transitions: List[HierarchicalTransition]) -> Dict[str, torch.Tensor]:
        """Convert hierarchical transitions to batched tensors."""
        batch = {}
        
        # Standard fields
        observations = [t.observation for t in transitions]
        batch["observations"] = self._stack_observations(observations)
        
        next_observations = [t.next_observation for t in transitions]
        batch["next_observations"] = self._stack_observations(next_observations)
        
        batch["actions"] = torch.tensor([t.action for t in transitions], 
                                       dtype=torch.long, device=self.device)
        batch["rewards"] = torch.tensor([t.reward for t in transitions], 
                                       dtype=torch.float32, device=self.device)
        batch["dones"] = torch.tensor([t.done for t in transitions], 
                                     dtype=torch.bool, device=self.device)
        
        # Hierarchical fields
        batch["options"] = torch.tensor([t.option for t in transitions], 
                                       dtype=torch.long, device=self.device)
        batch["option_durations"] = torch.tensor([t.option_duration for t in transitions], 
                                                dtype=torch.long, device=self.device)
        batch["terminations"] = torch.tensor([t.termination for t in transitions], 
                                           dtype=torch.bool, device=self.device)
        batch["option_rewards"] = torch.tensor([t.option_reward for t in transitions], 
                                              dtype=torch.float32, device=self.device)
        
        return batch
    
    def sample_by_option(self, option: int, batch_size: Optional[int] = None) -> Dict[str, torch.Tensor]:
        """Sample transitions for a specific option."""
        if batch_size is None:
            batch_size = self.batch_size
        
        # Filter transitions by option
        option_transitions = [t for t in self.buffer if t.option == option]
        
        if len(option_transitions) < batch_size:
            batch_size = len(option_transitions)
        
        if batch_size == 0:
            return {}
        
        transitions = random.sample(option_transitions, batch_size)
        return self._collate_hierarchical_transitions(transitions)


class PrioritizedReplayBuffer(ReplayBuffer):
    """
    Prioritized experience replay buffer.
    
    Samples transitions based on TD error priorities with importance sampling.
    """
    
    def __init__(self, capacity: int, config: Optional[Dict[str, Any]] = None):
        super().__init__(capacity, config)
        
        # Priority parameters
        self.alpha = self.config.get("priority_alpha", 0.6)  # Priority exponent
        self.beta = self.config.get("priority_beta", 0.4)    # Importance sampling exponent
        self.beta_increment = self.config.get("beta_increment", 0.001)
        self.max_beta = self.config.get("max_beta", 1.0)
        self.epsilon = self.config.get("priority_epsilon", 1e-6)  # Small constant to avoid zero priority
        
        # Priority storage
        self.priorities = np.zeros(capacity, dtype=np.float32)
        self.max_priority = 1.0
        
        logger.info(f"PrioritizedReplayBuffer initialized: capacity={capacity}, "
                   f"alpha={self.alpha}, beta={self.beta}")
    
    def add(self, transition: Union[Transition, Dict[str, Any]], 
            priority: Optional[float] = None) -> None:
        """Add transition with priority."""
        # Add transition to buffer
        super().add(transition)
        
        # Set priority
        if priority is None:
            priority = self.max_priority
        
        self.priorities[self.position] = priority
        self.max_priority = max(self.max_priority, priority)
        
        # Update position
        self.position = (self.position + 1) % self.capacity
    
    def sample(self, batch_size: Optional[int] = None) -> Tuple[Dict[str, torch.Tensor], np.ndarray, np.ndarray]:
        """
        Sample batch with priorities.
        
        Returns:
            Tuple of (batch, indices, weights)
        """
        if batch_size is None:
            batch_size = self.batch_size
        
        if len(self.buffer) < batch_size:
            batch_size = len(self.buffer)
        
        # Get valid priorities
        valid_size = len(self.buffer)
        priorities = self.priorities[:valid_size]
        
        # Compute sampling probabilities
        probs = priorities ** self.alpha
        probs = probs / probs.sum()
        
        # Sample indices
        indices = np.random.choice(valid_size, batch_size, p=probs)
        
        # Compute importance sampling weights
        weights = (valid_size * probs[indices]) ** (-self.beta)
        weights = weights / weights.max()  # Normalize by max weight
        
        # Get transitions
        transitions = [self.buffer[i] for i in indices]
        batch = self._collate_transitions(transitions)
        
        # Update beta
        self.beta = min(self.max_beta, self.beta + self.beta_increment)
        
        return batch, indices, weights
    
    def update_priorities(self, indices: np.ndarray, priorities: np.ndarray) -> None:
        """Update priorities for sampled transitions."""
        priorities = np.abs(priorities) + self.epsilon
        self.priorities[indices] = priorities
        self.max_priority = max(self.max_priority, priorities.max())


class MultiAgentReplayBuffer:
    """
    Replay buffer for multi-agent environments.
    
    Handles coordination between agents and supports both centralized
    and decentralized training paradigms.
    """
    
    def __init__(self, capacity: int, num_agents: int, config: Optional[Dict[str, Any]] = None):
        self.capacity = capacity
        self.num_agents = num_agents
        self.config = config or {}
        
        # Storage mode
        self.centralized = self.config.get("centralized", True)
        
        if self.centralized:
            # Single buffer for all agents
            self.buffer = deque(maxlen=capacity)
        else:
            # Separate buffer per agent
            self.buffers = {
                f"agent_{i}": deque(maxlen=capacity)
                for i in range(num_agents)
            }
        
        # Sampling parameters
        self.batch_size = self.config.get("batch_size", 32)
        self.device = self.config.get("device", "cpu")
        
        logger.info(f"MultiAgentReplayBuffer initialized: {num_agents} agents, "
                   f"centralized={self.centralized}, capacity={capacity}")
    
    def add(self, transition: Union[MultiAgentTransition, Dict[str, Any]]) -> None:
        """Add multi-agent transition."""
        if isinstance(transition, dict):
            transition = MultiAgentTransition(
                observations=transition["observations"],
                actions=transition["actions"],
                rewards=transition["rewards"],
                next_observations=transition["next_observations"],
                dones=transition["dones"],
                agent_masks=transition.get("agent_masks", {})
            )
        
        if self.centralized:
            self.buffer.append(transition)
        else:
            # Add to individual agent buffers
            for i in range(self.num_agents):
                agent_id = f"agent_{i}"
                if agent_id in transition.observations:
                    agent_transition = Transition(
                        observation=transition.observations[agent_id],
                        action=transition.actions[agent_id],
                        reward=transition.rewards[agent_id],
                        next_observation=transition.next_observations[agent_id],
                        done=transition.dones[agent_id]
                    )
                    self.buffers[agent_id].append(agent_transition)
    
    def sample(self, batch_size: Optional[int] = None, 
               agent_id: Optional[str] = None) -> Dict[str, torch.Tensor]:
        """Sample transitions for training."""
        if batch_size is None:
            batch_size = self.batch_size
        
        if self.centralized:
            return self._sample_centralized(batch_size)
        else:
            if agent_id is None:
                raise ValueError("agent_id required for decentralized sampling")
            return self._sample_decentralized(agent_id, batch_size)
    
    def _sample_centralized(self, batch_size: int) -> Dict[str, torch.Tensor]:
        """Sample from centralized buffer."""
        if len(self.buffer) < batch_size:
            batch_size = len(self.buffer)
        
        transitions = random.sample(self.buffer, batch_size)
        
        # Collate multi-agent transitions
        batch = {}
        
        # Get agent IDs from first transition
        agent_ids = list(transitions[0].observations.keys())
        
        for agent_id in agent_ids:
            agent_batch = {}
            
            # Stack observations for this agent
            observations = [t.observations[agent_id] for t in transitions]
            agent_batch["observations"] = self._stack_observations(observations)
            
            next_observations = [t.next_observations[agent_id] for t in transitions]
            agent_batch["next_observations"] = self._stack_observations(next_observations)
            
            # Stack other fields
            agent_batch["actions"] = torch.tensor([t.actions[agent_id] for t in transitions], 
                                                 dtype=torch.long, device=self.device)
            agent_batch["rewards"] = torch.tensor([t.rewards[agent_id] for t in transitions], 
                                                 dtype=torch.float32, device=self.device)
            agent_batch["dones"] = torch.tensor([t.dones[agent_id] for t in transitions], 
                                               dtype=torch.bool, device=self.device)
            
            batch[agent_id] = agent_batch
        
        return batch
    
    def _sample_decentralized(self, agent_id: str, batch_size: int) -> Dict[str, torch.Tensor]:
        """Sample from individual agent buffer."""
        if agent_id not in self.buffers:
            raise ValueError(f"Unknown agent_id: {agent_id}")
        
        buffer = self.buffers[agent_id]
        if len(buffer) < batch_size:
            batch_size = len(buffer)
        
        transitions = random.sample(buffer, batch_size)
        return self._collate_transitions(transitions)
    
    def _stack_observations(self, observations: List[Any]) -> torch.Tensor:
        """Stack observations into tensor."""
        if isinstance(observations[0], torch.Tensor):
            return torch.stack(observations).to(self.device)
        elif isinstance(observations[0], np.ndarray):
            return torch.tensor(np.stack(observations), dtype=torch.float32, device=self.device)
        else:
            return torch.tensor(observations, dtype=torch.float32, device=self.device)
    
    def _collate_transitions(self, transitions: List[Transition]) -> Dict[str, torch.Tensor]:
        """Convert transitions to batch format."""
        batch = {}
        
        observations = [t.observation for t in transitions]
        batch["observations"] = self._stack_observations(observations)
        
        next_observations = [t.next_observation for t in transitions]
        batch["next_observations"] = self._stack_observations(next_observations)
        
        batch["actions"] = torch.tensor([t.action for t in transitions], 
                                       dtype=torch.long, device=self.device)
        batch["rewards"] = torch.tensor([t.reward for t in transitions], 
                                       dtype=torch.float32, device=self.device)
        batch["dones"] = torch.tensor([t.done for t in transitions], 
                                     dtype=torch.bool, device=self.device)
        
        return batch
    
    def clear(self, agent_id: Optional[str] = None) -> None:
        """Clear buffers."""
        if self.centralized:
            self.buffer.clear()
        else:
            if agent_id is None:
                for buffer in self.buffers.values():
                    buffer.clear()
            else:
                self.buffers[agent_id].clear()
    
    def __len__(self) -> int:
        if self.centralized:
            return len(self.buffer)
        else:
            return sum(len(buffer) for buffer in self.buffers.values())