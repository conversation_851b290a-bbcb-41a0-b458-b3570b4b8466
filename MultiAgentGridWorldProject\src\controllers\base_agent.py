"""
Base Agent Interface

Defines the common interface used across all agent architectures.
This is agnostic to hierarchy and can be used for any policy architecture.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Tuple
import torch
import logging

logger = logging.getLogger(__name__)


class BaseAgent(ABC):
    """
    Abstract base class for all agent implementations.
    
    Provides a common interface for acting, learning, and state management
    that works across different policy architectures (flat, hierarchical, etc.).
    """
    
    def __init__(self, agent_id: str, config: Dict[str, Any]):
        """
        Initialize base agent.
        
        Args:
            agent_id: Unique identifier for this agent
            config: Configuration dictionary
        """
        self.agent_id = agent_id
        self.config = config
        
        # Episode tracking
        self.episode_step = 0
        self.total_steps = 0
        self.episode_reward = 0.0
        
        # State tracking
        self.last_observation = None
        self.last_action = None
        self.is_done = False
        
        logger.info(f"BaseAgent {agent_id} initialized")
    
    @abstractmethod
    def act(self, observation: Dict[str, torch.Tensor]) -> Dict[str, int]:
        """
        Select action(s) given current observation.
        
        Args:
            observation: Current observation from environment
            
        Returns:
            Dictionary mapping action types to action values
        """
        pass
    
    @abstractmethod
    def learn(self, buffer: Any) -> Dict[str, float]:
        """
        Update agent parameters using experience buffer.
        
        Args:
            buffer: Experience buffer containing transitions
            
        Returns:
            Dictionary of learning metrics (losses, etc.)
        """
        pass
    
    def step(self, reward: Optional[float] = None, done: Optional[bool] = None) -> None:
        """
        Update internal state after environment step.
        
        Args:
            reward: Reward received from last action
            done: Whether episode is finished
        """
        self.episode_step += 1
        self.total_steps += 1
        
        if reward is not None:
            self.episode_reward += reward
        
        if done is not None:
            self.is_done = done
    
    def reset(self) -> None:
        """Reset agent state for new episode."""
        self.episode_step = 0
        self.episode_reward = 0.0
        self.last_observation = None
        self.last_action = None
        self.is_done = False
        
        logger.debug(f"Agent {self.agent_id} reset for new episode")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get agent metrics for logging/monitoring."""
        return {
            "episode_step": self.episode_step,
            "total_steps": self.total_steps,
            "episode_reward": self.episode_reward,
            "is_done": self.is_done
        }
    
    def save_state(self) -> Dict[str, Any]:
        """Save agent state for checkpointing."""
        return {
            "agent_id": self.agent_id,
            "episode_step": self.episode_step,
            "total_steps": self.total_steps,
            "episode_reward": self.episode_reward,
            "config": self.config
        }
    
    def load_state(self, state: Dict[str, Any]) -> None:
        """Load agent state from checkpoint."""
        self.episode_step = state.get("episode_step", 0)
        self.total_steps = state.get("total_steps", 0)
        self.episode_reward = state.get("episode_reward", 0.0)
        
        logger.info(f"Agent {self.agent_id} loaded state: step={self.total_steps}")
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(id={self.agent_id}, steps={self.total_steps})"