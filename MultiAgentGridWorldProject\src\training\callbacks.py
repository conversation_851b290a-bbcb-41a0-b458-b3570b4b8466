"""
Training Callbacks

Handles auxiliary behaviors like model checkpointing, early stopping,
logging, and visualization during training.
"""

import os
import json
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from abc import ABC, abstractmethod
import logging
from collections import defaultdict

logger = logging.getLogger(__name__)


class Callback(ABC):
    """Base class for training callbacks."""
    
    @abstractmethod
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Called at the end of each epoch."""
        pass
    
    def on_training_start(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Called at the start of training."""
        return {}
    
    def on_training_end(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Called at the end of training."""
        return {}


class EarlyStopping(Callback):
    """
    Early stopping callback that monitors a metric and stops training
    when no improvement is observed.
    """
    
    def __init__(self, metric: str = "avg_reward", patience: int = 50, 
                 min_delta: float = 0.01, mode: str = "max"):
        """
        Initialize early stopping callback.
        
        Args:
            metric: Metric to monitor
            patience: Number of epochs to wait for improvement
            min_delta: Minimum change to qualify as improvement
            mode: "max" for maximizing metric, "min" for minimizing
        """
        self.metric = metric
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        
        self.best_value = float('-inf') if mode == "max" else float('inf')
        self.wait_count = 0
        self.stopped_epoch = 0
        
        logger.info(f"EarlyStopping initialized: metric={metric}, patience={patience}, "
                   f"min_delta={min_delta}, mode={mode}")
    
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Check for early stopping condition."""
        if self.metric not in metrics:
            return {}
        
        current_value = metrics[self.metric]
        
        if self.mode == "max":
            improved = current_value > self.best_value + self.min_delta
        else:
            improved = current_value < self.best_value - self.min_delta
        
        if improved:
            self.best_value = current_value
            self.wait_count = 0
            logger.debug(f"EarlyStopping: New best {self.metric}: {self.best_value:.4f}")
        else:
            self.wait_count += 1
            logger.debug(f"EarlyStopping: No improvement for {self.wait_count}/{self.patience} epochs")
        
        if self.wait_count >= self.patience:
            self.stopped_epoch = epoch
            logger.info(f"EarlyStopping triggered at epoch {epoch}. "
                       f"Best {self.metric}: {self.best_value:.4f}")
            return {"stop_training": True}
        
        return {}


class ModelCheckpoint(Callback):
    """
    Model checkpointing callback that saves model states periodically
    and keeps track of the best performing model.
    """
    
    def __init__(self, checkpoint_dir: Union[str, Path], save_frequency: int = 10,
                 save_best: bool = True, metric: str = "avg_reward", mode: str = "max"):
        """
        Initialize model checkpoint callback.
        
        Args:
            checkpoint_dir: Directory to save checkpoints
            save_frequency: Save checkpoint every N epochs
            save_best: Whether to save the best model separately
            metric: Metric to use for determining best model
            mode: "max" for maximizing metric, "min" for minimizing
        """
        self.checkpoint_dir = Path(checkpoint_dir)
        self.save_frequency = save_frequency
        self.save_best = save_best
        self.metric = metric
        self.mode = mode
        
        self.best_value = float('-inf') if mode == "max" else float('inf')
        self.best_epoch = 0
        
        # Create checkpoint directory
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"ModelCheckpoint initialized: dir={checkpoint_dir}, "
                   f"frequency={save_frequency}, metric={metric}")
    
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Save checkpoints based on frequency and best performance."""
        results = {}
        
        # Regular checkpoint saving
        if epoch % self.save_frequency == 0:
            self._save_checkpoint(epoch, agents, "regular")
            results["checkpoint_saved"] = True
        
        # Best model saving
        if self.save_best and self.metric in metrics:
            current_value = metrics[self.metric]
            
            if self.mode == "max":
                is_best = current_value > self.best_value
            else:
                is_best = current_value < self.best_value
            
            if is_best:
                self.best_value = current_value
                self.best_epoch = epoch
                self._save_checkpoint(epoch, agents, "best")
                results["best_model_saved"] = True
                logger.info(f"New best model saved: {self.metric}={self.best_value:.4f}")
        
        return results
    
    def _save_checkpoint(self, epoch: int, agents: Dict[str, Any], checkpoint_type: str):
        """Save checkpoint to disk."""
        timestamp = checkpoint_type if checkpoint_type == "best" else f"epoch_{epoch}"
        
        for agent_id, agent in agents.items():
            # Save agent state
            agent_checkpoint = {
                "epoch": epoch,
                "agent_state": agent.save_state(),
                "best_metric": self.best_value,
                "best_epoch": self.best_epoch
            }
            
            # Save policy parameters if available
            if hasattr(agent.policy, 'state_dict'):
                agent_checkpoint["policy_state_dict"] = agent.policy.state_dict()
            
            # Save individual policy components
            if hasattr(agent.policy, 'option_policy'):
                agent_checkpoint["option_policy_state_dict"] = agent.policy.option_policy.state_dict()
            if hasattr(agent.policy, 'worker_policy'):
                agent_checkpoint["worker_policy_state_dict"] = agent.policy.worker_policy.state_dict()
            if hasattr(agent.policy, 'termination_fn'):
                agent_checkpoint["termination_fn_state_dict"] = agent.policy.termination_fn.state_dict()
            
            # Save communication module if available
            if hasattr(agent, 'comm_module') and agent.comm_module is not None:
                agent_checkpoint["comm_module_state_dict"] = agent.comm_module.state_dict()
            
            checkpoint_path = self.checkpoint_dir / f"{agent_id}_{timestamp}.pt"
            torch.save(agent_checkpoint, checkpoint_path)
        
        logger.debug(f"Checkpoint saved: {timestamp}")


class MetricsLogger(Callback):
    """
    Metrics logging callback that tracks and saves training metrics.
    """
    
    def __init__(self, log_dir: Union[str, Path], plot_dir: Optional[Union[str, Path]] = None,
                 save_frequency: int = 10, plot_frequency: int = 50):
        """
        Initialize metrics logger.
        
        Args:
            log_dir: Directory to save log files
            plot_dir: Directory to save plots (optional)
            save_frequency: Save metrics every N epochs
            plot_frequency: Generate plots every N epochs
        """
        self.log_dir = Path(log_dir)
        self.plot_dir = Path(plot_dir) if plot_dir else None
        self.save_frequency = save_frequency
        self.plot_frequency = plot_frequency
        
        # Create directories
        self.log_dir.mkdir(parents=True, exist_ok=True)
        if self.plot_dir:
            self.plot_dir.mkdir(parents=True, exist_ok=True)
        
        # Metrics storage
        self.metrics_history = defaultdict(list)
        self.epoch_history = []
        
        logger.info(f"MetricsLogger initialized: log_dir={log_dir}")
    
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Log metrics and generate plots."""
        # Store metrics
        self.epoch_history.append(epoch)
        for key, value in metrics.items():
            if isinstance(value, (int, float, np.integer, np.floating)):
                self.metrics_history[key].append(float(value))
        
        results = {}
        
        # Save metrics to file
        if epoch % self.save_frequency == 0:
            self._save_metrics()
            results["metrics_saved"] = True
        
        # Generate plots
        if self.plot_dir and epoch % self.plot_frequency == 0:
            self._generate_plots()
            results["plots_generated"] = True
        
        return results
    
    def _save_metrics(self):
        """Save metrics to JSON file."""
        metrics_data = {
            "epochs": self.epoch_history,
            "metrics": dict(self.metrics_history)
        }
        
        metrics_file = self.log_dir / "metrics.json"
        with open(metrics_file, 'w') as f:
            json.dump(metrics_data, f, indent=2)
        
        # Also save as CSV for easy analysis
        import pandas as pd
        df_data = {"epoch": self.epoch_history}
        df_data.update(self.metrics_history)
        
        # Pad shorter lists with NaN
        max_len = max(len(values) for values in df_data.values())
        for key, values in df_data.items():
            if len(values) < max_len:
                df_data[key] = values + [np.nan] * (max_len - len(values))
        
        df = pd.DataFrame(df_data)
        csv_file = self.log_dir / "metrics.csv"
        df.to_csv(csv_file, index=False)
    
    def _generate_plots(self):
        """Generate training plots."""
        if not self.plot_dir:
            return
        
        # Reward plot
        if "avg_reward" in self.metrics_history:
            plt.figure(figsize=(10, 6))
            epochs = self.epoch_history[-len(self.metrics_history["avg_reward"]):]
            rewards = self.metrics_history["avg_reward"]
            
            plt.plot(epochs, rewards, 'b-', linewidth=2, label='Average Reward')
            
            if "std_reward" in self.metrics_history:
                stds = self.metrics_history["std_reward"][-len(rewards):]
                plt.fill_between(epochs, 
                               np.array(rewards) - np.array(stds),
                               np.array(rewards) + np.array(stds),
                               alpha=0.3, color='blue')
            
            plt.xlabel('Epoch')
            plt.ylabel('Average Reward')
            plt.title('Training Progress - Reward')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig(self.plot_dir / "reward_progress.png", dpi=150, bbox_inches='tight')
            plt.close()
        
        # Loss plots
        loss_metrics = [k for k in self.metrics_history.keys() if "loss" in k.lower()]
        if loss_metrics:
            n_plots = len(loss_metrics)
            n_cols = min(3, n_plots)
            n_rows = (n_plots + n_cols - 1) // n_cols
            
            plt.figure(figsize=(5 * n_cols, 4 * n_rows))
            
            for i, loss_key in enumerate(loss_metrics):
                plt.subplot(n_rows, n_cols, i + 1)
                epochs = self.epoch_history[-len(self.metrics_history[loss_key]):]
                losses = self.metrics_history[loss_key]
                
                plt.plot(epochs, losses, linewidth=2)
                plt.title(loss_key.replace('_', ' ').title())
                plt.xlabel('Epoch')
                plt.ylabel('Loss')
                plt.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig(self.plot_dir / "loss_progress.png", dpi=150, bbox_inches='tight')
            plt.close()
        
        # Option usage plot
        option_metrics = [k for k in self.metrics_history.keys() if "option_usage" in k]
        if option_metrics:
            plt.figure(figsize=(12, 6))
            
            for option_key in option_metrics:
                epochs = self.epoch_history[-len(self.metrics_history[option_key]):]
                usage = self.metrics_history[option_key]
                
                label = option_key.replace("option_usage_", "").replace("_", " ")
                plt.plot(epochs, usage, linewidth=2, label=label)
            
            plt.xlabel('Epoch')
            plt.ylabel('Usage Frequency')
            plt.title('Option Usage Over Time')
            plt.legend()
            plt.grid(True, alpha=0.3)
            plt.savefig(self.plot_dir / "option_usage.png", dpi=150, bbox_inches='tight')
            plt.close()


class WandbLogger(Callback):
    """
    Weights & Biases logging callback for experiment tracking.
    """
    
    def __init__(self, project: str, name: Optional[str] = None, 
                 config: Optional[Dict[str, Any]] = None, tags: Optional[List[str]] = None):
        """
        Initialize Weights & Biases logger.
        
        Args:
            project: W&B project name
            name: Run name (optional)
            config: Configuration to log
            tags: Tags for the run
        """
        try:
            import wandb
            self.wandb = wandb
            self.enabled = True
        except ImportError:
            logger.warning("wandb not installed. WandbLogger will be disabled.")
            self.enabled = False
            return
        
        self.project = project
        self.name = name
        self.config = config
        self.tags = tags or []
        
        logger.info(f"WandbLogger initialized: project={project}")
    
    def on_training_start(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Initialize W&B run."""
        if not self.enabled:
            return {}
        
        # Merge configs
        full_config = self.config or {}
        full_config.update(config)
        
        self.wandb.init(
            project=self.project,
            name=self.name,
            config=full_config,
            tags=self.tags
        )
        
        logger.info("W&B run initialized")
        return {}
    
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Log metrics to W&B."""
        if not self.enabled:
            return {}
        
        # Filter numeric metrics
        wandb_metrics = {}
        for key, value in metrics.items():
            if isinstance(value, (int, float, np.integer, np.floating)):
                wandb_metrics[key] = float(value)
        
        self.wandb.log(wandb_metrics, step=epoch)
        
        return {}
    
    def on_training_end(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Finish W&B run."""
        if not self.enabled:
            return {}
        
        self.wandb.finish()
        logger.info("W&B run finished")
        return {}


class CallbackManager:
    """
    Manages multiple callbacks and coordinates their execution.
    """
    
    def __init__(self, callbacks: List[Callback]):
        """
        Initialize callback manager.
        
        Args:
            callbacks: List of callback instances
        """
        self.callbacks = callbacks
        logger.info(f"CallbackManager initialized with {len(callbacks)} callbacks")
    
    def on_training_start(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Call on_training_start for all callbacks."""
        results = {}
        for callback in self.callbacks:
            try:
                callback_result = callback.on_training_start(config)
                results.update(callback_result)
            except Exception as e:
                logger.error(f"Error in callback {callback.__class__.__name__}.on_training_start: {e}")
        
        return results
    
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Call on_epoch_end for all callbacks."""
        results = {}
        for callback in self.callbacks:
            try:
                callback_result = callback.on_epoch_end(epoch, metrics, agents)
                results.update(callback_result)
                
                # Check for early stopping
                if callback_result.get("stop_training", False):
                    results["stop_training"] = True
                    break
                    
            except Exception as e:
                logger.error(f"Error in callback {callback.__class__.__name__}.on_epoch_end: {e}")
        
        return results
    
    def on_training_end(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Call on_training_end for all callbacks."""
        callback_results = {}
        for callback in self.callbacks:
            try:
                callback_result = callback.on_training_end(results)
                callback_results.update(callback_result)
            except Exception as e:
                logger.error(f"Error in callback {callback.__class__.__name__}.on_training_end: {e}")
        
        return callback_results


class ProgressLogger(Callback):
    """
    Simple progress logging callback that prints training progress.
    """
    
    def __init__(self, log_frequency: int = 1):
        """
        Initialize progress logger.
        
        Args:
            log_frequency: Log progress every N epochs
        """
        self.log_frequency = log_frequency
        self.start_time = None
    
    def on_training_start(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """Record training start time."""
        import time
        self.start_time = time.time()
        logger.info("Training started")
        return {}
    
    def on_epoch_end(self, epoch: int, metrics: Dict[str, Any], agents: Dict[str, Any]) -> Dict[str, Any]:
        """Log training progress."""
        if epoch % self.log_frequency == 0:
            import time
            elapsed = time.time() - self.start_time if self.start_time else 0
            
            log_parts = [f"Epoch {epoch}"]
            
            # Add key metrics
            if "avg_reward" in metrics:
                log_parts.append(f"Reward: {metrics['avg_reward']:.3f}")
            if "total_loss" in metrics:
                log_parts.append(f"Loss: {metrics['total_loss']:.4f}")
            
            log_parts.append(f"Time: {elapsed:.1f}s")
            
            logger.info(" | ".join(log_parts))
        
        return {}