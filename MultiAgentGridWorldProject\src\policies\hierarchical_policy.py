"""
Hierarchical Policy

Orchestrates the complete hierarchical decision-making process by coordinating
option selection (π^H), worker execution (π^L_z), and termination decisions (β_z).
"""

import torch
import torch.nn as nn
from typing import Dict, Tuple, Optional, Any, List
import numpy as np
import logging

from .option_policy import OptionPolicy, MultiAgentOptionPolicy
from .worker_policy import WorkerPolicy, MultiAgentWorkerPolicy
from .termination_fn import TerminationFunction, MultiAgentTerminationFunction

logger = logging.getLogger(__name__)


class HierarchicalPolicy(nn.Module):
    """
    Complete hierarchical policy system.
    
    Coordinates option selection, worker execution, and termination decisions
    for a single agent with temporal abstraction.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__()
        
        self.config = config
        
        # Hierarchical parameters
        self.option_freq = config.get("option_freq", 8)  # K steps
        self.num_options = config.get("num_options", 6)
        
        # Initialize policy components
        self.option_policy = OptionPolicy(config)
        self.worker_policy = WorkerPolicy(config)
        self.termination_fn = TerminationFunction(config)
        
        # State tracking
        self.current_option = None
        self.option_step_count = 0
        self.option_history = []
        self.action_history = []
        
        # Metrics tracking
        self.option_switches = 0
        self.total_steps = 0
        self.option_durations = {i: [] for i in range(self.num_options)}
        
        logger.info(f"HierarchicalPolicy initialized: {self.num_options} options, "
                   f"K={self.option_freq} steps")
    
    def forward(self, observations: torch.Tensor,
                deterministic: bool = False,
                force_option_selection: bool = False) -> Dict[str, torch.Tensor]:
        """
        Forward pass through hierarchical policy.
        
        Args:
            observations: Current observations [batch_size, obs_dim]
            deterministic: Whether to use deterministic policies
            force_option_selection: Force new option selection regardless of timing
            
        Returns:
            Dictionary containing:
                - actions: Selected primitive actions
                - current_options: Currently active options
                - option_changed: Whether option was changed this step
                - option_log_probs: Log probabilities of option selection
                - action_log_probs: Log probabilities of action selection
                - termination_probs: Termination probabilities
                - values: Value estimates
        """
        batch_size = observations.shape[0]
        
        # Check if we need to select new options
        need_option_selection = (
            self.current_option is None or
            self.option_step_count >= self.option_freq or
            force_option_selection
        )
        
        # Option selection phase
        if need_option_selection:
            option_result = self.option_policy(observations, deterministic=deterministic)
            new_options = option_result["selected_options"]
            option_log_probs = option_result["option_log_probs"]
            option_values = option_result["option_values"]
            option_changed = True
            
            # Update current option
            if self.current_option is not None:
                # Record duration of previous option
                prev_option = self.current_option.item() if isinstance(self.current_option, torch.Tensor) else self.current_option
                self.option_durations[prev_option].append(self.option_step_count)
                self.option_switches += 1
            
            self.current_option = new_options
            self.option_step_count = 0
            
        else:
            # Continue with current option
            new_options = self.current_option
            option_log_probs = torch.zeros(batch_size, device=observations.device)
            option_values = torch.zeros(batch_size, self.num_options, device=observations.device)
            option_changed = False
        
        # Worker execution phase
        worker_result = self.worker_policy(
            observations, 
            new_options, 
            deterministic=deterministic
        )
        
        actions = worker_result["actions"]
        action_log_probs = worker_result["action_log_probs"]
        worker_values = worker_result["values"]
        
        # Termination decision phase
        option_durations = torch.tensor([self.option_step_count], device=observations.device).repeat(batch_size)
        
        # Calculate worker entropy for termination decision
        worker_entropy = None
        if "action_probs" in worker_result and worker_result["action_probs"] is not None:
            action_probs = worker_result["action_probs"]
            worker_entropy = -torch.sum(action_probs * torch.log(action_probs + 1e-8), dim=-1)
        
        termination_result = self.termination_fn(
            observations,
            new_options,
            option_durations=option_durations,
            worker_entropy=worker_entropy
        )
        
        termination_probs = termination_result["termination_probs"]
        should_terminate = termination_result["should_terminate"]
        
        # Force termination if needed
        if should_terminate.any():
            self.option_step_count = self.option_freq  # Force option selection next step
        
        # Update step count
        self.option_step_count += 1
        self.total_steps += 1
        
        # Update history
        self.option_history.append(new_options.clone() if isinstance(new_options, torch.Tensor) else new_options)
        self.action_history.append(actions.clone() if isinstance(actions, torch.Tensor) else actions)
        
        return {
            "actions": actions,
            "current_options": new_options,
            "option_changed": option_changed,
            "option_log_probs": option_log_probs,
            "action_log_probs": action_log_probs,
            "termination_probs": termination_probs,
            "should_terminate": should_terminate,
            "option_values": option_values,
            "worker_values": worker_values,
            "option_step_count": self.option_step_count
        }
    
    def reset(self):
        """Reset policy state for new episode."""
        if self.current_option is not None:
            # Record final option duration
            prev_option = self.current_option.item() if isinstance(self.current_option, torch.Tensor) else self.current_option
            self.option_durations[prev_option].append(self.option_step_count)
        
        self.current_option = None
        self.option_step_count = 0
        self.option_history.clear()
        self.action_history.clear()
    
    def get_metrics(self) -> Dict[str, float]:
        """Get hierarchical policy metrics."""
        metrics = {
            "option_switches": self.option_switches,
            "total_steps": self.total_steps,
            "avg_option_duration": np.mean([
                np.mean(durations) if durations else 0 
                for durations in self.option_durations.values()
            ]),
            "option_switch_rate": self.option_switches / max(1, self.total_steps)
        }
        
        # Per-option metrics
        for option_id, durations in self.option_durations.items():
            if durations:
                metrics[f"option_{option_id}_avg_duration"] = np.mean(durations)
                metrics[f"option_{option_id}_usage_count"] = len(durations)
        
        return metrics
    
    def get_option_usage_distribution(self) -> Dict[int, float]:
        """Get distribution of option usage."""
        total_usage = sum(len(durations) for durations in self.option_durations.values())
        if total_usage == 0:
            return {i: 0.0 for i in range(self.num_options)}
        
        return {
            option_id: len(durations) / total_usage
            for option_id, durations in self.option_durations.items()
        }


class MultiAgentHierarchicalPolicy(nn.Module):
    """
    Multi-agent hierarchical policy system.
    
    Manages hierarchical policies for multiple agents with optional
    parameter sharing and coordination mechanisms.
    """
    
    def __init__(self, config: Dict[str, Any], num_agents: int):
        super().__init__()
        
        self.config = config
        self.num_agents = num_agents
        self.parameter_sharing = config.get("parameter_sharing", False)
        
        # Initialize multi-agent policy components
        self.option_policy = MultiAgentOptionPolicy(config, num_agents)
        self.worker_policy = MultiAgentWorkerPolicy(config, num_agents)
        self.termination_fn = MultiAgentTerminationFunction(config, num_agents)
        
        # Agent state tracking
        self.agent_states = {}
        for i in range(num_agents):
            agent_id = f"agent_{i}"
            self.agent_states[agent_id] = {
                "current_option": None,
                "option_step_count": 0,
                "option_history": [],
                "action_history": [],
                "option_switches": 0,
                "total_steps": 0,
                "option_durations": {j: [] for j in range(config.get("num_options", 6))}
            }
        
        # Coordination mechanisms
        self.use_coordination = config.get("use_coordination", False)
        self.coordination_type = config.get("coordination_type", "attention")
        
        if self.use_coordination:
            self._build_coordination_module()
        
        logger.info(f"MultiAgentHierarchicalPolicy initialized: {num_agents} agents, "
                   f"parameter sharing: {self.parameter_sharing}, "
                   f"coordination: {self.use_coordination}")
    
    def _build_coordination_module(self):
        """Build coordination module for multi-agent option selection."""
        if self.coordination_type == "attention":
            # Simple attention-based coordination
            self.coordination_attention = nn.MultiheadAttention(
                embed_dim=self.config.get("hidden_dim", 256),
                num_heads=self.config.get("coordination_heads", 4),
                batch_first=True
            )
        elif self.coordination_type == "gnn":
            # Graph neural network for coordination
            # This would require additional GNN implementation
            pass
    
    def forward(self, observations: Dict[str, torch.Tensor],
                agent_ids: Optional[List[str]] = None,
                deterministic: bool = False,
                force_option_selection: Optional[Dict[str, bool]] = None) -> Dict[str, Dict[str, torch.Tensor]]:
        """
        Forward pass for multiple agents.
        
        Args:
            observations: Dict mapping agent_id to observations
            agent_ids: List of agent IDs to process (None for all)
            deterministic: Whether to use deterministic policies
            force_option_selection: Dict mapping agent_id to force option selection
            
        Returns:
            Dict mapping agent_id to hierarchical policy results
        """
        if agent_ids is None:
            agent_ids = list(observations.keys())
        
        # Determine which agents need option selection
        agents_need_options = {}
        for agent_id in agent_ids:
            if agent_id not in observations:
                continue
                
            state = self.agent_states[agent_id]
            force_selection = force_option_selection.get(agent_id, False) if force_option_selection else False
            
            agents_need_options[agent_id] = (
                state["current_option"] is None or
                state["option_step_count"] >= self.config.get("option_freq", 8) or
                force_selection
            )
        
        # Option selection phase
        option_results = {}
        if any(agents_need_options.values()):
            # Get observations for agents that need option selection
            option_obs = {
                agent_id: observations[agent_id] 
                for agent_id in agent_ids 
                if agents_need_options.get(agent_id, False) and agent_id in observations
            }
            
            if option_obs:
                option_results = self.option_policy(
                    option_obs, 
                    list(option_obs.keys()), 
                    deterministic=deterministic
                )
        
        # Update agent states with new options
        for agent_id in agent_ids:
            if agent_id not in observations:
                continue
                
            state = self.agent_states[agent_id]
            
            if agents_need_options.get(agent_id, False) and agent_id in option_results:
                # New option selected
                new_option = option_results[agent_id]["selected_options"]
                
                # Record previous option duration
                if state["current_option"] is not None:
                    prev_option = state["current_option"].item() if isinstance(state["current_option"], torch.Tensor) else state["current_option"]
                    state["option_durations"][prev_option].append(state["option_step_count"])
                    state["option_switches"] += 1
                
                state["current_option"] = new_option
                state["option_step_count"] = 0
            
            # Ensure current option exists
            if state["current_option"] is None:
                # Default to option 0 if no option selected
                state["current_option"] = torch.tensor([0], device=observations[agent_id].device)
        
        # Worker execution phase
        current_options = {
            agent_id: self.agent_states[agent_id]["current_option"]
            for agent_id in agent_ids
            if agent_id in observations
        }
        
        worker_results = self.worker_policy(
            observations, 
            current_options, 
            agent_ids, 
            deterministic=deterministic
        )
        
        # Termination decision phase
        option_durations = {
            agent_id: torch.tensor([self.agent_states[agent_id]["option_step_count"]], 
                                 device=observations[agent_id].device)
            for agent_id in agent_ids
            if agent_id in observations
        }
        
        # Calculate worker entropy for termination
        worker_entropy = {}
        for agent_id in agent_ids:
            if agent_id in worker_results and "action_probs" in worker_results[agent_id]:
                action_probs = worker_results[agent_id]["action_probs"]
                if action_probs is not None:
                    worker_entropy[agent_id] = -torch.sum(
                        action_probs * torch.log(action_probs + 1e-8), dim=-1
                    )
        
        termination_results = self.termination_fn(
            observations,
            current_options,
            agent_ids,
            option_durations=option_durations,
            worker_entropy=worker_entropy if worker_entropy else None
        )
        
        # Compile final results
        results = {}
        for agent_id in agent_ids:
            if agent_id not in observations:
                continue
                
            state = self.agent_states[agent_id]
            
            # Check for forced termination
            should_terminate = False
            if agent_id in termination_results:
                should_terminate = termination_results[agent_id]["should_terminate"].any()
            
            if should_terminate:
                state["option_step_count"] = self.config.get("option_freq", 8)  # Force option selection next step
            
            # Update step counts
            state["option_step_count"] += 1
            state["total_steps"] += 1
            
            # Update history
            state["option_history"].append(state["current_option"].clone())
            if agent_id in worker_results:
                state["action_history"].append(worker_results[agent_id]["actions"].clone())
            
            # Compile result for this agent
            agent_result = {
                "actions": worker_results[agent_id]["actions"] if agent_id in worker_results else None,
                "current_options": state["current_option"],
                "option_changed": agents_need_options.get(agent_id, False),
                "option_step_count": state["option_step_count"]
            }
            
            # Add option selection results if available
            if agent_id in option_results:
                agent_result.update({
                    "option_log_probs": option_results[agent_id]["option_log_probs"],
                    "option_values": option_results[agent_id]["option_values"]
                })
            else:
                agent_result.update({
                    "option_log_probs": torch.zeros(1, device=observations[agent_id].device),
                    "option_values": torch.zeros(self.config.get("num_options", 6), device=observations[agent_id].device)
                })
            
            # Add worker results
            if agent_id in worker_results:
                agent_result.update({
                    "action_log_probs": worker_results[agent_id]["action_log_probs"],
                    "worker_values": worker_results[agent_id]["values"]
                })
            
            # Add termination results
            if agent_id in termination_results:
                agent_result.update({
                    "termination_probs": termination_results[agent_id]["termination_probs"],
                    "should_terminate": termination_results[agent_id]["should_terminate"]
                })
            
            results[agent_id] = agent_result
        
        return results
    
    def reset(self, agent_ids: Optional[List[str]] = None):
        """Reset policy states for specified agents."""
        if agent_ids is None:
            agent_ids = list(self.agent_states.keys())
        
        for agent_id in agent_ids:
            if agent_id in self.agent_states:
                state = self.agent_states[agent_id]
                
                # Record final option duration
                if state["current_option"] is not None:
                    prev_option = state["current_option"].item() if isinstance(state["current_option"], torch.Tensor) else state["current_option"]
                    state["option_durations"][prev_option].append(state["option_step_count"])
                
                # Reset state
                state["current_option"] = None
                state["option_step_count"] = 0
                state["option_history"].clear()
                state["action_history"].clear()
    
    def get_metrics(self, agent_ids: Optional[List[str]] = None) -> Dict[str, Dict[str, float]]:
        """Get metrics for specified agents."""
        if agent_ids is None:
            agent_ids = list(self.agent_states.keys())
        
        metrics = {}
        for agent_id in agent_ids:
            if agent_id in self.agent_states:
                state = self.agent_states[agent_id]
                
                agent_metrics = {
                    "option_switches": state["option_switches"],
                    "total_steps": state["total_steps"],
                    "avg_option_duration": np.mean([
                        np.mean(durations) if durations else 0 
                        for durations in state["option_durations"].values()
                    ]),
                    "option_switch_rate": state["option_switches"] / max(1, state["total_steps"])
                }
                
                # Per-option metrics
                for option_id, durations in state["option_durations"].items():
                    if durations:
                        agent_metrics[f"option_{option_id}_avg_duration"] = np.mean(durations)
                        agent_metrics[f"option_{option_id}_usage_count"] = len(durations)
                
                metrics[agent_id] = agent_metrics
        
        return metrics
    
    def get_coordination_info(self) -> Dict[str, Any]:
        """Get information about agent coordination."""
        if not self.use_coordination:
            return {"coordination_enabled": False}
        
        # This would include coordination-specific metrics
        return {
            "coordination_enabled": True,
            "coordination_type": self.coordination_type,
            # Add more coordination metrics as needed
        }