"""
Environment Utilities

Utility functions and configurations for the environment module.
"""

import warnings
import logging

logger = logging.getLogger(__name__)


def suppress_environment_warnings():
    """Suppress common environment-related warnings that don't affect functionality."""
    
    # Suppress PettingZoo deprecation warnings (we handle both old and new imports)
    warnings.filterwarnings("ignore", category=DeprecationWarning, module="pettingzoo")
    
    # Suppress SWIG warnings (from gym/mujoco internals)
    warnings.filterwarnings("ignore", message=".*SwigPy.*has no __module__ attribute")
    warnings.filterwarnings("ignore", message=".*swigvarlink.*has no __module__ attribute")
    
    # Suppress other common warnings that don't affect functionality
    warnings.filterwarnings("ignore", category=DeprecationWarning, message=".*builtin type.*")
    
    logger.debug("Environment warnings suppressed for cleaner output")


def configure_environment_logging(level: str = "INFO"):
    """Configure logging for environment components."""
    
    # Set logging level for environment components
    env_loggers = [
        "src.environment.base_env",
        "src.environment.grid_world", 
        "src.environment.mpe_adapter",
        "src.environment.smac_adapter",
        "src.environment.dynamic_events",
        "src.environment.wrappers",
        "src.environment.curriculum"
    ]
    
    for logger_name in env_loggers:
        env_logger = logging.getLogger(logger_name)
        env_logger.setLevel(getattr(logging, level.upper()))
    
    logger.info(f"Environment logging configured to {level} level")


# Automatically suppress warnings when module is imported
suppress_environment_warnings()