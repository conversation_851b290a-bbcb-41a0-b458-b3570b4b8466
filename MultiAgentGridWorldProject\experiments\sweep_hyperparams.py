"""
Hyperparameter Sweeping System

Comprehensive hyperparameter optimization using Optuna or <PERSON> for hierarchical multi-agent RL.
Supports parallel trials, multi-seed evaluation, and automatic result aggregation.

Usage:
    python experiments/sweep_hyperparams.py task=gridworld method=optuna
    python experiments/sweep_hyperparams.py task=mpe method=ray n_trials=100
    python experiments/sweep_hyperparams.py task=smac method=optuna n_trials=50 n_seeds=5
    python experiments/sweep_hyperparams.py --config-path=../configs/sweeps --config-name=gridworld_sweep
"""

import os
import sys
import logging
import time
import json
import csv
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
import numpy as np
import pandas as pd

import hydra
from omegaconf import DictConfig, OmegaConf
import torch

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logger = logging.getLogger(__name__)


def set_seed(seed: int):
    """Set random seeds for reproducibility."""
    import random
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)


class OptunaSweeper:
    """Optuna-based hyperparameter optimization."""
    
    def __init__(self, config: Dict[str, Any]):
        try:
            import optuna
            self.optuna = optuna
        except ImportError:
            raise ImportError("Optuna not available. Install with: pip install optuna")
        
        self.config = config
        self.n_trials = config.get("n_trials", 100)
        self.n_seeds = config.get("n_seeds", 3)
        self.task = config.get("task", "gridworld")
        self.study_name = f"{self.task}_hrl_sweep_{int(time.time())}"
        
        # Results storage
        self.results_dir = Path(config.get("results_dir", "results")) / self.task / "sweeps"
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized Optuna sweeper: {self.study_name}")
    
    def define_search_space(self, trial) -> Dict[str, Any]:
        """Define hyperparameter search space."""
        search_space = {}
        
        # Learning rates
        search_space["training.learning_rate"] = trial.suggest_float("lr", 1e-5, 1e-2, log=True)
        search_space["training.option_learning_rate"] = trial.suggest_float("option_lr", 1e-5, 1e-2, log=True)
        search_space["training.worker_learning_rate"] = trial.suggest_float("worker_lr", 1e-5, 1e-2, log=True)
        
        # Architecture
        search_space["agent.policy.num_options"] = trial.suggest_int("num_options", 4, 12)
        search_space["agent.policy.hidden_dim"] = trial.suggest_categorical("hidden_dim", [64, 128, 256, 512])
        search_space["agent.policy.num_layers"] = trial.suggest_int("num_layers", 2, 4)
        
        # Training parameters
        search_space["training.gamma"] = trial.suggest_float("gamma", 0.95, 0.999)
        search_space["training.entropy_coef"] = trial.suggest_float("entropy_coef", 0.001, 0.1, log=True)
        search_space["training.value_coef"] = trial.suggest_float("value_coef", 0.1, 1.0)
        search_space["training.termination_coef"] = trial.suggest_float("termination_coef", 0.01, 0.5)
        
        # Option-specific parameters
        search_space["agent.policy.option_freq"] = trial.suggest_int("option_freq", 4, 16)
        search_space["agent.max_option_duration"] = trial.suggest_int("max_option_duration", 8, 32)
        
        # Communication (if multi-agent)
        if self.config.get("multi_agent", False):
            search_space["agent.communication.attention_heads"] = trial.suggest_categorical("attention_heads", [2, 4, 8])
            search_space["agent.communication.comm_dim"] = trial.suggest_categorical("comm_dim", [32, 64, 128])
            search_space["training.communication_coef"] = trial.suggest_float("comm_coef", 0.01, 0.5)
        
        return search_space
    
    def objective_function(self, trial) -> float:
        """Objective function for optimization."""
        # Get hyperparameters for this trial
        hyperparams = self.define_search_space(trial)
        
        # Run multiple seeds and average results
        seed_results = []
        for seed in range(self.n_seeds):
            try:
                result = self.run_single_trial(hyperparams, seed, trial.number)
                if result is not None:
                    seed_results.append(result)
            except Exception as e:
                logger.warning(f"Trial {trial.number}, seed {seed} failed: {e}")
                continue
        
        if not seed_results:
            logger.error(f"All seeds failed for trial {trial.number}")
            return float('-inf')
        
        # Return mean performance across seeds
        mean_reward = np.mean(seed_results)
        std_reward = np.std(seed_results)
        
        # Log trial results
        logger.info(f"Trial {trial.number}: {mean_reward:.3f} ± {std_reward:.3f}")
        
        # Store additional metrics
        trial.set_user_attr("mean_reward", mean_reward)
        trial.set_user_attr("std_reward", std_reward)
        trial.set_user_attr("n_seeds", len(seed_results))
        
        return mean_reward
    
    def run_single_trial(self, hyperparams: Dict[str, Any], seed: int, trial_num: int) -> Optional[float]:
        """Run a single trial with given hyperparameters."""
        try:
            # Clear any existing Hydra instance
            from hydra.core.global_hydra import GlobalHydra
            GlobalHydra.instance().clear()
            
            # Import experiment runner based on task
            if self.task == "gridworld":
                from experiments.run_gridworld import main as run_experiment
                config_name = "gridworld_default"
            elif self.task == "mpe":
                from experiments.run_mpe import main as run_experiment
                config_name = "mpe_default"
            elif self.task == "smac":
                from experiments.run_smac import main as run_experiment
                config_name = "smac_default"
            else:
                raise ValueError(f"Unknown task: {self.task}")
            
            # Create config with hyperparameters
            with hydra.initialize(config_path="../configs", version_base="1.3"):
                cfg = hydra.compose(config_name=config_name)
                
                # Apply hyperparameters
                for key, value in hyperparams.items():
                    OmegaConf.set(cfg, key, value)
                
                # Set seed and experiment name
                cfg.seed = seed
                cfg.experiment_name = f"{self.task}_sweep_trial_{trial_num}_seed_{seed}"
                cfg.results_dir = str(self.results_dir / f"trial_{trial_num}")
                
                # Reduce training time for sweeps
                cfg.training.num_epochs = self.config.get("sweep_epochs", 50)
                cfg.training.eval_frequency = max(1, cfg.training.num_epochs // 10)
                
                # Run experiment
                results = run_experiment(cfg)
                
                # Extract performance metric
                if results and "best_reward" in results:
                    return results["best_reward"]
                else:
                    logger.warning(f"No valid results for trial {trial_num}, seed {seed}")
                    return None
                    
        except Exception as e:
            logger.error(f"Trial {trial_num}, seed {seed} failed: {e}")
            return None
    
    def run_sweep(self) -> Dict[str, Any]:
        """Run the complete hyperparameter sweep."""
        logger.info(f"Starting Optuna sweep: {self.n_trials} trials, {self.n_seeds} seeds each")
        
        # Create study
        study = self.optuna.create_study(
            direction="maximize",
            study_name=self.study_name,
            storage=f"sqlite:///{self.results_dir}/optuna_study.db",
            load_if_exists=True
        )
        
        # Run optimization
        study.optimize(self.objective_function, n_trials=self.n_trials)
        
        # Save results
        results = self.save_results(study)
        
        logger.info(f"Sweep completed! Best reward: {study.best_value:.3f}")
        logger.info(f"Best params: {study.best_params}")
        
        return results
    
    def save_results(self, study) -> Dict[str, Any]:
        """Save sweep results to files."""
        # Best trial info
        best_trial = study.best_trial
        best_results = {
            "best_value": study.best_value,
            "best_params": study.best_params,
            "best_trial_number": best_trial.number,
            "n_trials": len(study.trials),
            "study_name": self.study_name
        }
        
        # Save best config
        best_config_path = self.results_dir / "best_config.yaml"
        with open(best_config_path, 'w') as f:
            OmegaConf.save(OmegaConf.create(study.best_params), f)
        
        # Save detailed results
        results_path = self.results_dir / "sweep_results.json"
        with open(results_path, 'w') as f:
            json.dump(best_results, f, indent=2)
        
        # Save trials dataframe
        df = study.trials_dataframe()
        df.to_csv(self.results_dir / "all_trials.csv", index=False)
        
        # Create summary report
        self.create_summary_report(study)
        
        logger.info(f"Results saved to {self.results_dir}")
        return best_results
    
    def create_summary_report(self, study):
        """Create a comprehensive summary report."""
        report_path = self.results_dir / "sweep_summary.md"
        
        with open(report_path, 'w') as f:
            f.write(f"# Hyperparameter Sweep Summary\n\n")
            f.write(f"**Task**: {self.task}\n")
            f.write(f"**Study**: {self.study_name}\n")
            f.write(f"**Trials**: {len(study.trials)}\n")
            f.write(f"**Seeds per trial**: {self.n_seeds}\n\n")
            
            f.write(f"## Best Results\n\n")
            f.write(f"**Best Reward**: {study.best_value:.4f}\n")
            f.write(f"**Best Trial**: {study.best_trial.number}\n\n")
            
            f.write(f"### Best Hyperparameters\n\n")
            for param, value in study.best_params.items():
                f.write(f"- **{param}**: {value}\n")
            
            f.write(f"\n## Top 10 Trials\n\n")
            top_trials = sorted(study.trials, key=lambda t: t.value or float('-inf'), reverse=True)[:10]
            
            f.write("| Trial | Reward | Params |\n")
            f.write("|-------|--------|--------|\n")
            for trial in top_trials:
                if trial.value is not None:
                    params_str = ", ".join([f"{k}={v}" for k, v in list(trial.params.items())[:3]])
                    f.write(f"| {trial.number} | {trial.value:.3f} | {params_str}... |\n")


class RayTuneSweeper:
    """Ray Tune-based hyperparameter optimization."""
    
    def __init__(self, config: Dict[str, Any]):
        try:
            import ray
            from ray import tune
            self.ray = ray
            self.tune = tune
        except ImportError:
            raise ImportError("Ray Tune not available. Install with: pip install ray[tune]")
        
        self.config = config
        self.n_trials = config.get("n_trials", 100)
        self.task = config.get("task", "gridworld")
        
        # Results storage
        self.results_dir = Path(config.get("results_dir", "results")) / self.task / "sweeps"
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"Initialized Ray Tune sweeper for {self.task}")
    
    def define_search_space(self) -> Dict[str, Any]:
        """Define Ray Tune search space."""
        search_space = {
            "training.learning_rate": self.tune.loguniform(1e-5, 1e-2),
            "training.option_learning_rate": self.tune.loguniform(1e-5, 1e-2),
            "training.worker_learning_rate": self.tune.loguniform(1e-5, 1e-2),
            "agent.policy.num_options": self.tune.randint(4, 13),
            "agent.policy.hidden_dim": self.tune.choice([64, 128, 256, 512]),
            "agent.policy.num_layers": self.tune.randint(2, 5),
            "training.gamma": self.tune.uniform(0.95, 0.999),
            "training.entropy_coef": self.tune.loguniform(0.001, 0.1),
            "training.value_coef": self.tune.uniform(0.1, 1.0),
            "training.termination_coef": self.tune.uniform(0.01, 0.5),
            "agent.policy.option_freq": self.tune.randint(4, 17),
            "agent.max_option_duration": self.tune.randint(8, 33),
        }
        
        # Add multi-agent parameters if needed
        if self.config.get("multi_agent", False):
            search_space.update({
                "agent.communication.attention_heads": self.tune.choice([2, 4, 8]),
                "agent.communication.comm_dim": self.tune.choice([32, 64, 128]),
                "training.communication_coef": self.tune.uniform(0.01, 0.5),
            })
        
        return search_space
    
    def run_sweep(self) -> Dict[str, Any]:
        """Run Ray Tune sweep."""
        logger.info(f"Starting Ray Tune sweep: {self.n_trials} trials")
        
        # Initialize Ray
        if not self.ray.is_initialized():
            self.ray.init()
        
        # Define trainable function
        def trainable(config):
            # Implementation would go here
            # This is a placeholder - in practice you'd run the experiment
            # and report metrics back to Ray Tune
            pass
        
        # Run tuning
        analysis = self.tune.run(
            trainable,
            config=self.define_search_space(),
            num_samples=self.n_trials,
            local_dir=str(self.results_dir),
            name=f"{self.task}_ray_sweep"
        )
        
        # Save results
        results = self.save_ray_results(analysis)
        return results
    
    def save_ray_results(self, analysis) -> Dict[str, Any]:
        """Save Ray Tune results."""
        best_config = analysis.get_best_config(metric="reward", mode="max")
        best_result = analysis.get_best_trial(metric="reward", mode="max").last_result
        
        results = {
            "best_config": best_config,
            "best_reward": best_result.get("reward", 0),
            "n_trials": len(analysis.trials)
        }
        
        # Save to file
        with open(self.results_dir / "ray_results.json", 'w') as f:
            json.dump(results, f, indent=2)
        
        return results


@hydra.main(config_path="../configs", config_name="sweep_default", version_base="1.3")
def main(cfg: DictConfig) -> None:
    """
    Main hyperparameter sweeping function.
    
    Args:
        cfg: Hydra configuration for sweeping
    """
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting Hyperparameter Sweep")
    logger.info(f"Configuration:\n{OmegaConf.to_yaml(cfg)}")
    
    # Convert config to dict
    config = OmegaConf.to_container(cfg, resolve=True)
    
    # Choose sweeping method
    method = config.get("method", "optuna").lower()
    
    try:
        if method == "optuna":
            sweeper = OptunaSweeper(config)
            results = sweeper.run_sweep()
        elif method == "ray":
            sweeper = RayTuneSweeper(config)
            results = sweeper.run_sweep()
        else:
            raise ValueError(f"Unknown sweeping method: {method}")
        
        # Print final summary
        print("\n" + "="*60)
        print("🎉 HYPERPARAMETER SWEEP COMPLETED!")
        print("="*60)
        print(f"🔍 Method: {method.upper()}")
        print(f"🎯 Task: {config.get('task', 'unknown')}")
        print(f"🔢 Trials: {results.get('n_trials', 'unknown')}")
        print(f"📊 Best Reward: {results.get('best_value', results.get('best_reward', 'unknown')):.3f}")
        print(f"📁 Results: {sweeper.results_dir}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"Hyperparameter sweep failed: {str(e)}")
        logger.exception("Full traceback:")
        raise


if __name__ == "__main__":
    main()