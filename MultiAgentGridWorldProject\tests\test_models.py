"""
Tests for Models Module

Tests all parametric models, layers, and buffers used by policies,
critics, communication, and replay systems.
"""

import pytest
import torch
import numpy as np
from unittest.mock import Mock, patch

from src.models.option_selector import OptionSelector, MultiAgentOptionSelector
from src.models.attention_module import (
    MultiHeadAttention, AttentionModule, CoordinationAttention, 
    OptionStateFusion, create_causal_mask, create_padding_mask
)
from src.models.critic_network import CriticNetwork, HierarchicalCritic, SharedCritic
from src.models.replay_buffer import (
    ReplayBuffer, HierarchicalReplayBuffer, PrioritizedReplayBuffer,
    MultiAgentReplayBuffer, Transition, HierarchicalTransition
)


class TestOptionSelector:
    """Test option selector functionality."""
    
    @pytest.fixture
    def config(self):
        return {
            "obs_dim": 64,
            "num_options": 4,
            "hidden_dim": 128,
            "num_layers": 2,
            "use_gumbel_softmax": True,
            "gumbel_temperature": 1.0
        }
    
    @pytest.fixture
    def selector(self, config):
        return OptionSelector(config)
    
    def test_option_selector_initialization(self, selector, config):
        """Test option selector initialization."""
        assert selector.obs_dim == config["obs_dim"]
        assert selector.num_options == config["num_options"]
        assert selector.hidden_dim == config["hidden_dim"]
        assert selector.use_gumbel_softmax == config["use_gumbel_softmax"]
    
    def test_option_selector_logits_shape(self, selector):
        """Test that option selector returns correct logits shape."""
        batch_size = 8
        observations = torch.randn(batch_size, selector.obs_dim)
        
        output = selector(observations)
        
        # Check output structure
        assert isinstance(output, dict)
        assert "option_logits" in output
        assert "option_probs" in output
        assert "selected_options" in output
        
        # Check shapes
        assert output["option_logits"].shape == (batch_size, selector.num_options)
        assert output["option_probs"].shape == (batch_size, selector.num_options)
        assert output["selected_options"].shape == (batch_size,)
        
        # Check probabilities sum to 1
        prob_sums = output["option_probs"].sum(dim=-1)
        assert torch.allclose(prob_sums, torch.ones(batch_size), atol=1e-6)
    
    def test_deterministic_selection(self, selector):
        """Test deterministic option selection."""
        batch_size = 4
        observations = torch.randn(batch_size, selector.obs_dim)
        
        output = selector(observations, deterministic=True)
        
        # Check that selected options are argmax of logits
        expected_options = torch.argmax(output["option_logits"], dim=-1)
        assert torch.equal(output["selected_options"], expected_options)
    
    def test_gumbel_softmax_sampling(self, selector):
        """Test Gumbel-softmax sampling."""
        batch_size = 4
        observations = torch.randn(batch_size, selector.obs_dim)
        
        output = selector(observations, deterministic=False)
        
        # Check that option_samples are soft (not one-hot)
        option_samples = output["option_samples"]
        assert option_samples.shape == (batch_size, selector.num_options)
        
        # Samples should sum to 1 (approximately)
        sample_sums = option_samples.sum(dim=-1)
        assert torch.allclose(sample_sums, torch.ones(batch_size), atol=1e-5)
    
    def test_temperature_update(self, selector):
        """Test temperature decay."""
        initial_temp = selector.gumbel_temperature
        selector.update_temperature()
        
        assert selector.gumbel_temperature < initial_temp
        assert selector.gumbel_temperature >= selector.min_temperature
    
    def test_entropy_computation(self, selector):
        """Test entropy computation."""
        batch_size = 4
        observations = torch.randn(batch_size, selector.obs_dim)
        
        entropy = selector.compute_entropy(observations)
        
        assert entropy.shape == (batch_size,)
        assert torch.all(entropy >= 0)  # Entropy should be non-negative
    
    def test_kl_divergence(self, selector):
        """Test KL divergence computation."""
        batch_size = 4
        observations = torch.randn(batch_size, selector.obs_dim)
        target_logits = torch.randn(batch_size, selector.num_options)
        
        kl_div = selector.compute_kl_divergence(observations, target_logits)
        
        assert isinstance(kl_div, torch.Tensor)
        assert kl_div.dim() == 0  # Scalar
        assert kl_div >= 0  # KL divergence is non-negative


class TestMultiAgentOptionSelector:
    """Test multi-agent option selector."""
    
    @pytest.fixture
    def config(self):
        return {
            "obs_dim": 64,
            "num_options": 4,
            "num_agents": 3,
            "shared_selector": True
        }
    
    @pytest.fixture
    def multi_selector(self, config):
        return MultiAgentOptionSelector(config)
    
    def test_shared_selector(self, multi_selector):
        """Test shared selector functionality."""
        observations = {
            "agent_0": torch.randn(2, 64),
            "agent_1": torch.randn(2, 64),
            "agent_2": torch.randn(2, 64)
        }
        
        outputs = multi_selector(observations)
        
        assert len(outputs) == 3
        for agent_id in observations.keys():
            assert agent_id in outputs
            assert "option_logits" in outputs[agent_id]
            assert outputs[agent_id]["option_logits"].shape == (2, 4)
    
    def test_separate_selectors(self, config):
        """Test separate selectors per agent."""
        config["shared_selector"] = False
        multi_selector = MultiAgentOptionSelector(config)
        
        observations = {
            "agent_0": torch.randn(2, 64),
            "agent_1": torch.randn(2, 64)
        }
        
        outputs = multi_selector(observations)
        
        assert len(outputs) == 2
        for agent_id in observations.keys():
            assert agent_id in outputs


class TestMultiHeadAttention:
    """Test multi-head attention mechanism."""
    
    @pytest.fixture
    def config(self):
        return {
            "d_model": 256,
            "num_heads": 8,
            "dropout": 0.1
        }
    
    @pytest.fixture
    def attention(self, config):
        return MultiHeadAttention(config)
    
    def test_attention_initialization(self, attention, config):
        """Test attention initialization."""
        assert attention.d_model == config["d_model"]
        assert attention.num_heads == config["num_heads"]
        assert attention.d_k == config["d_model"] // config["num_heads"]
    
    def test_self_attention(self, attention):
        """Test self-attention forward pass."""
        batch_size, seq_len = 4, 10
        query = torch.randn(batch_size, seq_len, attention.d_model)
        
        # Set to eval mode to disable dropout
        attention.eval()
        
        output, attn_weights = attention(query, return_attention=True)
        
        # Check output shape
        assert output.shape == (batch_size, seq_len, attention.d_model)
        
        # Check attention weights shape
        assert attn_weights.shape == (batch_size, attention.num_heads, seq_len, seq_len)
        
        # Attention weights should sum to 1 along last dimension (no dropout in eval mode)
        attn_sums = attn_weights.sum(dim=-1)
        expected_sums = torch.ones(batch_size, attention.num_heads, seq_len)
        assert torch.allclose(attn_sums, expected_sums, atol=1e-6)
    
    def test_cross_attention(self, attention):
        """Test cross-attention forward pass."""
        batch_size, seq_len_q, seq_len_k = 4, 8, 12
        query = torch.randn(batch_size, seq_len_q, attention.d_model)
        key = torch.randn(batch_size, seq_len_k, attention.d_model)
        value = torch.randn(batch_size, seq_len_k, attention.d_model)
        
        output, attn_weights = attention(query, key, value, return_attention=True)
        
        # Check output shape
        assert output.shape == (batch_size, seq_len_q, attention.d_model)
        
        # Check attention weights shape
        assert attn_weights.shape == (batch_size, attention.num_heads, seq_len_q, seq_len_k)
    
    def test_attention_mask(self, attention):
        """Test attention with mask."""
        batch_size, seq_len = 4, 8
        query = torch.randn(batch_size, seq_len, attention.d_model)
        
        # Create causal mask
        mask = create_causal_mask(seq_len, query.device)
        mask = mask.unsqueeze(0).expand(batch_size, -1, -1)
        
        output, attn_weights = attention(query, mask=mask, return_attention=True)
        
        # Check that attention weights respect the mask
        # Upper triangular part should be close to zero
        for b in range(batch_size):
            for h in range(attention.num_heads):
                upper_tri = torch.triu(attn_weights[b, h], diagonal=1)
                assert torch.allclose(upper_tri, torch.zeros_like(upper_tri), atol=1e-5)


class TestAttentionModule:
    """Test complete attention module."""
    
    @pytest.fixture
    def config(self):
        return {
            "d_model": 256,
            "num_heads": 8,
            "d_ff": 1024,
            "use_residual": True,
            "use_layer_norm": True,
            "use_ffn": True
        }
    
    @pytest.fixture
    def attention_module(self, config):
        return AttentionModule(config)
    
    def test_attention_module_output_dim(self, attention_module):
        """Test attention module output dimensions."""
        batch_size, seq_len = 4, 10
        x = torch.randn(batch_size, seq_len, attention_module.d_model)
        
        output, attn_weights = attention_module(x, return_attention=True)
        
        # Output should have same shape as input
        assert output.shape == x.shape
        
        # Check attention weights
        assert attn_weights.shape == (batch_size, attention_module.num_heads, seq_len, seq_len)
    
    def test_residual_connections(self, config):
        """Test residual connections."""
        config["use_residual"] = True
        attention_module = AttentionModule(config)
        
        batch_size, seq_len = 4, 10
        x = torch.randn(batch_size, seq_len, attention_module.d_model)
        
        # Forward pass
        output, _ = attention_module(x)
        
        # With residual connections, output should not be identical to input
        assert not torch.equal(output, x)
        
        # But should be related (this is a weak test, but checks basic functionality)
        assert output.shape == x.shape


class TestCoordinationAttention:
    """Test coordination attention for multi-agent systems."""
    
    @pytest.fixture
    def config(self):
        return {
            "num_agents": 4,
            "agent_dim": 128,
            "coordination_dim": 256,
            "coordination_heads": 4
        }
    
    @pytest.fixture
    def coord_attention(self, config):
        return CoordinationAttention(config)
    
    def test_coordination_attention(self, coord_attention):
        """Test coordination attention forward pass."""
        batch_size, num_agents = 2, coord_attention.num_agents
        agent_states = torch.randn(batch_size, num_agents, coord_attention.agent_dim)
        
        output, attn_weights = coord_attention(agent_states, return_attention=True)
        
        # Output should have same shape as input
        assert output.shape == agent_states.shape
        
        # Check attention weights
        assert attn_weights.shape == (batch_size, coord_attention.attention.num_heads, num_agents, num_agents)
    
    def test_agent_masking(self, coord_attention):
        """Test agent masking functionality."""
        batch_size, num_agents = 2, coord_attention.num_agents
        agent_states = torch.randn(batch_size, num_agents, coord_attention.agent_dim)
        
        # Create mask (first agent inactive)
        agent_mask = torch.ones(batch_size, num_agents)
        agent_mask[:, 0] = 0
        
        output, _ = coord_attention(agent_states, agent_mask=agent_mask)
        
        # Output should still have correct shape
        assert output.shape == agent_states.shape


class TestCriticNetwork:
    """Test critic network functionality."""
    
    @pytest.fixture
    def state_value_config(self):
        return {
            "obs_dim": 64,
            "hidden_dim": 128,
            "num_layers": 2,
            "critic_type": "state_value",
            "num_critics": 1
        }
    
    @pytest.fixture
    def action_value_config(self):
        return {
            "obs_dim": 64,
            "action_dim": 4,
            "hidden_dim": 128,
            "num_layers": 2,
            "critic_type": "action_value",
            "num_critics": 2
        }
    
    def test_state_value_critic(self, state_value_config):
        """Test state-value critic."""
        critic = CriticNetwork(state_value_config)
        
        batch_size = 8
        observations = torch.randn(batch_size, state_value_config["obs_dim"])
        
        output = critic(observations)
        
        assert "values" in output
        assert output["values"].shape == (batch_size, 1)  # Single critic
    
    def test_action_value_critic(self, action_value_config):
        """Test action-value critic."""
        critic = CriticNetwork(action_value_config)
        
        batch_size = 8
        observations = torch.randn(batch_size, action_value_config["obs_dim"])
        actions = torch.randn(batch_size, action_value_config["action_dim"])
        
        output = critic(observations, actions)
        
        assert "values" in output
        assert output["values"].shape == (batch_size, 2)  # Two critics (ensemble)
    
    def test_hierarchical_critic(self):
        """Test hierarchical critic with option conditioning."""
        config = {
            "obs_dim": 64,
            "action_dim": 4,
            "hidden_dim": 128,
            "critic_type": "action_value",
            "use_option_conditioning": True,
            "num_options": 6,
            "option_embed_dim": 32
        }
        
        critic = CriticNetwork(config)
        
        batch_size = 8
        observations = torch.randn(batch_size, config["obs_dim"])
        actions = torch.randn(batch_size, config["action_dim"])
        options = torch.randint(0, config["num_options"], (batch_size,))
        
        output = critic(observations, actions, options)
        
        assert "values" in output
        assert output["values"].shape == (batch_size, 1)
    
    def test_critic_scalar_output(self, state_value_config):
        """Test that critic returns scalar values."""
        critic = CriticNetwork(state_value_config)
        
        batch_size = 4
        observations = torch.randn(batch_size, state_value_config["obs_dim"])
        
        output = critic(observations)
        values = output["values"]
        
        # Values should be scalars (or single values per sample)
        assert values.dim() == 2  # [batch_size, num_critics]
        assert values.shape[1] == 1  # Single critic
        
        # Check that values are finite
        assert torch.isfinite(values).all()
    
    def test_target_computation(self, state_value_config):
        """Test TD target computation."""
        critic = CriticNetwork(state_value_config)
        
        batch_size = 4
        rewards = torch.randn(batch_size)
        next_values = torch.randn(batch_size)
        dones = torch.randint(0, 2, (batch_size,)).bool()
        gamma = 0.99
        
        targets = critic.compute_target(rewards, next_values, dones, gamma)
        
        assert targets.shape == (batch_size,)
        
        # Check target computation for non-terminal states
        non_terminal_mask = ~dones
        if non_terminal_mask.any():
            expected = rewards[non_terminal_mask] + gamma * next_values[non_terminal_mask]
            assert torch.allclose(targets[non_terminal_mask], expected)
    
    def test_loss_computation(self, state_value_config):
        """Test critic loss computation."""
        critic = CriticNetwork(state_value_config)
        
        batch_size = 4
        predictions = torch.randn(batch_size)
        targets = torch.randn(batch_size)
        
        # Test different loss types
        mse_loss = critic.compute_loss(predictions, targets, "mse")
        huber_loss = critic.compute_loss(predictions, targets, "huber")
        mae_loss = critic.compute_loss(predictions, targets, "mae")
        
        assert isinstance(mse_loss, torch.Tensor)
        assert isinstance(huber_loss, torch.Tensor)
        assert isinstance(mae_loss, torch.Tensor)
        
        # All losses should be non-negative
        assert mse_loss >= 0
        assert huber_loss >= 0
        assert mae_loss >= 0


class TestHierarchicalCritic:
    """Test hierarchical critic functionality."""
    
    @pytest.fixture
    def config(self):
        return {
            "obs_dim": 64,
            "action_dim": 4,
            "num_options": 6,
            "hidden_dim": 128,
            "num_option_critics": 1,
            "num_action_critics": 2
        }
    
    @pytest.fixture
    def hierarchical_critic(self, config):
        return HierarchicalCritic(config)
    
    def test_hierarchical_critic_initialization(self, hierarchical_critic, config):
        """Test hierarchical critic initialization."""
        assert hierarchical_critic.num_options == config["num_options"]
        assert hasattr(hierarchical_critic, "option_critic")
        assert hasattr(hierarchical_critic, "action_critic")
    
    def test_option_level_forward(self, hierarchical_critic):
        """Test option-level critic forward pass."""
        batch_size = 8
        observations = torch.randn(batch_size, 64)
        
        output = hierarchical_critic(observations, level="option")
        
        assert "option_values" in output
        assert output["option_values"].shape == (batch_size, 1)
    
    def test_action_level_forward(self, hierarchical_critic):
        """Test action-level critic forward pass."""
        batch_size = 8
        observations = torch.randn(batch_size, 64)
        actions = torch.randn(batch_size, 4)
        options = torch.randint(0, 6, (batch_size,))
        
        output = hierarchical_critic(observations, actions, options, level="action")
        
        assert "action_values" in output
        assert output["action_values"].shape == (batch_size, 2)  # Two action critics
    
    def test_both_levels_forward(self, hierarchical_critic):
        """Test both levels forward pass."""
        batch_size = 8
        observations = torch.randn(batch_size, 64)
        actions = torch.randn(batch_size, 4)
        options = torch.randint(0, 6, (batch_size,))
        
        output = hierarchical_critic(observations, actions, options, level="both")
        
        assert "option_values" in output
        assert "action_values" in output
        assert output["option_values"].shape == (batch_size, 1)
        assert output["action_values"].shape == (batch_size, 2)


class TestReplayBuffer:
    """Test replay buffer functionality."""
    
    @pytest.fixture
    def buffer(self):
        return ReplayBuffer(capacity=100, config={"batch_size": 16})
    
    def test_buffer_initialization(self, buffer):
        """Test buffer initialization."""
        assert buffer.capacity == 100
        assert len(buffer) == 0
        assert buffer.batch_size == 16
    
    def test_add_transition(self, buffer):
        """Test adding transitions."""
        transition = Transition(
            observation=torch.randn(4),
            action=1,
            reward=0.5,
            next_observation=torch.randn(4),
            done=False
        )
        
        buffer.add(transition)
        
        assert len(buffer) == 1
    
    def test_replay_buffer_add_sample(self, buffer):
        """Test adding and sampling transitions."""
        # Add multiple transitions
        for i in range(20):
            transition = Transition(
                observation=torch.randn(4),
                action=i % 4,
                reward=float(i),
                next_observation=torch.randn(4),
                done=i == 19
            )
            buffer.add(transition)
        
        assert len(buffer) == 20
        
        # Sample batch
        batch = buffer.sample(batch_size=8)
        
        assert isinstance(batch, dict)
        assert "observations" in batch
        assert "actions" in batch
        assert "rewards" in batch
        assert "next_observations" in batch
        assert "dones" in batch
        
        # Check batch shapes
        assert batch["observations"].shape == (8, 4)
        assert batch["actions"].shape == (8,)
        assert batch["rewards"].shape == (8,)
        assert batch["next_observations"].shape == (8, 4)
        assert batch["dones"].shape == (8,)
    
    def test_buffer_capacity(self):
        """Test buffer capacity limits."""
        buffer = ReplayBuffer(capacity=5)
        
        # Add more transitions than capacity
        for i in range(10):
            transition = Transition(
                observation=torch.tensor([i]),
                action=i,
                reward=float(i),
                next_observation=torch.tensor([i+1]),
                done=False
            )
            buffer.add(transition)
        
        # Should only keep last 5 transitions
        assert len(buffer) == 5
    
    def test_is_ready(self, buffer):
        """Test buffer readiness check."""
        assert not buffer.is_ready()
        
        # Add enough transitions
        for i in range(buffer.batch_size):
            transition = Transition(
                observation=torch.randn(4),
                action=i,
                reward=1.0,
                next_observation=torch.randn(4),
                done=False
            )
            buffer.add(transition)
        
        assert buffer.is_ready()


class TestHierarchicalReplayBuffer:
    """Test hierarchical replay buffer."""
    
    @pytest.fixture
    def config(self):
        return {
            "batch_size": 16,
            "num_options": 6,
            "store_option_trajectories": True
        }
    
    @pytest.fixture
    def hrl_buffer(self, config):
        return HierarchicalReplayBuffer(capacity=100, config=config)
    
    def test_hierarchical_buffer_initialization(self, hrl_buffer, config):
        """Test hierarchical buffer initialization."""
        assert hrl_buffer.capacity == 100
        assert hrl_buffer.num_options == config["num_options"]
        assert hrl_buffer.store_option_trajectories == config["store_option_trajectories"]
    
    def test_add_hierarchical_transition(self, hrl_buffer):
        """Test adding hierarchical transitions."""
        transition = HierarchicalTransition(
            observation=torch.randn(4),
            action=1,
            reward=0.5,
            next_observation=torch.randn(4),
            done=False,
            option=2,
            option_duration=3,
            termination=False,
            option_reward=1.0
        )
        
        hrl_buffer.add(transition)
        
        assert len(hrl_buffer) == 1
    
    def test_sample_action_level(self, hrl_buffer):
        """Test sampling action-level transitions."""
        # Add transitions
        for i in range(20):
            transition = HierarchicalTransition(
                observation=torch.randn(4),
                action=i % 4,
                reward=float(i),
                next_observation=torch.randn(4),
                done=False,
                option=i % hrl_buffer.num_options,
                option_duration=i + 1,
                termination=i % 10 == 0,
                option_reward=float(i) * 2
            )
            hrl_buffer.add(transition)
        
        # Sample action-level batch
        batch = hrl_buffer.sample(batch_size=8, level="action")
        
        # Check hierarchical fields
        assert "options" in batch
        assert "option_durations" in batch
        assert "terminations" in batch
        assert "option_rewards" in batch
        
        assert batch["options"].shape == (8,)
        assert batch["option_durations"].shape == (8,)
        assert batch["terminations"].shape == (8,)
        assert batch["option_rewards"].shape == (8,)
    
    def test_sample_by_option(self, hrl_buffer):
        """Test sampling transitions for specific option."""
        target_option = 2
        
        # Add transitions with different options
        for i in range(20):
            transition = HierarchicalTransition(
                observation=torch.randn(4),
                action=i % 4,
                reward=float(i),
                next_observation=torch.randn(4),
                done=False,
                option=i % hrl_buffer.num_options,
                option_duration=i + 1,
                termination=False,
                option_reward=float(i)
            )
            hrl_buffer.add(transition)
        
        # Sample transitions for specific option
        batch = hrl_buffer.sample_by_option(target_option, batch_size=4)
        
        if len(batch) > 0:  # Only check if we have transitions for this option
            # All sampled transitions should have the target option
            assert torch.all(batch["options"] == target_option)


class TestPrioritizedReplayBuffer:
    """Test prioritized replay buffer."""
    
    @pytest.fixture
    def config(self):
        return {
            "batch_size": 8,
            "priority_alpha": 0.6,
            "priority_beta": 0.4,
            "priority_epsilon": 1e-6
        }
    
    @pytest.fixture
    def priority_buffer(self, config):
        return PrioritizedReplayBuffer(capacity=50, config=config)
    
    def test_priority_buffer_initialization(self, priority_buffer, config):
        """Test priority buffer initialization."""
        assert priority_buffer.alpha == config["priority_alpha"]
        assert priority_buffer.beta == config["priority_beta"]
        assert priority_buffer.epsilon == config["priority_epsilon"]
    
    def test_add_with_priority(self, priority_buffer):
        """Test adding transitions with priorities."""
        transition = Transition(
            observation=torch.randn(4),
            action=1,
            reward=0.5,
            next_observation=torch.randn(4),
            done=False
        )
        
        priority = 2.0
        priority_buffer.add(transition, priority=priority)
        
        assert len(priority_buffer) == 1
        assert priority_buffer.priorities[0] == priority
    
    def test_prioritized_sampling_weights(self, priority_buffer):
        """Test prioritized sampling with importance weights."""
        # Add transitions with different priorities
        priorities = [1.0, 2.0, 0.5, 3.0, 1.5]
        for i, priority in enumerate(priorities):
            transition = Transition(
                observation=torch.randn(4),
                action=i,
                reward=float(i),
                next_observation=torch.randn(4),
                done=False
            )
            priority_buffer.add(transition, priority=priority)
        
        # Sample with priorities
        batch, indices, weights = priority_buffer.sample(batch_size=3)
        
        assert isinstance(batch, dict)
        assert len(indices) == 3
        assert len(weights) == 3
        
        # Weights should be positive
        assert np.all(weights > 0)
        
        # Indices should be valid
        assert np.all(indices < len(priority_buffer))
    
    def test_priority_update(self, priority_buffer):
        """Test updating priorities."""
        # Add some transitions
        for i in range(5):
            transition = Transition(
                observation=torch.randn(4),
                action=i,
                reward=float(i),
                next_observation=torch.randn(4),
                done=False
            )
            priority_buffer.add(transition, priority=1.0)
        
        # Update priorities
        indices = np.array([0, 2, 4])
        new_priorities = np.array([5.0, 3.0, 2.0])
        
        priority_buffer.update_priorities(indices, new_priorities)
        
        # Check that priorities were updated
        assert priority_buffer.priorities[0] > 1.0  # Should include epsilon
        assert priority_buffer.priorities[2] > 1.0
        assert priority_buffer.priorities[4] > 1.0


class TestMultiAgentReplayBuffer:
    """Test multi-agent replay buffer."""
    
    @pytest.fixture
    def config(self):
        return {
            "batch_size": 8,
            "centralized": True
        }
    
    @pytest.fixture
    def ma_buffer(self, config):
        return MultiAgentReplayBuffer(capacity=50, num_agents=3, config=config)
    
    def test_multi_agent_buffer_initialization(self, ma_buffer, config):
        """Test multi-agent buffer initialization."""
        assert ma_buffer.num_agents == 3
        assert ma_buffer.centralized == config["centralized"]
    
    def test_centralized_storage(self, ma_buffer):
        """Test centralized multi-agent storage."""
        from src.models.replay_buffer import MultiAgentTransition
        
        transition = MultiAgentTransition(
            observations={
                "agent_0": torch.randn(4),
                "agent_1": torch.randn(4),
                "agent_2": torch.randn(4)
            },
            actions={
                "agent_0": 1,
                "agent_1": 2,
                "agent_2": 0
            },
            rewards={
                "agent_0": 1.0,
                "agent_1": 0.5,
                "agent_2": -0.1
            },
            next_observations={
                "agent_0": torch.randn(4),
                "agent_1": torch.randn(4),
                "agent_2": torch.randn(4)
            },
            dones={
                "agent_0": False,
                "agent_1": False,
                "agent_2": True
            },
            agent_masks={}
        )
        
        ma_buffer.add(transition)
        
        assert len(ma_buffer) == 1
    
    def test_decentralized_storage(self, config):
        """Test decentralized multi-agent storage."""
        config["centralized"] = False
        ma_buffer = MultiAgentReplayBuffer(capacity=50, num_agents=3, config=config)
        
        from src.models.replay_buffer import MultiAgentTransition
        
        transition = MultiAgentTransition(
            observations={
                "agent_0": torch.randn(4),
                "agent_1": torch.randn(4),
                "agent_2": torch.randn(4)
            },
            actions={
                "agent_0": 1,
                "agent_1": 2,
                "agent_2": 0
            },
            rewards={
                "agent_0": 1.0,
                "agent_1": 0.5,
                "agent_2": -0.1
            },
            next_observations={
                "agent_0": torch.randn(4),
                "agent_1": torch.randn(4),
                "agent_2": torch.randn(4)
            },
            dones={
                "agent_0": False,
                "agent_1": False,
                "agent_2": True
            },
            agent_masks={}
        )
        
        ma_buffer.add(transition)
        
        # Should have added to individual agent buffers
        assert len(ma_buffer) == 3  # Total across all agent buffers


def test_mask_creation():
    """Test utility functions for mask creation."""
    # Test causal mask
    seq_len = 5
    device = torch.device("cpu")
    causal_mask = create_causal_mask(seq_len, device)
    
    assert causal_mask.shape == (seq_len, seq_len)
    assert torch.equal(causal_mask, torch.tril(torch.ones(seq_len, seq_len)))
    
    # Test padding mask
    lengths = torch.tensor([3, 5, 2, 4])
    max_len = 5
    padding_mask = create_padding_mask(lengths, max_len)
    
    assert padding_mask.shape == (4, 5)
    # First sequence should have mask [True, True, True, False, False]
    expected_first = torch.tensor([True, True, True, False, False])
    assert torch.equal(padding_mask[0], expected_first)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])