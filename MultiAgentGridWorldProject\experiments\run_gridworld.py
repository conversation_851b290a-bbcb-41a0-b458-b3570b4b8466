"""
GridWorld Experiment Runner

Entry point for running hierarchical multi-agent RL experiments on GridWorld environments.
Supports full configuration management, reproducible seeding, and comprehensive logging.

Usage:
    python experiments/run_gridworld.py
    python experiments/run_gridworld.py agent.num_options=8 training.lr=0.0005
    python experiments/run_gridworld.py multi_agent=true num_agents=4
    python experiments/run_gridworld.py seed=42 experiment_name=my_experiment
"""

import os
import sys
import logging
import hydra
from omegaconf import DictConfig, OmegaConf
from pathlib import Path
import torch
import numpy as np
import random

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.training.runner import TrainingRunner
from src.environment.grid_world import GridWorldEnv
from src.controllers.hierarchical_agent import HierarchicalAgent

logger = logging.getLogger(__name__)


def set_seed(seed: int):
    """Set random seeds for reproducibility."""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
    
    # Ensure deterministic behavior
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    logger.info(f"Set random seed to {seed}")


def create_environment(env_config: DictConfig) -> GridWorldEnv:
    """Create GridWorld environment from config."""
    config_dict = OmegaConf.to_container(env_config, resolve=True)
    env = GridWorldEnv(config_dict)
    logger.info(f"Created GridWorld environment: {config_dict}")
    return env


def setup_experiment_config(cfg: DictConfig) -> dict:
    """Setup complete experiment configuration."""
    # Convert to regular dict for easier manipulation
    config = OmegaConf.to_container(cfg, resolve=True)
    
    # Setup results directory
    results_dir = Path(config.get("results_dir", "results"))
    experiment_name = config.get("experiment_name", "gridworld_hrl")
    
    # Add timestamp if not in config
    if "timestamp" not in config:
        import time
        config["timestamp"] = int(time.time())
    
    # Update paths
    config["results_dir"] = str(results_dir)
    config["experiment_name"] = experiment_name
    
    # Ensure required sections exist (use existing sections from Hydra)
    if "training" not in config:
        config["training"] = {}
    if "agent" not in config:
        config["agent"] = {}
    if "environment" not in config:
        config["environment"] = config.get("env", {})
    
    return config


@hydra.main(config_path="../configs", config_name="gridworld_default", version_base="1.3")
def main(cfg: DictConfig) -> None:
    """
    Main experiment runner for GridWorld hierarchical RL.
    
    Args:
        cfg: Hydra configuration object
    """
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    logger.info("Starting GridWorld Hierarchical RL Experiment")
    logger.info(f"Configuration:\n{OmegaConf.to_yaml(cfg)}")
    
    # Set random seed for reproducibility
    seed = cfg.get("seed", 42)
    set_seed(seed)
    
    # Setup experiment configuration
    config = setup_experiment_config(cfg)
    
    try:
        # Initialize training runner
        logger.info("Initializing training runner...")
        runner = TrainingRunner(config)
        
        # Setup environment
        logger.info("Setting up environment...")
        env_config = config["environment"].copy()
        # Override environment settings with experiment settings
        if "num_agents" in config:
            env_config["num_agents"] = config["num_agents"]
        runner.setup_environment(env_config)
        
        # Setup agents
        logger.info("Setting up agents...")
        agent_config = config["agent"].copy()
        if config.get("multi_agent", False):
            agent_config["num_agents"] = config.get("num_agents", 2)
        
        # Set observation dimension based on environment
        # For GridWorld with partial observation, the obs dim is typically around 64
        if "policy" not in agent_config:
            agent_config["policy"] = {}
        agent_config["policy"]["obs_dim"] = 64  # Match GridWorld observation size
        agent_config["policy"]["action_dim"] = 5  # GridWorld has 5 actions (up, down, left, right, stay)
        
        runner.setup_agents(agent_config)
        
        # Setup training
        logger.info("Setting up training components...")
        training_config = config["training"]
        training_config["horizon"] = config.get("horizon", 2048)
        runner.setup_training(training_config)
        
        # Run training
        logger.info("Starting training...")
        results = runner.train()
        
        # Log final results
        logger.info("Training completed successfully!")
        logger.info(f"Final results: {results['final_evaluation']}")
        logger.info(f"Best reward: {results['best_reward']:.3f}")
        logger.info(f"Results saved to: {runner.run_dir}")
        
        # Print summary for CLI users
        print("\n" + "="*60)
        print("🎉 EXPERIMENT COMPLETED SUCCESSFULLY!")
        print("="*60)
        print(f"📊 Best Reward: {results['best_reward']:.3f}")
        print(f"⏱️  Training Time: {results['training_time']:.2f}s")
        print(f"🔢 Total Steps: {results['global_steps']:,}")
        print(f"📁 Results: {runner.run_dir}")
        print("="*60)
        
        return results
        
    except Exception as e:
        logger.error(f"Experiment failed: {str(e)}")
        logger.exception("Full traceback:")
        raise


if __name__ == "__main__":
    main()
