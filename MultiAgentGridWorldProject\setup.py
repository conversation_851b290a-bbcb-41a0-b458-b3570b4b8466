#!/usr/bin/env python3
"""
SD-HRL: Spatially-Distributed Hierarchical Reinforcement Learning
A complete multi-agent framework for real-world coordination

Setup configuration for package installation and distribution.
"""

from setuptools import setup, find_packages
import os
import sys

# Ensure Python 3.9+
if sys.version_info < (3, 9):
    raise RuntimeError("SD-HRL requires Python 3.9 or higher")

# Read long description from README
def read_long_description():
    """Read the long description from README.md"""
    here = os.path.abspath(os.path.dirname(__file__))
    readme_path = os.path.join(here, "README.md")
    
    if os.path.exists(readme_path):
        with open(readme_path, "r", encoding="utf-8") as f:
            return f.read()
    return "SD-HRL: Spatially-Distributed Hierarchical Reinforcement Learning Framework"

# Read requirements from requirements.txt
def read_requirements():
    """Read requirements from requirements.txt"""
    here = os.path.abspath(os.path.dirname(__file__))
    req_path = os.path.join(here, "requirements.txt")
    
    requirements = []
    if os.path.exists(req_path):
        with open(req_path, "r", encoding="utf-8") as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#"):
                    requirements.append(line)
    
    return requirements

# Core requirements (always needed)
CORE_REQUIREMENTS = [
    "torch>=1.12.0",
    "numpy>=1.21.0",
    "matplotlib>=3.5.0",
    "seaborn>=0.11.0",
    "pandas>=1.3.0",
    "pyyaml>=6.0",
    "hydra-core>=1.1.0",
    "tensorboard>=2.8.0",
    "tqdm>=4.62.0",
    "scipy>=1.7.0",
    "scikit-learn>=1.0.0",
    "gymnasium>=0.26.0",
    "pygame>=2.1.0",  # For visualization
]

# Optional requirements for extended functionality
EXTRA_REQUIREMENTS = {
    "smac": [
        "pysc2>=3.0.0",
        "smac>=1.0.0",
    ],
    "mpe": [
        "pettingzoo[mpe]>=1.18.0",
    ],
    "wandb": [
        "wandb>=0.12.0",
    ],
    "dev": [
        "pytest>=7.0.0",
        "pytest-cov>=3.0.0",
        "black>=22.0.0",
        "flake8>=4.0.0",
        "mypy>=0.950",
        "pre-commit>=2.17.0",
    ],
    "docs": [
        "sphinx>=4.5.0",
        "sphinx-rtd-theme>=1.0.0",
        "myst-parser>=0.17.0",
    ],
    "benchmarks": [
        "ray[tune]>=2.0.0",
        "optuna>=3.0.0",
        "plotly>=5.0.0",
    ],
    "realworld": [
        "cityflow>=1.0.0",  # Traffic simulation
        "pandapower>=2.8.0",  # Power grid simulation
    ]
}

# All optional requirements combined
EXTRA_REQUIREMENTS["all"] = [
    req for reqs in EXTRA_REQUIREMENTS.values() for req in reqs
]

setup(
    # Basic package information
    name="sdhrl",
    version="1.0.0",
    author="SD-HRL Research Team",
    author_email="<EMAIL>",
    description="Spatially-Distributed Hierarchical Reinforcement Learning Framework",
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-username/MultiAgentGridWorldProject",
    
    # Package configuration
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.9",
    
    # Dependencies
    install_requires=CORE_REQUIREMENTS,
    extras_require=EXTRA_REQUIREMENTS,
    
    # Package metadata
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: Science/Research",
        "Intended Audience :: Developers",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Software Development :: Libraries :: Python Modules",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Operating System :: OS Independent",
    ],
    
    # Keywords for discovery
    keywords=[
        "reinforcement-learning",
        "multi-agent",
        "hierarchical-rl",
        "attention-mechanism",
        "coordination",
        "distributed-systems",
        "machine-learning",
        "artificial-intelligence",
        "robotics",
        "smart-grids",
        "traffic-control",
    ],
    
    # Entry points for command-line tools
    entry_points={
        "console_scripts": [
            "sdhrl-train=experiments.run_gridworld:main",
            "sdhrl-benchmark=benchmarks.compare_results:main",
            "sdhrl-realworld=benchmarks.realworld_case:main",
        ],
    },
    
    # Include additional files
    include_package_data=True,
    package_data={
        "": [
            "configs/*.yaml",
            "assets/checkpoints/*.pth",
            "visualizations/*.gif",
            "visualizations/*.png",
        ],
    },
    
    # Project URLs
    project_urls={
        "Documentation": "https://github.com/your-username/MultiAgentGridWorldProject/docs",
        "Bug Reports": "https://github.com/your-username/MultiAgentGridWorldProject/issues",
        "Source": "https://github.com/your-username/MultiAgentGridWorldProject",
        "Research Paper": "https://github.com/your-username/MultiAgentGridWorldProject/BENCHMARK_RESULTS.md",
    },
    
    # Additional metadata
    zip_safe=False,
    platforms=["any"],
    
    # Test configuration
    test_suite="tests",
    tests_require=EXTRA_REQUIREMENTS["dev"],
    
    # Minimum versions for critical dependencies
    setup_requires=[
        "setuptools>=45",
        "wheel>=0.36",
    ],
)

# Post-installation message
def print_installation_success():
    """Print success message after installation"""
    print("\n" + "="*60)
    print("🎉 SD-HRL Installation Complete!")
    print("="*60)
    print("📚 Quick Start:")
    print("  sdhrl-train                    # Train SD-HRL system")
    print("  sdhrl-benchmark                # Run benchmark comparisons")  
    print("  sdhrl-realworld --env cityflow # Real-world case study")
    print("\n📖 Documentation:")
    print("  README.md                      # Complete usage guide")
    print("  BENCHMARK_RESULTS.md           # Research results")
    print("  docs/architecture.md           # Technical details")
    print("\n🧪 Testing:")
    print("  pytest tests/                  # Run test suite")
    print("  python experiments/run_gridworld.py  # Quick demo")
    print("="*60)
    print("✅ Ready for research and deployment!")
    print("="*60 + "\n")

if __name__ == "__main__":
    # This runs when setup.py is executed directly
    print_installation_success()