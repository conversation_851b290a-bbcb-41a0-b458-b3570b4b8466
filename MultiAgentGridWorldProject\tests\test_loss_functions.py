"""
Loss Functions and Backpropagation Tests

Tests loss computation, gradient flow, and optimization for all models.
Ensures no NaNs in gradients and proper backpropagation.
"""

import pytest
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, Any, List

from src.models.option_selector import OptionSelector
from src.models.attention_module import AttentionModule, MultiHeadAttention
from src.models.critic_network import CriticNetwork, HierarchicalCritic
from src.policies.hierarchical_policy import HierarchicalPolicy
from src.comms.graph_attention import MultiAgentCommunicationModule


class TestLossFunctionsAndGradients:
    """Test loss functions and gradient flow."""
    
    def test_option_selector_loss_and_gradients(self):
        """Test option selector loss computation and gradient flow."""
        
        config = {
            "obs_dim": 64,
            "num_options": 6,
            "hidden_dim": 128,
            "num_layers": 2,
            "use_gumbel_softmax": True,
            "gumbel_temperature": 1.0
        }
        
        selector = OptionSelector(config)
        optimizer = torch.optim.Adam(selector.parameters(), lr=1e-3)
        
        batch_size = 16
        observations = torch.randn(batch_size, config["obs_dim"])
        target_options = torch.randint(0, config["num_options"], (batch_size,))
        
        # Forward pass
        output = selector(observations)
        option_logits = output["option_logits"]
        
        # Compute cross-entropy loss
        loss = nn.CrossEntropyLoss()(option_logits, target_options)
        
        # Check loss is finite
        assert torch.isfinite(loss), "Loss contains NaN or Inf"
        assert loss.item() > 0, "Loss should be positive"
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        
        # Check gradients
        gradient_norms = []
        param_count = 0
        
        for name, param in selector.named_parameters():
            param_count += 1
            if param.grad is not None:
                # Check for NaN gradients
                assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in {name}"
                
                # Check gradient magnitude
                grad_norm = param.grad.norm().item()
                gradient_norms.append(grad_norm)
                
                assert grad_norm < 100.0, f"Gradient explosion in {name}: {grad_norm}"
                assert grad_norm > 1e-8, f"Vanishing gradients in {name}: {grad_norm}"
        
        assert len(gradient_norms) > 0, "No gradients computed"
        assert param_count > 0, "No parameters found"
        
        # Update parameters
        optimizer.step()
        
        # Verify parameters updated and remain finite
        for name, param in selector.named_parameters():
            assert torch.isfinite(param).all(), f"NaN/Inf parameters in {name} after update"
        
        # Test that loss can decrease
        output_after = selector(observations)
        loss_after = nn.CrossEntropyLoss()(output_after["option_logits"], target_options)
        
        # Loss should generally decrease or stay similar (allowing some variance)
        assert loss_after.item() <= loss.item() + 0.5, "Loss increased significantly after update"
    
    def test_critic_network_loss_and_gradients(self):
        """Test critic network loss computation and gradient flow."""
        
        config = {
            "obs_dim": 64,
            "action_dim": 4,
            "hidden_dim": 128,
            "num_layers": 2,
            "critic_type": "action_value",
            "num_critics": 2  # Ensemble
        }
        
        critic = CriticNetwork(config)
        optimizer = torch.optim.Adam(critic.parameters(), lr=1e-3)
        
        batch_size = 16
        observations = torch.randn(batch_size, config["obs_dim"])
        actions = torch.randn(batch_size, config["action_dim"])
        targets = torch.randn(batch_size)
        
        # Forward pass
        output = critic(observations, actions)
        values = output["values"]  # [batch_size, num_critics]
        
        # Compute loss for each critic
        losses = []
        for i in range(values.shape[1]):
            loss = critic.compute_loss(values[:, i], targets, "mse")
            losses.append(loss)
        
        total_loss = torch.stack(losses).mean()
        
        # Check loss is finite
        assert torch.isfinite(total_loss), "Total loss contains NaN or Inf"
        assert total_loss.item() >= 0, "MSE loss should be non-negative"
        
        # Backward pass
        optimizer.zero_grad()
        total_loss.backward()
        
        # Check gradients
        gradient_norms = []
        for name, param in critic.named_parameters():
            if param.grad is not None:
                # Check for NaN gradients
                assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in {name}"
                
                # Check gradient magnitude
                grad_norm = param.grad.norm().item()
                gradient_norms.append(grad_norm)
                
                assert grad_norm < 100.0, f"Gradient explosion in {name}: {grad_norm}"
        
        assert len(gradient_norms) > 0, "No gradients computed"
        
        # Update parameters
        optimizer.step()
        
        # Verify parameters remain finite
        for name, param in critic.named_parameters():
            assert torch.isfinite(param).all(), f"NaN/Inf parameters in {name} after update"
    
    def test_hierarchical_critic_loss_and_gradients(self):
        """Test hierarchical critic loss computation and gradient flow."""
        
        config = {
            "obs_dim": 64,
            "action_dim": 4,
            "num_options": 6,
            "hidden_dim": 128,
            "num_option_critics": 1,
            "num_action_critics": 2
        }
        
        hierarchical_critic = HierarchicalCritic(config)
        optimizer = torch.optim.Adam(hierarchical_critic.parameters(), lr=1e-3)
        
        batch_size = 16
        observations = torch.randn(batch_size, config["obs_dim"])
        actions = torch.randn(batch_size, config["action_dim"])
        options = torch.randint(0, config["num_options"], (batch_size,))
        
        option_returns = torch.randn(batch_size)
        action_returns = torch.randn(batch_size)
        
        # Compute losses
        option_loss = hierarchical_critic.compute_option_loss(observations, option_returns)
        action_loss = hierarchical_critic.compute_action_loss(observations, actions, options, action_returns)
        
        total_loss = option_loss + action_loss
        
        # Check losses are finite
        assert torch.isfinite(option_loss), "Option loss contains NaN or Inf"
        assert torch.isfinite(action_loss), "Action loss contains NaN or Inf"
        assert torch.isfinite(total_loss), "Total loss contains NaN or Inf"
        
        # Backward pass
        optimizer.zero_grad()
        total_loss.backward()
        
        # Check gradients in both critics
        option_gradients = []
        action_gradients = []
        
        for name, param in hierarchical_critic.option_critic.named_parameters():
            if param.grad is not None:
                assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in option_critic.{name}"
                option_gradients.append(param.grad.norm().item())
        
        for name, param in hierarchical_critic.action_critic.named_parameters():
            if param.grad is not None:
                assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in action_critic.{name}"
                action_gradients.append(param.grad.norm().item())
        
        assert len(option_gradients) > 0, "No gradients in option critic"
        assert len(action_gradients) > 0, "No gradients in action critic"
        
        # Update parameters
        optimizer.step()
        
        # Verify parameters remain finite
        for name, param in hierarchical_critic.named_parameters():
            assert torch.isfinite(param).all(), f"NaN/Inf parameters in {name} after update"
    
    def test_attention_module_loss_and_gradients(self):
        """Test attention module loss computation and gradient flow."""
        
        config = {
            "d_model": 256,
            "num_heads": 8,
            "d_ff": 1024,
            "dropout": 0.1,
            "use_residual": True,
            "use_layer_norm": True,
            "use_ffn": True
        }
        
        attention = AttentionModule(config)
        optimizer = torch.optim.Adam(attention.parameters(), lr=1e-3)
        
        batch_size, seq_len = 8, 16
        x = torch.randn(batch_size, seq_len, config["d_model"])
        target = torch.randn(batch_size, seq_len, config["d_model"])
        
        # Forward pass
        output, attn_weights = attention(x, return_attention=True)
        
        # Compute MSE loss
        loss = nn.MSELoss()(output, target)
        
        # Check loss is finite
        assert torch.isfinite(loss), "Loss contains NaN or Inf"
        assert loss.item() >= 0, "MSE loss should be non-negative"
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        
        # Check gradients
        gradient_norms = []
        for name, param in attention.named_parameters():
            if param.grad is not None:
                assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in {name}"
                
                grad_norm = param.grad.norm().item()
                gradient_norms.append(grad_norm)
                
                assert grad_norm < 100.0, f"Gradient explosion in {name}: {grad_norm}"
        
        assert len(gradient_norms) > 0, "No gradients computed"
        
        # Update parameters
        optimizer.step()
        
        # Verify parameters remain finite
        for name, param in attention.named_parameters():
            assert torch.isfinite(param).all(), f"NaN/Inf parameters in {name} after update"
    
    def test_communication_module_loss_and_gradients(self):
        """Test communication module loss computation and gradient flow."""
        
        config = {
            "num_agents": 3,
            "communication_dim": 64,
            "d_model": 64,
            "num_heads": 4,
            "comm_hidden_dim": 128,
            "attention_heads": 4,
            "comm_layers": 2
        }
        
        comm_module = MultiAgentCommunicationModule(config)
        optimizer = torch.optim.Adam(comm_module.parameters(), lr=1e-3)
        
        # Create sample data
        agent_features = {
            "agent_0": torch.randn(64, requires_grad=True),
            "agent_1": torch.randn(64, requires_grad=True),
            "agent_2": torch.randn(64, requires_grad=True)
        }
        
        agent_positions = {
            "agent_0": torch.tensor([0.0, 0.0]),
            "agent_1": torch.tensor([1.0, 1.0]),
            "agent_2": torch.tensor([2.0, 2.0])
        }
        
        communication_mask = {
            "agent_0": True,
            "agent_1": True,
            "agent_2": False  # One agent muted
        }
        
        # Forward pass
        updated_features = comm_module(agent_features, agent_positions, communication_mask)
        
        # Create target features
        target_features = {
            agent_id: torch.randn_like(features)
            for agent_id, features in updated_features.items()
        }
        
        # Compute loss
        loss = 0
        for agent_id in updated_features.keys():
            loss += nn.MSELoss()(updated_features[agent_id], target_features[agent_id])
        
        # Check loss is finite
        assert torch.isfinite(loss), "Loss contains NaN or Inf"
        assert loss.item() >= 0, "MSE loss should be non-negative"
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        
        # Check gradients
        gradient_norms = []
        for name, param in comm_module.named_parameters():
            if param.grad is not None:
                assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in {name}"
                
                grad_norm = param.grad.norm().item()
                gradient_norms.append(grad_norm)
                
                assert grad_norm < 100.0, f"Gradient explosion in {name}: {grad_norm}"
        
        assert len(gradient_norms) > 0, "No gradients computed"
        
        # Update parameters
        optimizer.step()
        
        # Verify parameters remain finite
        for name, param in comm_module.named_parameters():
            assert torch.isfinite(param).all(), f"NaN/Inf parameters in {name} after update"
    
    def test_hierarchical_policy_end_to_end_gradients(self):
        """Test end-to-end gradient flow through hierarchical policy."""
        
        config = {
            "num_options": 4,
            "obs_dim": 64,
            "action_dim": 4,
            "hidden_dim": 128,
            "num_layers": 2
        }
        
        policy = HierarchicalPolicy(config)
        
        # Create optimizers for different components
        option_optimizer = torch.optim.Adam(policy.option_policy.parameters(), lr=1e-3)
        worker_optimizer = torch.optim.Adam(policy.worker_policy.parameters(), lr=1e-3)
        termination_optimizer = torch.optim.Adam(policy.termination_fn.parameters(), lr=1e-3)
        
        batch_size = 16
        observations = torch.randn(batch_size, config["obs_dim"])
        actions = torch.randint(0, config["action_dim"], (batch_size,))
        options = torch.randint(0, config["num_options"], (batch_size,))
        rewards = torch.randn(batch_size)
        
        # Forward pass through all components
        option_output = policy.option_policy(observations)
        worker_output = policy.worker_policy(observations, options)
        termination_output = policy.termination_fn(observations, options)
        
        # Compute losses
        option_loss = nn.CrossEntropyLoss()(option_output["option_logits"], options)
        
        # Worker policy loss (policy gradient style)
        action_log_probs = worker_output["action_log_probs"]
        worker_loss = -(action_log_probs * rewards).mean()
        
        # Termination loss
        termination_targets = torch.randint(0, 2, (batch_size,)).float()
        termination_loss = nn.BCELoss()(termination_output["termination_probs"], termination_targets)
        
        # Check all losses are finite
        assert torch.isfinite(option_loss), "Option loss contains NaN or Inf"
        assert torch.isfinite(worker_loss), "Worker loss contains NaN or Inf"
        assert torch.isfinite(termination_loss), "Termination loss contains NaN or Inf"
        
        # Backward passes for each component
        components = [
            (option_optimizer, option_loss, policy.option_policy, "option_policy"),
            (worker_optimizer, worker_loss, policy.worker_policy, "worker_policy"),
            (termination_optimizer, termination_loss, policy.termination_fn, "termination_fn")
        ]
        
        for optimizer, loss, component, comp_name in components:
            optimizer.zero_grad()
            loss.backward(retain_graph=True)
            
            # Check gradients
            gradient_norms = []
            for name, param in component.named_parameters():
                if param.grad is not None:
                    assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in {comp_name}.{name}"
                    
                    grad_norm = param.grad.norm().item()
                    gradient_norms.append(grad_norm)
                    
                    assert grad_norm < 100.0, f"Gradient explosion in {comp_name}.{name}: {grad_norm}"
                    assert grad_norm > 1e-8, f"Vanishing gradients in {comp_name}.{name}: {grad_norm}"
            
            assert len(gradient_norms) > 0, f"No gradients in {comp_name}"
            
            # Update parameters
            optimizer.step()
            
            # Verify parameters remain finite
            for name, param in component.named_parameters():
                assert torch.isfinite(param).all(), f"NaN/Inf parameters in {comp_name}.{name} after update"
    
    def test_gradient_clipping(self):
        """Test gradient clipping functionality."""
        
        config = {
            "obs_dim": 64,
            "num_options": 6,
            "hidden_dim": 128,
            "num_layers": 3
        }
        
        selector = OptionSelector(config)
        optimizer = torch.optim.Adam(selector.parameters(), lr=1e-2)  # Higher LR to potentially cause issues
        
        batch_size = 32
        observations = torch.randn(batch_size, config["obs_dim"])
        target_options = torch.randint(0, config["num_options"], (batch_size,))
        
        # Create a loss that might cause large gradients
        output = selector(observations)
        loss = nn.CrossEntropyLoss()(output["option_logits"], target_options) * 100  # Scale up loss
        
        # Backward pass
        optimizer.zero_grad()
        loss.backward()
        
        # Check gradients before clipping
        grad_norms_before = []
        for param in selector.parameters():
            if param.grad is not None:
                grad_norms_before.append(param.grad.norm().item())
        
        # Apply gradient clipping
        max_grad_norm = 1.0
        torch.nn.utils.clip_grad_norm_(selector.parameters(), max_grad_norm)
        
        # Check gradients after clipping
        total_norm = 0
        for param in selector.parameters():
            if param.grad is not None:
                param_norm = param.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        total_norm = total_norm ** (1. / 2)
        
        # Total norm should be <= max_grad_norm (allowing for small numerical errors)
        assert total_norm <= max_grad_norm + 1e-6, f"Gradient clipping failed: {total_norm} > {max_grad_norm}"
        
        # Update parameters
        optimizer.step()
        
        # Verify parameters remain finite
        for param in selector.parameters():
            assert torch.isfinite(param).all(), "NaN/Inf parameters after gradient clipping"
    
    def test_different_loss_types(self):
        """Test different loss types for critic networks."""
        
        config = {
            "obs_dim": 64,
            "action_dim": 4,
            "hidden_dim": 128,
            "critic_type": "action_value"
        }
        
        critic = CriticNetwork(config)
        
        batch_size = 16
        observations = torch.randn(batch_size, config["obs_dim"])
        actions = torch.randn(batch_size, config["action_dim"])
        targets = torch.randn(batch_size)
        
        # Forward pass
        output = critic(observations, actions)
        predictions = output["values"].squeeze(-1)  # Remove critic dimension
        
        # Test different loss types
        loss_types = ["mse", "huber", "mae"]
        
        for loss_type in loss_types:
            # Create fresh forward pass for each loss type
            output = critic(observations, actions)
            predictions = output["values"].squeeze(-1)
            
            loss = critic.compute_loss(predictions, targets, loss_type)
            
            # Check loss is finite and non-negative
            assert torch.isfinite(loss), f"{loss_type} loss contains NaN or Inf"
            assert loss.item() >= 0, f"{loss_type} loss should be non-negative"
            
            # Test backward pass
            critic.zero_grad()
            loss.backward()
            
            # Check gradients
            for name, param in critic.named_parameters():
                if param.grad is not None:
                    assert torch.isfinite(param.grad).all(), f"NaN/Inf gradients in {name} with {loss_type} loss"


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])