#!/usr/bin/env python3
"""
Simple test to verify the utility modules are working
"""

import numpy as np
import matplotlib.pyplot as plt
import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_attention_viz():
    """Test attention visualization utilities"""
    print("🔍 Testing Attention Visualization...")
    
    try:
        from src.utils.attention_viz import AttentionVisualizer
        
        # Create test data
        n_agents = 4
        attention_matrix = np.random.rand(n_agents, n_agents)
        attention_matrix = attention_matrix / attention_matrix.sum(axis=1, keepdims=True)  # Normalize
        
        visualizer = AttentionVisualizer()
        
        # Test attention heatmap
        print("  ✓ Testing attention heatmap...")
        fig = visualizer.plot_attention_heatmap(attention_matrix)
        plt.close()
        
        print("  ✅ Attention visualization test PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Attention visualization test FAILED: {e}")
        return False

def test_metrics():
    """Test metrics collection utilities"""
    print("📊 Testing Metrics Collection...")
    
    try:
        from src.utils.metrics import MetricsCollector
        
        collector = MetricsCollector()
        
        # Test episode tracking
        print("  ✓ Testing episode tracking...")
        agent_ids = ['agent_0', 'agent_1']
        collector.start_episode(0, agent_ids)
        
        # Add some metrics
        for step in range(10):
            observations = {'agent_0': np.random.randn(4), 'agent_1': np.random.randn(4)}
            actions = {'agent_0': np.random.randint(0, 4), 'agent_1': np.random.randint(0, 4)}
            rewards = {'agent_0': np.random.randn(), 'agent_1': np.random.randn()}
            dones = {'agent_0': False, 'agent_1': False}
            info = {'positions': {'agent_0': (step, step), 'agent_1': (step+1, step+1)}}

            collector.record_step(step, observations, actions, rewards, dones, info)

        episode_metrics = collector.end_episode(success=True)
        
        print("  ✅ Metrics collection test PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Metrics collection test FAILED: {e}")
        return False

def test_visualization():
    """Test environment visualization utilities"""
    print("🎨 Testing Environment Visualization...")
    
    try:
        from src.utils.visualization import GridWorldVisualizer
        
        # Create test environment
        grid_size = (8, 8)
        visualizer = GridWorldVisualizer(grid_size)
        
        # Test basic grid rendering
        print("  ✓ Testing basic grid rendering...")
        grid = np.zeros(grid_size)
        grid[2:4, 2:4] = 1  # Add some obstacles
        
        agent_positions = {'agent_0': (1, 1), 'agent_1': (6, 6)}
        goals = [(7, 7), (0, 0)]
        
        frame = visualizer.render_frame(grid, agent_positions, goals)
        
        print("  ✅ Environment visualization test PASSED!")
        return True
        
    except Exception as e:
        print(f"  ❌ Environment visualization test FAILED: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Simple SD-HRL Utility Tests")
    print("=" * 50)
    
    tests = [
        test_attention_viz,
        test_metrics,
        test_visualization
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print("🏁 Test Results Summary")
    print("=" * 50)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED! ({passed}/{total})")
        print("\n🎉 SD-HRL utility modules are working!")
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
