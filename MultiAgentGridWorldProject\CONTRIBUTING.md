# 🤝 Contributing to SD-HRL

Thank you for your interest in contributing to the SD-HRL (Spatially-Distributed Hierarchical Reinforcement Learning) framework! This document provides guidelines for contributing to the project.

## 🎯 **How to Contribute**

### **🐛 Bug Reports**
- Use the [GitHub Issues](https://github.com/your-username/MultiAgentGridWorldProject/issues) page
- Include a clear description of the problem
- Provide steps to reproduce the issue
- Include system information (OS, Python version, dependencies)
- Add relevant error messages and stack traces

### **✨ Feature Requests**
- Open an issue with the "enhancement" label
- Describe the proposed feature and its use case
- Explain how it fits with the project's goals
- Consider implementation complexity and maintenance burden

### **🔧 Code Contributions**
1. **Fork** the repository
2. **Create** a feature branch (`git checkout -b feature/amazing-feature`)
3. **Make** your changes following our coding standards
4. **Add** tests for new functionality
5. **Update** documentation as needed
6. **Commit** your changes (`git commit -m 'Add amazing feature'`)
7. **Push** to the branch (`git push origin feature/amazing-feature`)
8. **Open** a Pull Request

## 📋 **Development Setup**

### **Environment Setup**
```bash
# Clone your fork
git clone https://github.com/your-username/MultiAgentGridWorldProject.git
cd MultiAgentGridWorldProject

# Create development environment
conda env create -f environment.yml
conda activate sdhrl

# Install in development mode
pip install -e ".[dev,all]"

# Install pre-commit hooks
pre-commit install
```

### **Running Tests**
```bash
# Run all tests
pytest tests/

# Run with coverage
pytest tests/ --cov=src --cov-report=html

# Run specific test categories
pytest tests/test_environment.py -v
pytest tests/test_policies.py -v
pytest tests/test_training_system.py -v
```

### **Code Quality**
```bash
# Format code
black src/ tests/ benchmarks/ experiments/

# Check style
flake8 src/ tests/ benchmarks/ experiments/

# Type checking
mypy src/
```

## 🎨 **Coding Standards**

### **Python Style**
- Follow [PEP 8](https://pep8.org/) style guidelines
- Use [Black](https://black.readthedocs.io/) for code formatting
- Maximum line length: 88 characters (Black default)
- Use type hints for all function signatures
- Write docstrings for all public functions and classes

### **Code Organization**
```python
"""
Module docstring explaining the purpose and main components.
"""

import standard_library
import third_party_packages
import local_modules

from typing import Dict, List, Optional, Tuple
import torch
import numpy as np

from src.environment.base_env import BaseMultiAgentEnv


class ExampleClass:
    """
    Class docstring with clear description.
    
    Args:
        param1: Description of parameter
        param2: Description of parameter
        
    Attributes:
        attr1: Description of attribute
        attr2: Description of attribute
    """
    
    def __init__(self, param1: int, param2: str):
        self.attr1 = param1
        self.attr2 = param2
    
    def public_method(self, arg: float) -> bool:
        """
        Method docstring with clear description.
        
        Args:
            arg: Description of argument
            
        Returns:
            Description of return value
            
        Raises:
            ValueError: When invalid argument provided
        """
        if arg < 0:
            raise ValueError("Argument must be non-negative")
        return True
    
    def _private_method(self) -> None:
        """Private method for internal use only."""
        pass
```

### **Testing Standards**
- Write tests for all new functionality
- Aim for >80% code coverage
- Use descriptive test names
- Follow the Arrange-Act-Assert pattern
- Mock external dependencies

```python
def test_hierarchical_agent_option_selection():
    """Test that hierarchical agent selects valid options."""
    # Arrange
    config = {"num_options": 6, "option_freq": 8}
    agent = HierarchicalAgent("test_agent", config)
    observation = torch.randn(64)
    
    # Act
    action = agent.act(observation)
    
    # Assert
    assert "action" in action
    assert 0 <= action["action"] < agent.action_space.n
    assert agent.option_tracker.current_option is not None
```

## 📚 **Documentation**

### **Code Documentation**
- All public functions and classes must have docstrings
- Use Google-style docstrings
- Include type information in docstrings
- Provide usage examples for complex functions

### **README Updates**
- Update README.md for new features
- Add examples for new functionality
- Update installation instructions if needed
- Keep the feature list current

### **Architecture Documentation**
- Update `docs/architecture.md` for structural changes
- Add diagrams for new components
- Document design decisions and trade-offs

## 🧪 **Adding New Features**

### **New Environments**
1. Inherit from `BaseMultiAgentEnv`
2. Implement required abstract methods
3. Add configuration file in `configs/`
4. Create adapter in `src/environment/`
5. Add tests in `tests/test_environment.py`
6. Update documentation

### **New Algorithms**
1. Follow the existing policy structure
2. Add to appropriate module in `src/policies/`
3. Implement required interfaces
4. Add comprehensive tests
5. Include in benchmark comparisons
6. Document algorithm details

### **New Benchmarks**
1. Add to `benchmarks/` directory
2. Follow existing naming conventions
3. Include statistical analysis
4. Generate publication-quality plots
5. Add to comparison suite
6. Document methodology

## 🔍 **Review Process**

### **Pull Request Guidelines**
- **Clear Title**: Descriptive title summarizing the change
- **Detailed Description**: Explain what, why, and how
- **Linked Issues**: Reference related issues
- **Testing**: Include test results and coverage
- **Documentation**: Update relevant documentation
- **Breaking Changes**: Clearly mark any breaking changes

### **Review Criteria**
- ✅ Code follows style guidelines
- ✅ Tests pass and coverage is maintained
- ✅ Documentation is updated
- ✅ No breaking changes without justification
- ✅ Performance impact is considered
- ✅ Security implications are addressed

## 🏷️ **Issue Labels**

- `bug`: Something isn't working
- `enhancement`: New feature or request
- `documentation`: Improvements or additions to documentation
- `good first issue`: Good for newcomers
- `help wanted`: Extra attention is needed
- `question`: Further information is requested
- `wontfix`: This will not be worked on

## 🎯 **Priority Areas**

We especially welcome contributions in these areas:

### **High Priority**
- 🐛 Bug fixes and stability improvements
- 📊 Additional baseline algorithm implementations
- 🌍 New real-world environment adapters
- 🧪 Extended test coverage
- 📚 Documentation improvements

### **Medium Priority**
- ✨ New visualization tools
- 🔧 Performance optimizations
- 🎮 Additional game environments
- 📈 Advanced metrics and analysis tools

### **Research Extensions**
- 🧠 New hierarchical learning algorithms
- 🌐 Advanced communication mechanisms
- 🔄 Meta-learning capabilities
- 🎯 Transfer learning features

## 📞 **Getting Help**

- 💬 **Discussions**: Use [GitHub Discussions](https://github.com/your-username/MultiAgentGridWorldProject/discussions) for questions
- 🐛 **Issues**: Report bugs via [GitHub Issues](https://github.com/your-username/MultiAgentGridWorldProject/issues)
- 📧 **Email**: Contact maintainers for complex questions
- 📖 **Documentation**: Check existing docs first

## 🙏 **Recognition**

Contributors will be:
- Listed in the project's contributors section
- Acknowledged in research papers (for significant contributions)
- Invited to co-author publications (for major features)
- Given commit access (for regular contributors)

## 📜 **Code of Conduct**

This project follows a standard code of conduct:
- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different viewpoints and experiences
- Show empathy towards other community members

## 🎉 **Thank You!**

Your contributions help make SD-HRL a better framework for the entire research community. Whether you're fixing a typo, adding a feature, or improving documentation, every contribution matters!

---

**Happy Contributing!** 🚀