"""
Performance Metrics and Evaluation Tools

This module provides comprehensive metrics collection and analysis tools
for evaluating multi-agent reinforcement learning systems.
"""

import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Optional, Tuple, Any, Union
import logging
from collections import defaultdict, deque
from pathlib import Path
import json
import yaml
from dataclasses import dataclass, asdict
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns

logger = logging.getLogger(__name__)


@dataclass
class EpisodeMetrics:
    """Container for episode-level metrics."""
    episode_id: int
    total_reward: float
    episode_length: int
    success: bool
    collision_count: int
    deadlock_count: int
    communication_count: int
    option_switches: int
    goal_reached_time: Optional[int] = None
    cooperation_events: int = 0
    exploration_coverage: float = 0.0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


@dataclass
class AgentMetrics:
    """Container for agent-specific metrics."""
    agent_id: str
    total_reward: float
    success_rate: float
    average_episode_length: float
    collision_rate: float
    deadlock_rate: float
    communication_efficiency: float
    option_diversity: float
    exploration_efficiency: float
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return asdict(self)


class MetricsCollector:
    """
    Comprehensive metrics collection and analysis system.
    
    Collects, processes, and analyzes performance metrics for multi-agent
    reinforcement learning experiments.
    """
    
    def __init__(self, save_dir: str = "results/metrics"):
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        # Episode-level metrics
        self.episode_metrics: List[EpisodeMetrics] = []
        
        # Agent-level metrics
        self.agent_metrics: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Real-time tracking
        self.current_episode_data = {}
        self.step_rewards = []
        self.step_actions = []
        self.step_observations = []
        
        # Communication tracking
        self.communication_history = []
        self.attention_weights = []
        
        # Option tracking (for hierarchical agents)
        self.option_history = defaultdict(list)
        self.option_durations = defaultdict(list)
        
        # Performance tracking
        self.training_curves = {
            'episode_rewards': [],
            'success_rates': [],
            'episode_lengths': [],
            'communication_efficiency': [],
            'option_diversity': []
        }
        
    def start_episode(self, episode_id: int, agents: List[str]):
        """Initialize tracking for a new episode."""
        self.current_episode_data = {
            'episode_id': episode_id,
            'agents': agents,
            'start_time': 0,
            'step_count': 0,
            'total_reward': 0.0,
            'agent_rewards': {agent: 0.0 for agent in agents},
            'collisions': 0,
            'deadlocks': 0,
            'communications': 0,
            'option_switches': {agent: 0 for agent in agents},
            'goals_reached': set(),
            'cooperation_events': 0,
            'visited_positions': set()
        }
        
        # Clear step-level data
        self.step_rewards.clear()
        self.step_actions.clear()
        self.step_observations.clear()
        
    def record_step(
        self,
        step: int,
        observations: Dict[str, Any],
        actions: Dict[str, Any],
        rewards: Dict[str, float],
        dones: Dict[str, bool],
        info: Dict[str, Any]
    ):
        """Record data from a single environment step."""
        if not self.current_episode_data:
            logger.warning("No episode started. Call start_episode() first.")
            return
        
        # Update episode data
        self.current_episode_data['step_count'] = step
        
        # Record rewards
        step_total_reward = sum(rewards.values())
        self.current_episode_data['total_reward'] += step_total_reward
        self.step_rewards.append(step_total_reward)
        
        for agent, reward in rewards.items():
            self.current_episode_data['agent_rewards'][agent] += reward
        
        # Record actions and observations
        self.step_actions.append(actions.copy())
        self.step_observations.append(observations.copy())
        
        # Extract additional metrics from info
        if info:
            # Collision detection
            if 'collisions' in info:
                self.current_episode_data['collisions'] += info['collisions']
            
            # Deadlock detection
            if 'deadlocks' in info:
                self.current_episode_data['deadlocks'] += info['deadlocks']
            
            # Communication events
            if 'communications' in info:
                self.current_episode_data['communications'] += info['communications']
            
            # Goal achievements
            if 'goals_reached' in info:
                self.current_episode_data['goals_reached'].update(info['goals_reached'])
            
            # Cooperation events
            if 'cooperation_events' in info:
                self.current_episode_data['cooperation_events'] += info['cooperation_events']
            
            # Position tracking for exploration
            if 'positions' in info:
                for pos in info['positions'].values():
                    if isinstance(pos, (list, tuple)) and len(pos) == 2:
                        self.current_episode_data['visited_positions'].add(tuple(pos))
            
            # Option switches (for hierarchical agents)
            if 'option_switches' in info:
                for agent, switches in info['option_switches'].items():
                    self.current_episode_data['option_switches'][agent] += switches
            
            # Attention weights
            if 'attention_weights' in info:
                self.attention_weights.append(info['attention_weights'])
    
    def end_episode(self, success: bool = False) -> EpisodeMetrics:
        """Finalize episode and compute metrics."""
        if not self.current_episode_data:
            logger.warning("No episode data to finalize.")
            return None
        
        # Create episode metrics
        episode_metrics = EpisodeMetrics(
            episode_id=self.current_episode_data['episode_id'],
            total_reward=self.current_episode_data['total_reward'],
            episode_length=self.current_episode_data['step_count'],
            success=success,
            collision_count=self.current_episode_data['collisions'],
            deadlock_count=self.current_episode_data['deadlocks'],
            communication_count=self.current_episode_data['communications'],
            option_switches=sum(self.current_episode_data['option_switches'].values()),
            cooperation_events=self.current_episode_data['cooperation_events'],
            exploration_coverage=len(self.current_episode_data['visited_positions'])
        )
        
        # Store episode metrics
        self.episode_metrics.append(episode_metrics)
        
        # Update training curves
        self.training_curves['episode_rewards'].append(episode_metrics.total_reward)
        self.training_curves['episode_lengths'].append(episode_metrics.episode_length)
        
        # Compute rolling success rate
        recent_episodes = self.episode_metrics[-100:]  # Last 100 episodes
        success_rate = sum(ep.success for ep in recent_episodes) / len(recent_episodes)
        self.training_curves['success_rates'].append(success_rate)
        
        # Compute communication efficiency
        if episode_metrics.episode_length > 0:
            comm_efficiency = 1.0 - (episode_metrics.communication_count / 
                                   (episode_metrics.episode_length * len(self.current_episode_data['agents'])))
            comm_efficiency = max(0.0, comm_efficiency)
        else:
            comm_efficiency = 0.0
        self.training_curves['communication_efficiency'].append(comm_efficiency)
        
        # Compute option diversity (if applicable)
        if self.current_episode_data['option_switches']:
            total_switches = sum(self.current_episode_data['option_switches'].values())
            option_diversity = min(1.0, total_switches / episode_metrics.episode_length)
        else:
            option_diversity = 0.0
        self.training_curves['option_diversity'].append(option_diversity)
        
        # Clear current episode data
        self.current_episode_data = {}
        
        return episode_metrics
    
    def compute_agent_metrics(self, agent_id: str) -> AgentMetrics:
        """Compute comprehensive metrics for a specific agent."""
        if not self.episode_metrics:
            logger.warning("No episode data available for agent metrics.")
            return None
        
        # Filter episodes for this agent
        agent_episodes = [ep for ep in self.episode_metrics 
                         if agent_id in self.current_episode_data.get('agents', [])]
        
        if not agent_episodes:
            # Use all episodes if agent filtering fails
            agent_episodes = self.episode_metrics
        
        # Compute basic metrics
        total_reward = sum(ep.total_reward for ep in agent_episodes)
        success_rate = sum(ep.success for ep in agent_episodes) / len(agent_episodes)
        avg_episode_length = np.mean([ep.episode_length for ep in agent_episodes])
        
        # Compute rates
        total_episodes = len(agent_episodes)
        collision_rate = sum(ep.collision_count for ep in agent_episodes) / total_episodes
        deadlock_rate = sum(ep.deadlock_count for ep in agent_episodes) / total_episodes
        
        # Communication efficiency
        total_steps = sum(ep.episode_length for ep in agent_episodes)
        total_communications = sum(ep.communication_count for ep in agent_episodes)
        communication_efficiency = 1.0 - (total_communications / max(1, total_steps))
        communication_efficiency = max(0.0, communication_efficiency)
        
        # Option diversity
        total_switches = sum(ep.option_switches for ep in agent_episodes)
        option_diversity = total_switches / max(1, total_steps)
        
        # Exploration efficiency
        total_coverage = sum(ep.exploration_coverage for ep in agent_episodes)
        exploration_efficiency = total_coverage / max(1, total_steps)
        
        return AgentMetrics(
            agent_id=agent_id,
            total_reward=total_reward,
            success_rate=success_rate,
            average_episode_length=avg_episode_length,
            collision_rate=collision_rate,
            deadlock_rate=deadlock_rate,
            communication_efficiency=communication_efficiency,
            option_diversity=option_diversity,
            exploration_efficiency=exploration_efficiency
        )
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """Compute comprehensive summary statistics."""
        if not self.episode_metrics:
            return {}
        
        # Basic statistics
        rewards = [ep.total_reward for ep in self.episode_metrics]
        lengths = [ep.episode_length for ep in self.episode_metrics]
        successes = [ep.success for ep in self.episode_metrics]
        
        summary = {
            'total_episodes': len(self.episode_metrics),
            'reward_stats': {
                'mean': np.mean(rewards),
                'std': np.std(rewards),
                'min': np.min(rewards),
                'max': np.max(rewards),
                'median': np.median(rewards)
            },
            'length_stats': {
                'mean': np.mean(lengths),
                'std': np.std(lengths),
                'min': np.min(lengths),
                'max': np.max(lengths),
                'median': np.median(lengths)
            },
            'success_rate': np.mean(successes),
            'collision_rate': np.mean([ep.collision_count for ep in self.episode_metrics]),
            'deadlock_rate': np.mean([ep.deadlock_count for ep in self.episode_metrics]),
            'communication_efficiency': np.mean(self.training_curves['communication_efficiency']),
            'option_diversity': np.mean(self.training_curves['option_diversity'])
        }
        
        # Recent performance (last 100 episodes)
        if len(self.episode_metrics) >= 100:
            recent_episodes = self.episode_metrics[-100:]
            recent_rewards = [ep.total_reward for ep in recent_episodes]
            recent_successes = [ep.success for ep in recent_episodes]
            
            summary['recent_performance'] = {
                'reward_mean': np.mean(recent_rewards),
                'reward_std': np.std(recent_rewards),
                'success_rate': np.mean(recent_successes)
            }
        
        return summary
    
    def plot_training_curves(self, save_path: Optional[str] = None) -> plt.Figure:
        """Plot comprehensive training curves."""
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        
        # Episode rewards
        if self.training_curves['episode_rewards']:
            axes[0, 0].plot(self.training_curves['episode_rewards'], alpha=0.7)
            # Add moving average
            window = min(100, len(self.training_curves['episode_rewards']) // 10)
            if window > 1:
                moving_avg = pd.Series(self.training_curves['episode_rewards']).rolling(window).mean()
                axes[0, 0].plot(moving_avg, color='red', linewidth=2, label=f'MA({window})')
                axes[0, 0].legend()
            axes[0, 0].set_title('Episode Rewards')
            axes[0, 0].set_xlabel('Episode')
            axes[0, 0].set_ylabel('Total Reward')
            axes[0, 0].grid(True, alpha=0.3)
        
        # Success rates
        if self.training_curves['success_rates']:
            axes[0, 1].plot(self.training_curves['success_rates'])
            axes[0, 1].set_title('Success Rate')
            axes[0, 1].set_xlabel('Episode')
            axes[0, 1].set_ylabel('Success Rate')
            axes[0, 1].set_ylim(0, 1)
            axes[0, 1].grid(True, alpha=0.3)
        
        # Episode lengths
        if self.training_curves['episode_lengths']:
            axes[0, 2].plot(self.training_curves['episode_lengths'], alpha=0.7)
            axes[0, 2].set_title('Episode Lengths')
            axes[0, 2].set_xlabel('Episode')
            axes[0, 2].set_ylabel('Steps')
            axes[0, 2].grid(True, alpha=0.3)
        
        # Communication efficiency
        if self.training_curves['communication_efficiency']:
            axes[1, 0].plot(self.training_curves['communication_efficiency'])
            axes[1, 0].set_title('Communication Efficiency')
            axes[1, 0].set_xlabel('Episode')
            axes[1, 0].set_ylabel('Efficiency')
            axes[1, 0].set_ylim(0, 1)
            axes[1, 0].grid(True, alpha=0.3)
        
        # Option diversity
        if self.training_curves['option_diversity']:
            axes[1, 1].plot(self.training_curves['option_diversity'])
            axes[1, 1].set_title('Option Diversity')
            axes[1, 1].set_xlabel('Episode')
            axes[1, 1].set_ylabel('Diversity')
            axes[1, 1].set_ylim(0, 1)
            axes[1, 1].grid(True, alpha=0.3)
        
        # Performance distribution
        if self.episode_metrics:
            rewards = [ep.total_reward for ep in self.episode_metrics]
            axes[1, 2].hist(rewards, bins=30, alpha=0.7, edgecolor='black')
            axes[1, 2].set_title('Reward Distribution')
            axes[1, 2].set_xlabel('Total Reward')
            axes[1, 2].set_ylabel('Frequency')
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            fig.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"Training curves saved to {save_path}")
        
        return fig
    
    def save_metrics(self, filename: str = "metrics.json"):
        """Save all collected metrics to file."""
        save_path = self.save_dir / filename
        
        # Prepare data for serialization
        data = {
            'episode_metrics': [ep.to_dict() for ep in self.episode_metrics],
            'training_curves': self.training_curves,
            'summary_statistics': self.get_summary_statistics(),
            'metadata': {
                'total_episodes': len(self.episode_metrics),
                'collection_timestamp': pd.Timestamp.now().isoformat()
            }
        }
        
        # Save as JSON
        with open(save_path, 'w') as f:
            json.dump(data, f, indent=2, default=str)
        
        logger.info(f"Metrics saved to {save_path}")
        
        # Also save as CSV for easy analysis
        if self.episode_metrics:
            csv_path = save_path.with_suffix('.csv')
            df = pd.DataFrame([ep.to_dict() for ep in self.episode_metrics])
            df.to_csv(csv_path, index=False)
            logger.info(f"Episode metrics CSV saved to {csv_path}")
    
    def load_metrics(self, filename: str = "metrics.json"):
        """Load metrics from file."""
        load_path = self.save_dir / filename
        
        if not load_path.exists():
            logger.warning(f"Metrics file not found: {load_path}")
            return
        
        with open(load_path, 'r') as f:
            data = json.load(f)
        
        # Restore episode metrics
        self.episode_metrics = [
            EpisodeMetrics(**ep_data) for ep_data in data['episode_metrics']
        ]
        
        # Restore training curves
        self.training_curves = data['training_curves']
        
        logger.info(f"Metrics loaded from {load_path}")


def compute_success_rate(episode_results: List[bool]) -> float:
    """Compute success rate from episode results."""
    if not episode_results:
        return 0.0
    return sum(episode_results) / len(episode_results)


def compute_communication_efficiency(
    communication_counts: List[int],
    episode_lengths: List[int],
    num_agents: int
) -> float:
    """Compute communication efficiency metric."""
    if not communication_counts or not episode_lengths:
        return 0.0
    
    total_communications = sum(communication_counts)
    total_possible = sum(episode_lengths) * num_agents
    
    if total_possible == 0:
        return 0.0
    
    efficiency = 1.0 - (total_communications / total_possible)
    return max(0.0, efficiency)


def statistical_significance_test(
    group1: List[float],
    group2: List[float],
    alpha: float = 0.05
) -> Dict[str, Any]:
    """
    Perform statistical significance test between two groups.
    
    Args:
        group1: First group of values
        group2: Second group of values
        alpha: Significance level
        
    Returns:
        Dictionary with test results
    """
    if len(group1) < 2 or len(group2) < 2:
        return {'error': 'Insufficient data for statistical test'}
    
    # Perform t-test
    t_stat, p_value = stats.ttest_ind(group1, group2)
    
    # Effect size (Cohen's d)
    pooled_std = np.sqrt(((len(group1) - 1) * np.var(group1, ddof=1) + 
                         (len(group2) - 1) * np.var(group2, ddof=1)) / 
                        (len(group1) + len(group2) - 2))
    
    cohens_d = (np.mean(group1) - np.mean(group2)) / pooled_std if pooled_std > 0 else 0
    
    return {
        't_statistic': t_stat,
        'p_value': p_value,
        'significant': p_value < alpha,
        'cohens_d': cohens_d,
        'effect_size': 'small' if abs(cohens_d) < 0.5 else 'medium' if abs(cohens_d) < 0.8 else 'large',
        'group1_mean': np.mean(group1),
        'group1_std': np.std(group1),
        'group2_mean': np.mean(group2),
        'group2_std': np.std(group2)
    }


if __name__ == "__main__":
    # Example usage and testing
    collector = MetricsCollector()
    
    # Simulate some episodes
    agents = ['agent_0', 'agent_1', 'agent_2']
    
    for episode in range(10):
        collector.start_episode(episode, agents)
        
        # Simulate episode steps
        for step in range(np.random.randint(50, 200)):
            observations = {agent: np.random.randn(64) for agent in agents}
            actions = {agent: np.random.randint(0, 5) for agent in agents}
            rewards = {agent: np.random.randn() for agent in agents}
            dones = {agent: False for agent in agents}
            
            info = {
                'collisions': np.random.randint(0, 2),
                'communications': np.random.randint(0, 3),
                'positions': {agent: np.random.randint(0, 10, 2).tolist() for agent in agents}
            }
            
            collector.record_step(step, observations, actions, rewards, dones, info)
        
        # End episode
        success = np.random.random() > 0.5
        collector.end_episode(success)
    
    # Generate summary
    summary = collector.get_summary_statistics()
    print("Summary Statistics:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # Plot training curves
    fig = collector.plot_training_curves("training_curves.png")
    
    # Save metrics
    collector.save_metrics("test_metrics.json")
    
    print("Metrics collection test completed successfully!")